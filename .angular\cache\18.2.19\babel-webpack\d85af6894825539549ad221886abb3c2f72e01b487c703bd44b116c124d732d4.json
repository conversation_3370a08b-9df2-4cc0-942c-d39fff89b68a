{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  appearance;\n  constructor() {\n    const config = inject(MAT_CARD_CONFIG, {\n      optional: true\n    });\n    this.appearance = config?.appearance || 'raised';\n  }\n  static ɵfac = function MatCard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCard)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCard,\n    selectors: [[\"mat-card\"]],\n    hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n    hostVars: 8,\n    hostBindings: function MatCard_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\")(\"mat-mdc-card-filled\", ctx.appearance === \"filled\")(\"mdc-card--filled\", ctx.appearance === \"filled\");\n      }\n    },\n    inputs: {\n      appearance: \"appearance\"\n    },\n    exportAs: [\"matCard\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatCard_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-elevated-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mat-card-outlined-container-color, var(--mat-sys-surface));border-radius:var(--mat-card-outlined-container-shape, var(--mat-sys-corner-medium));border-width:var(--mat-card-outlined-outline-width, 1px);border-color:var(--mat-card-outlined-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mat-card-outlined-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mat-mdc-card-filled{background-color:var(--mat-card-filled-container-color, var(--mat-sys-surface-container-highest));border-radius:var(--mat-card-filled-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-filled-container-elevation, var(--mat-sys-level0))}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n        '[class.mat-mdc-card-filled]': 'appearance === \"filled\"',\n        '[class.mdc-card--filled]': 'appearance === \"filled\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-elevated-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mat-card-outlined-container-color, var(--mat-sys-surface));border-radius:var(--mat-card-outlined-container-shape, var(--mat-sys-corner-medium));border-width:var(--mat-card-outlined-outline-width, 1px);border-color:var(--mat-card-outlined-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mat-card-outlined-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mat-mdc-card-filled{background-color:var(--mat-card-filled-container-color, var(--mat-sys-surface-container-highest));border-radius:var(--mat-card-filled-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-filled-container-elevation, var(--mat-sys-level0))}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"]\n    }]\n  }], () => [], {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n  static ɵfac = function MatCardTitle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardTitle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardTitle,\n    selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-title\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n  static ɵfac = function MatCardTitleGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardTitleGroup)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCardTitleGroup,\n    selectors: [[\"mat-card-title-group\"]],\n    hostAttrs: [1, \"mat-mdc-card-title-group\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c2,\n    decls: 4,\n    vars: 0,\n    template: function MatCardTitleGroup_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelementStart(0, \"div\");\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵprojection(3, 2);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n  static ɵfac = function MatCardContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardContent,\n    selectors: [[\"mat-card-content\"]],\n    hostAttrs: [1, \"mat-mdc-card-content\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n  static ɵfac = function MatCardSubtitle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardSubtitle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardSubtitle,\n    selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-subtitle\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n  // as to not conflict with the native `align` attribute.\n  /** Position of the actions inside the card. */\n  align = 'start';\n  static ɵfac = function MatCardActions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardActions)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardActions,\n    selectors: [[\"mat-card-actions\"]],\n    hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n    hostVars: 2,\n    hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n      }\n    },\n    inputs: {\n      align: \"align\"\n    },\n    exportAs: [\"matCardActions\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      }\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n  static ɵfac = function MatCardHeader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardHeader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCardHeader,\n    selectors: [[\"mat-card-header\"]],\n    hostAttrs: [1, \"mat-mdc-card-header\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c4,\n    decls: 4,\n    vars: 0,\n    consts: [[1, \"mat-mdc-card-header-text\"]],\n    template: function MatCardHeader_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 0);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵprojection(3, 2);\n      }\n    },\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n  static ɵfac = function MatCardFooter_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardFooter)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardFooter,\n    selectors: [[\"mat-card-footer\"]],\n    hostAttrs: [1, \"mat-mdc-card-footer\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      }\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n  static ɵfac = function MatCardImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardImage,\n    selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n  static ɵfac = function MatCardSmImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardSmImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardSmImage,\n    selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n  static ɵfac = function MatCardMdImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardMdImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardMdImage,\n    selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n  static ɵfac = function MatCardLgImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardLgImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardLgImage,\n    selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n  static ɵfac = function MatCardXlImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardXlImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardXlImage,\n    selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n  static ɵfac = function MatCardAvatar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardAvatar)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCardAvatar,\n    selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-card-avatar\"],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      }\n    }]\n  }], null, null);\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {\n  static ɵfac = function MatCardModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCardModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCardModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, ...CARD_DIRECTIVES],\n      exports: [CARD_DIRECTIVES, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Directive", "NgModule", "M", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "_c4", "MAT_CARD_CONFIG", "MatCard", "appearance", "constructor", "config", "optional", "ɵfac", "MatCard_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatCard_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "MatCard_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "MatCardTitle", "MatCardTitle_Factory", "ɵdir", "ɵɵdefineDirective", "MatCardTitleGroup", "MatCardTitleGroup_Factory", "MatCardTitleGroup_Template", "ɵɵelementStart", "ɵɵelementEnd", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardContent_Factory", "MatCardSubtitle", "MatCardSubtitle_Factory", "MatCardActions", "align", "MatCardActions_Factory", "MatCardActions_HostBindings", "MatCardHeader", "MatCardHeader_Factory", "consts", "MatCardHeader_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCardFooter_Factory", "MatCardImage", "MatCardImage_Factory", "MatCardSmImage", "MatCardSmImage_Factory", "MatCardMdImage", "MatCardMdImage_Factory", "MatCardLgImage", "MatCardLgImage_Factory", "MatCardXlImage", "MatCardXlImage_Factory", "MatCardAvatar", "MatCardAvatar_Factory", "CARD_DIRECTIVES", "MatCardModule", "MatCardModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/material/fesm2022/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n    appearance;\n    constructor() {\n        const config = inject(MAT_CARD_CONFIG, { optional: true });\n        this.appearance = config?.appearance || 'raised';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCard, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCard, isStandalone: true, selector: \"mat-card\", inputs: { appearance: \"appearance\" }, host: { properties: { \"class.mat-mdc-card-outlined\": \"appearance === \\\"outlined\\\"\", \"class.mdc-card--outlined\": \"appearance === \\\"outlined\\\"\", \"class.mat-mdc-card-filled\": \"appearance === \\\"filled\\\"\", \"class.mdc-card--filled\": \"appearance === \\\"filled\\\"\" }, classAttribute: \"mat-mdc-card mdc-card\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n\", styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-elevated-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mat-card-outlined-container-color, var(--mat-sys-surface));border-radius:var(--mat-card-outlined-container-shape, var(--mat-sys-corner-medium));border-width:var(--mat-card-outlined-outline-width, 1px);border-color:var(--mat-card-outlined-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mat-card-outlined-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mat-mdc-card-filled{background-color:var(--mat-card-filled-container-color, var(--mat-sys-surface-container-highest));border-radius:var(--mat-card-filled-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-filled-container-elevation, var(--mat-sys-level0))}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', host: {\n                        'class': 'mat-mdc-card mdc-card',\n                        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n                        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n                        '[class.mat-mdc-card-filled]': 'appearance === \"filled\"',\n                        '[class.mdc-card--filled]': 'appearance === \"filled\"',\n                    }, exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n\", styles: [\".mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-color:var(--mat-card-elevated-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-elevated-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:\\\"\\\";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mat-card-elevated-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mat-card-outlined-container-color, var(--mat-sys-surface));border-radius:var(--mat-card-outlined-container-shape, var(--mat-sys-corner-medium));border-width:var(--mat-card-outlined-outline-width, 1px);border-color:var(--mat-card-outlined-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mat-card-outlined-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mat-mdc-card-filled{background-color:var(--mat-card-filled-container-color, var(--mat-sys-surface-container-highest));border-radius:var(--mat-card-filled-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mat-card-filled-container-elevation, var(--mat-sys-level0))}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { appearance: [{\n                type: Input\n            }] } });\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardTitle, isStandalone: true, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-mdc-card-title\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: { 'class': 'mat-mdc-card-title' },\n                }]\n        }] });\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardTitleGroup, isStandalone: true, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-mdc-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-title-group' }, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardContent, isStandalone: true, selector: \"mat-card-content\", host: { classAttribute: \"mat-mdc-card-content\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content',\n                    host: { 'class': 'mat-mdc-card-content' },\n                }]\n        }] });\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardSubtitle, isStandalone: true, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-mdc-card-subtitle\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: { 'class': 'mat-mdc-card-subtitle' },\n                }]\n        }] });\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    align = 'start';\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardActions, isStandalone: true, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-card-actions mdc-card__actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-mdc-card-actions mdc-card__actions',\n                        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n                    },\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardHeader, isStandalone: true, selector: \"mat-card-header\", host: { classAttribute: \"mat-mdc-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-header' }, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardFooter, isStandalone: true, selector: \"mat-card-footer\", host: { classAttribute: \"mat-mdc-card-footer\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-mdc-card-footer' },\n                }]\n        }] });\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardImage, isStandalone: true, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-mdc-card-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-mdc-card-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardSmImage, isStandalone: true, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-mdc-card-sm-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-mdc-card-sm-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardMdImage, isStandalone: true, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-mdc-card-md-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-mdc-card-md-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardLgImage, isStandalone: true, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-mdc-card-lg-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-mdc-card-lg-image mdc-card__media' },\n                }]\n        }] });\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardXlImage, isStandalone: true, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-mdc-card-xl-image mdc-card__media\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-mdc-card-xl-image mdc-card__media' },\n                }]\n        }] });\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: MatCardAvatar, isStandalone: true, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-mdc-card-avatar\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-mdc-card-avatar' },\n                }]\n        }] });\n\nconst CARD_DIRECTIVES = [\n    MatCard,\n    MatCardActions,\n    MatCardAvatar,\n    MatCardContent,\n    MatCardFooter,\n    MatCardHeader,\n    MatCardImage,\n    MatCardLgImage,\n    MatCardMdImage,\n    MatCardSmImage,\n    MatCardSubtitle,\n    MatCardTitle,\n    MatCardTitleGroup,\n    MatCardXlImage,\n];\nclass MatCardModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage], exports: [MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage, MatCommonModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, ...CARD_DIRECTIVES],\n                    exports: [CARD_DIRECTIVES, MatCommonModule],\n                }]\n        }] });\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzI,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,mBAAmB;AAC1B,OAAO,mBAAmB;;AAE1B;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,eAAe,GAAG,IAAIf,cAAc,CAAC,iBAAiB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,OAAO,CAAC;EACVC,UAAU;EACVC,WAAWA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAGlB,MAAM,CAACc,eAAe,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC1D,IAAI,CAACH,UAAU,GAAGE,MAAM,EAAEF,UAAU,IAAI,QAAQ;EACpD;EACA,OAAOI,IAAI,YAAAC,gBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFP,OAAO;EAAA;EAC1G,OAAOQ,IAAI,kBAD8EzB,EAAE,CAAA0B,iBAAA;IAAAC,IAAA,EACJV,OAAO;IAAAW,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADLjC,EAAE,CAAAmC,WAAA,0BAAAD,GAAA,CAAAhB,UAAA,KACW,UAAT,CAAC,uBAAAgB,GAAA,CAAAhB,UAAA,KAAQ,UAAT,CAAC,wBAAAgB,GAAA,CAAAhB,UAAA,KAAQ,QAAT,CAAC,qBAAAgB,GAAA,CAAAhB,UAAA,KAAQ,QAAT,CAAC;MAAA;IAAA;IAAAkB,MAAA;MAAAlB,UAAA;IAAA;IAAAmB,QAAA;IAAAC,UAAA;IAAAC,QAAA,GADLvC,EAAE,CAAAwC,mBAAA;IAAAC,kBAAA,EAAA9B,GAAA;IAAA+B,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,iBAAAZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAA8C,eAAA;QAAF9C,EAAE,CAAA+C,YAAA,EAC0c,CAAC;MAAA;IAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC1iB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FnD,EAAE,CAAAoD,iBAAA,CAGJnC,OAAO,EAAc,CAAC;IACrGU,IAAI,EAAExB,SAAS;IACfkD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEC,IAAI,EAAE;QACzB,OAAO,EAAE,uBAAuB;QAChC,+BAA+B,EAAE,2BAA2B;QAC5D,4BAA4B,EAAE,2BAA2B;QACzD,6BAA6B,EAAE,yBAAyB;QACxD,0BAA0B,EAAE;MAChC,CAAC;MAAElB,QAAQ,EAAE,SAAS;MAAEY,aAAa,EAAE7C,iBAAiB,CAACoD,IAAI;MAAEN,eAAe,EAAE7C,uBAAuB,CAACoD,MAAM;MAAEb,QAAQ,EAAE,6BAA6B;MAAEI,MAAM,EAAE,CAAC,uwIAAuwI;IAAE,CAAC;EACx7I,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE9B,UAAU,EAAE,CAAC;MACrDS,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,YAAY,CAAC;EACf,OAAOpC,IAAI,YAAAqC,qBAAAnC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkC,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBAzB8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAyBJ+B,YAAY;IAAA9B,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACvG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA3B6FnD,EAAE,CAAAoD,iBAAA,CA2BJM,YAAY,EAAc,CAAC;IAC1G/B,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kDAAkD;MAC5DC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqB;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMO,iBAAiB,CAAC;EACpB,OAAOxC,IAAI,YAAAyC,0BAAAvC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsC,iBAAiB;EAAA;EACpH,OAAOrC,IAAI,kBAzC8EzB,EAAE,CAAA0B,iBAAA;IAAAC,IAAA,EAyCJmC,iBAAiB;IAAAlC,SAAA;IAAAC,SAAA;IAAAS,UAAA;IAAAC,QAAA,GAzCfvC,EAAE,CAAAwC,mBAAA;IAAAC,kBAAA,EAAA5B,GAAA;IAAA6B,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAoB,2BAAA/B,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAA8C,eAAA,CAAAlC,GAAA;QAAFZ,EAAE,CAAAiE,cAAA,SAyCwJ,CAAC;QAzC3JjE,EAAE,CAAA+C,YAAA,EAyCiU,CAAC;QAzCpU/C,EAAE,CAAAkE,YAAA,CAyCyU,CAAC;QAzC5UlE,EAAE,CAAA+C,YAAA,KAyC8oB,CAAC;QAzCjpB/C,EAAE,CAAA+C,YAAA,KAyCyqB,CAAC;MAAA;IAAA;IAAAE,aAAA;IAAAC,eAAA;EAAA;AACzwB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3C6FnD,EAAE,CAAAoD,iBAAA,CA2CJU,iBAAiB,EAAc,CAAC;IAC/GnC,IAAI,EAAExB,SAAS;IACfkD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEL,aAAa,EAAE7C,iBAAiB,CAACoD,IAAI;MAAEN,eAAe,EAAE7C,uBAAuB,CAACoD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAA2B,CAAC;MAAEX,QAAQ,EAAE;IAA2hB,CAAC;EAC5tB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,cAAc,CAAC;EACjB,OAAO7C,IAAI,YAAA8C,uBAAA5C,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2C,cAAc;EAAA;EACjH,OAAOP,IAAI,kBAxD8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAwDJwC,cAAc;IAAAvC,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA1D6FnD,EAAE,CAAAoD,iBAAA,CA0DJe,cAAc,EAAc,CAAC;IAC5GxC,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,eAAe,CAAC;EAClB,OAAO/C,IAAI,YAAAgD,wBAAA9C,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6C,eAAe;EAAA;EAClH,OAAOT,IAAI,kBA1E8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EA0EJ0C,eAAe;IAAAzC,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AAC1G;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA5E6FnD,EAAE,CAAAoD,iBAAA,CA4EJiB,eAAe,EAAc,CAAC;IAC7G1C,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2DAA2D;MACrEC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwB;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,cAAc,CAAC;EACjB;EACA;EACA;EACAC,KAAK,GAAG,OAAO;EACf,OAAOlD,IAAI,YAAAmD,uBAAAjD,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+C,cAAc;EAAA;EACjH,OAAOX,IAAI,kBAhG8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAgGJ4C,cAAc;IAAA3C,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA2C,4BAAAzC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhGZjC,EAAE,CAAAmC,WAAA,mCAAAD,GAAA,CAAAsC,KAAA,KAgGM,KAAG,CAAC;MAAA;IAAA;IAAApC,MAAA;MAAAoC,KAAA;IAAA;IAAAnC,QAAA;IAAAC,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAlG6FnD,EAAE,CAAAoD,iBAAA,CAkGJmB,cAAc,EAAc,CAAC;IAC5G5C,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BjB,QAAQ,EAAE,gBAAgB;MAC1BkB,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wCAAwC,EAAE;MAC9C;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEiB,KAAK,EAAE,CAAC;MACtB7C,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqE,aAAa,CAAC;EAChB,OAAOrD,IAAI,YAAAsD,sBAAApD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmD,aAAa;EAAA;EAChH,OAAOlD,IAAI,kBAzH8EzB,EAAE,CAAA0B,iBAAA;IAAAC,IAAA,EAyHJgD,aAAa;IAAA/C,SAAA;IAAAC,SAAA;IAAAS,UAAA;IAAAC,QAAA,GAzHXvC,EAAE,CAAAwC,mBAAA;IAAAC,kBAAA,EAAA1B,GAAA;IAAA2B,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAkC,uBAAA7C,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAA8C,eAAA,CAAAhC,GAAA;QAAFd,EAAE,CAAA+C,YAAA,EAyH4M,CAAC;QAzH/M/C,EAAE,CAAAiE,cAAA,YAyHsP,CAAC;QAzHzPjE,EAAE,CAAA+C,YAAA,KAyH+Z,CAAC;QAzHla/C,EAAE,CAAAkE,YAAA,CAyHua,CAAC;QAzH1alE,EAAE,CAAA+C,YAAA,KAyHkc,CAAC;MAAA;IAAA;IAAAE,aAAA;IAAAC,eAAA;EAAA;AACliB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3H6FnD,EAAE,CAAAoD,iBAAA,CA2HJuB,aAAa,EAAc,CAAC;IAC3GhD,IAAI,EAAExB,SAAS;IACfkD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEL,aAAa,EAAE7C,iBAAiB,CAACoD,IAAI;MAAEN,eAAe,EAAE7C,uBAAuB,CAACoD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MAAEX,QAAQ,EAAE;IAAkU,CAAC;EACzf,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,aAAa,CAAC;EAChB,OAAOzD,IAAI,YAAA0D,sBAAAxD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuD,aAAa;EAAA;EAChH,OAAOnB,IAAI,kBAxI8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAwIJoD,aAAa;IAAAnD,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACxG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA1I6FnD,EAAE,CAAAoD,iBAAA,CA0IJ2B,aAAa,EAAc,CAAC;IAC3GpD,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,YAAY,CAAC;EACf,OAAO3D,IAAI,YAAA4D,qBAAA1D,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyD,YAAY;EAAA;EAC/G,OAAOrB,IAAI,kBA9J8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EA8JJsD,YAAY;IAAArD,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACvG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAhK6FnD,EAAE,CAAAoD,iBAAA,CAgKJ6B,YAAY,EAAc,CAAC;IAC1GtD,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqC;IAC1D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM4B,cAAc,CAAC;EACjB,OAAO7D,IAAI,YAAA8D,uBAAA5D,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2D,cAAc;EAAA;EACjH,OAAOvB,IAAI,kBA1K8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EA0KJwD,cAAc;IAAAvD,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA5K6FnD,EAAE,CAAAoD,iBAAA,CA4KJ+B,cAAc,EAAc,CAAC;IAC5GxD,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM8B,cAAc,CAAC;EACjB,OAAO/D,IAAI,YAAAgE,uBAAA9D,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6D,cAAc;EAAA;EACjH,OAAOzB,IAAI,kBAtL8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAsLJ0D,cAAc;IAAAzD,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAxL6FnD,EAAE,CAAAoD,iBAAA,CAwLJiC,cAAc,EAAc,CAAC;IAC5G1D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMgC,cAAc,CAAC;EACjB,OAAOjE,IAAI,YAAAkE,uBAAAhE,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+D,cAAc;EAAA;EACjH,OAAO3B,IAAI,kBAlM8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAkMJ4D,cAAc;IAAA3D,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KApM6FnD,EAAE,CAAAoD,iBAAA,CAoMJmC,cAAc,EAAc,CAAC;IAC5G5D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMkC,cAAc,CAAC;EACjB,OAAOnE,IAAI,YAAAoE,uBAAAlE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiE,cAAc;EAAA;EACjH,OAAO7B,IAAI,kBA9M8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EA8MJ8D,cAAc;IAAA7D,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACzG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAhN6FnD,EAAE,CAAAoD,iBAAA,CAgNJqC,cAAc,EAAc,CAAC;IAC5G9D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,aAAa,CAAC;EAChB,OAAOrE,IAAI,YAAAsE,sBAAApE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmE,aAAa;EAAA;EAChH,OAAO/B,IAAI,kBAlO8E5D,EAAE,CAAA6D,iBAAA;IAAAlC,IAAA,EAkOJgE,aAAa;IAAA/D,SAAA;IAAAC,SAAA;IAAAS,UAAA;EAAA;AACxG;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KApO6FnD,EAAE,CAAAoD,iBAAA,CAoOJuC,aAAa,EAAc,CAAC;IAC3GhE,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB;IAC3C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsC,eAAe,GAAG,CACpB5E,OAAO,EACPsD,cAAc,EACdoB,aAAa,EACbxB,cAAc,EACdY,aAAa,EACbJ,aAAa,EACbM,YAAY,EACZM,cAAc,EACdF,cAAc,EACdF,cAAc,EACdd,eAAe,EACfX,YAAY,EACZI,iBAAiB,EACjB2B,cAAc,CACjB;AACD,MAAMK,aAAa,CAAC;EAChB,OAAOxE,IAAI,YAAAyE,sBAAAvE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsE,aAAa;EAAA;EAChH,OAAOE,IAAI,kBA9P8EhG,EAAE,CAAAiG,gBAAA;IAAAtE,IAAA,EA8PSmE;EAAa;EA2BjH,OAAOI,IAAI,kBAzR8ElG,EAAE,CAAAmG,gBAAA;IAAAC,OAAA,GAyRkC1F,eAAe,EAAEA,eAAe;EAAA;AACjK;AACA;EAAA,QAAAyC,SAAA,oBAAAA,SAAA,KA3R6FnD,EAAE,CAAAoD,iBAAA,CA2RJ0C,aAAa,EAAc,CAAC;IAC3GnE,IAAI,EAAEnB,QAAQ;IACd6C,IAAI,EAAE,CAAC;MACC+C,OAAO,EAAE,CAAC1F,eAAe,EAAE,GAAGmF,eAAe,CAAC;MAC9CQ,OAAO,EAAE,CAACR,eAAe,EAAEnF,eAAe;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASM,eAAe,EAAEC,OAAO,EAAEsD,cAAc,EAAEoB,aAAa,EAAExB,cAAc,EAAEY,aAAa,EAAEJ,aAAa,EAAEM,YAAY,EAAEM,cAAc,EAAEF,cAAc,EAAES,aAAa,EAAEX,cAAc,EAAEd,eAAe,EAAEX,YAAY,EAAEI,iBAAiB,EAAE2B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}