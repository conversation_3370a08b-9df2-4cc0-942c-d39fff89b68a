@use 'sass:map';
@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../../m2/palette' as m2-palette;

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, chip);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    disabled-container-opacity: 0.4,
    trailing-action-opacity: 0.54,
    trailing-action-focus-opacity: 1,
    trailing-action-state-layer-color: transparent,
    selected-trailing-action-state-layer-color: transparent,
    trailing-action-hover-state-layer-opacity: 0,
    trailing-action-focus-state-layer-opacity: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: null) {
  $foreground: null;
  @if $palette-name == null {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $grey-50: map.get(m2-palette.$grey-palette, 50);
    $grey-900: map.get(m2-palette.$grey-palette, 900);
    $foreground: if($is-dark, $grey-50, $grey-900);
  }
  @else {
    $foreground: inspection.get-theme-color($theme, $palette-name, default-contrast);
  }
  @return (
    // The color of the icon at the end of a disabled chip.
    selected-disabled-trailing-icon-color: $foreground,
    // The color of the icon at the end of the chip.
    selected-trailing-icon-color: $foreground
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
