@use 'sass:meta';
@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    icon-button-icon-size: 24px,
    icon-button-container-shape: 50%,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return (
    icon-button-disabled-icon-color: if($is-dark, rgba(#fff, 0.5), rgba(#000, 0.38)),
    icon-button-disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),
    icon-button-focus-state-layer-opacity: if($is-dark, 0.24, 0.12),
    icon-button-hover-state-layer-opacity: if($is-dark, 0.08, 0.04),
    icon-button-icon-color: inherit,
    icon-button-pressed-state-layer-opacity: if($is-dark, 0.24, 0.12),
    icon-button-ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),
    icon-button-state-layer-color: inspection.get-theme-color($theme, foreground, base),
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name);
  $ripple-opacity: 0.1;

  @return (
    icon-button-icon-color: inspection.get-theme-color($theme, $palette-name),
    icon-button-state-layer-color: $color,
    icon-button-ripple-color: if(
      meta.type-of($color) == color,
      rgba($color, $ripple-opacity),
      inspection.get-theme-color($theme, foreground, base, $ripple-opacity)),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme, $exclude: ()) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  $tokens: (
    icon-button-touch-target-display: if($scale < -1, none, block),
    icon-button-state-layer-size: map.get((
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
      -5: 28px,
    ), $scale)
  );

  @each $token in $exclude {
    $tokens: map.remove($tokens, $token);
  }

  @return $tokens;
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
