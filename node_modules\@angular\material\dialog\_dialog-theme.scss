@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/tokens/m2/mdc/dialog' as tokens-mdc-dialog;
@use '../core/tokens/m2/mat/dialog' as tokens-mat-dialog;
@use '../core/tokens/token-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-dialog.$prefix, tokens-mdc-dialog.get-unthemable-tokens());
      @include token-utils.create-token-values(
        tokens-mat-dialog.$prefix, tokens-mat-dialog.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-dialog.$prefix, tokens-mdc-dialog.get-color-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-dialog.$prefix, tokens-mat-dialog.get-color-tokens($theme));
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-dialog.$prefix, tokens-mdc-dialog.get-typography-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-dialog.$prefix, tokens-mat-dialog.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {}
}

@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values(
    $tokens,
    (prefix: tokens-mdc-dialog.$prefix, tokens: tokens-mdc-dialog.get-token-slots()),
    (prefix: tokens-mat-dialog.$prefix, tokens: tokens-mat-dialog.get-token-slots()),
  );
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-dialog') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  @if ($tokens != ()) {
    @include token-utils.create-token-values(
      tokens-mdc-dialog.$prefix, map.get($tokens, tokens-mdc-dialog.$prefix));
    @include token-utils.create-token-values(
      tokens-mat-dialog.$prefix, map.get($tokens, tokens-mat-dialog.$prefix));
  }
}
