{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/data.service\";\nexport class PulsedesignComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.matrixData = null;\n    this.loading = true;\n    this.displayedColumns = [];\n  }\n  ngOnInit() {\n    this.dataService.getMatrixData().subscribe({\n      next: data => {\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  setupDisplayedColumns() {\n    if (!this.matrixData) return;\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n  getMinimumValue(batchNo, testName, subTestName) {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n  getPpPpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];\n    return data ? `${data.Pp.toFixed(3)} | ${data.PpK.toFixed(3)}` : '';\n  }\n  getCpCpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];\n    return data ? `${data.Cp.toFixed(3)} | ${data.CpK.toFixed(3)}` : '';\n  }\n  getUnit(testName, subTestName) {\n    if (!this.matrixData) return '';\n    return this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName] || '';\n  }\n  static {\n    this.ɵfac = function PulsedesignComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PulsedesignComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PulsedesignComponent,\n      selectors: [[\"app-pulsedesign\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"pulse-container\"]],\n      template: function PulsedesignComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Welcome to pulse latest design\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule, MatTableModule, MatCardModule, MatProgressSpinnerModule, HttpClientModule],\n      styles: [\".pulse-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2.5rem;\\n  text-align: center;\\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\\n  margin: 0;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHVsc2VkZXNpZ24vcHVsc2VkZXNpZ24uY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYiw2REFBNkQ7QUFDL0Q7O0FBRUE7RUFDRSxZQUFZO0VBQ1osaUJBQWlCO0VBQ2pCLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMsU0FBUztBQUNYIiwic291cmNlc0NvbnRlbnQiOlsiLnB1bHNlLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBoZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xufVxuXG5oMSB7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgdGV4dC1zaGFkb3c6IDJweCAycHggNHB4IHJnYmEoMCwwLDAsMC4zKTtcbiAgbWFyZ2luOiAwO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTableModule", "MatCardModule", "MatProgressSpinnerModule", "HttpClientModule", "PulsedesignComponent", "constructor", "dataService", "matrixData", "loading", "displayedColumns", "ngOnInit", "getMatrixData", "subscribe", "next", "data", "setupDisplayedColumns", "error", "console", "testNames", "for<PERSON>ach", "testName", "subTestsByTest", "subTestName", "push", "getMinimumValue", "batchNo", "dataMatrix", "Minimum", "getPpPpKValue", "Object", "values", "Pp", "toFixed", "PpK", "getCpCpKValue", "Cp", "CpK", "getUnit", "unitsByTestAndSubTest", "i0", "ɵɵdirectiveInject", "i1", "DataService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PulsedesignComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.ts", "C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { HttpClientModule } from '@angular/common/http';\nimport { DataService, MatrixData, TestData } from '../services/data.service';\n\n@Component({\n  selector: 'app-pulsedesign',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTableModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    HttpClientModule\n  ],\n  templateUrl: './pulsedesign.component.html',\n  styleUrls: ['./pulsedesign.component.css']\n})\nexport class PulsedesignComponent implements OnInit {\n  matrixData: MatrixData | null = null;\n  loading = true;\n  displayedColumns: string[] = [];\n\n  constructor(private dataService: DataService) {}\n\n  ngOnInit() {\n    this.dataService.getMatrixData().subscribe({\n      next: (data) => {\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private setupDisplayedColumns() {\n    if (!this.matrixData) return;\n\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData!.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n\n  getMinimumValue(batchNo: string, testName: string, subTestName: string): number | null {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n\n  getPpPpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];\n    return data ? `${data.Pp.toFixed(3)} | ${data.PpK.toFixed(3)}` : '';\n  }\n\n  getCpCpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];\n    return data ? `${data.Cp.toFixed(3)} | ${data.CpK.toFixed(3)}` : '';\n  }\n\n  getUnit(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n    return this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName] || '';\n  }\n}\n", "<div class=\"pulse-container\">\n  <h1>Welcome to pulse latest design</h1>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,sBAAsB;;;AAgBvD,OAAM,MAAOC,oBAAoB;EAK/BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAJ/B,KAAAC,UAAU,GAAsB,IAAI;IACpC,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,gBAAgB,GAAa,EAAE;EAEgB;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,aAAa,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACP,UAAU,GAAGO,IAAI;QACtB,IAAI,CAACC,qBAAqB,EAAE;QAC5B,IAAI,CAACP,OAAO,GAAG,KAAK;MACtB,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQO,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACR,UAAU,EAAE;IAEtB,IAAI,CAACE,gBAAgB,GAAG,CAAC,SAAS,CAAC;IACnC,IAAI,CAACF,UAAU,CAACW,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAG;MAC3C,IAAI,CAACb,UAAW,CAACc,cAAc,CAACD,QAAQ,CAAC,CAACD,OAAO,CAACG,WAAW,IAAG;QAC9D,IAAI,CAACb,gBAAgB,CAACc,IAAI,CAAC,GAAGH,QAAQ,IAAIE,WAAW,EAAE,CAAC;MAC1D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAE,eAAeA,CAACC,OAAe,EAAEL,QAAgB,EAAEE,WAAmB;IACpE,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE,OAAO,IAAI;IACjC,MAAMO,IAAI,GAAG,IAAI,CAACP,UAAU,CAACmB,UAAU,CAACD,OAAO,CAAC,GAAGL,QAAQ,CAAC,GAAGE,WAAW,CAAC;IAC3E,OAAOR,IAAI,GAAGA,IAAI,CAACa,OAAO,GAAG,IAAI;EACnC;EAEAC,aAAaA,CAACR,QAAgB,EAAEE,WAAmB;IACjD,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE,OAAO,EAAE;IAC/B,MAAMO,IAAI,GAAGe,MAAM,CAACC,MAAM,CAAC,IAAI,CAACvB,UAAU,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGN,QAAQ,CAAC,GAAGE,WAAW,CAAC;IACpF,OAAOR,IAAI,GAAG,GAAGA,IAAI,CAACiB,EAAE,CAACC,OAAO,CAAC,CAAC,CAAC,MAAMlB,IAAI,CAACmB,GAAG,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;EACrE;EAEAE,aAAaA,CAACd,QAAgB,EAAEE,WAAmB;IACjD,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE,OAAO,EAAE;IAC/B,MAAMO,IAAI,GAAGe,MAAM,CAACC,MAAM,CAAC,IAAI,CAACvB,UAAU,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGN,QAAQ,CAAC,GAAGE,WAAW,CAAC;IACpF,OAAOR,IAAI,GAAG,GAAGA,IAAI,CAACqB,EAAE,CAACH,OAAO,CAAC,CAAC,CAAC,MAAMlB,IAAI,CAACsB,GAAG,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;EACrE;EAEAK,OAAOA,CAACjB,QAAgB,EAAEE,WAAmB;IAC3C,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACA,UAAU,CAAC+B,qBAAqB,CAAClB,QAAQ,CAAC,GAAGE,WAAW,CAAC,IAAI,EAAE;EAC7E;;;uCArDWlB,oBAAoB,EAAAmC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBtC,oBAAoB;MAAAuC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB/Bb,EADF,CAAAe,cAAA,aAA6B,SACvB;UAAAf,EAAA,CAAAgB,MAAA,qCAA8B;UACpChB,EADoC,CAAAiB,YAAA,EAAK,EACnC;;;qBDUFzD,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,wBAAwB,EACxBC,gBAAgB;MAAAsD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}