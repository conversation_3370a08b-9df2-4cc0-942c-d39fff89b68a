/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export function getClosureSafeProperty(objWithPropertyToExtract) {
    for (let key in objWithPropertyToExtract) {
        if (objWithPropertyToExtract[key] === getClosureSafeProperty) {
            return key;
        }
    }
    throw Error('Could not find renamed property on target object.');
}
/**
 * Sets properties on a target object from a source object, but only if
 * the property doesn't already exist on the target object.
 * @param target The target to set properties on
 * @param source The source of the property keys and values to set
 */
export function fillProperties(target, source) {
    for (const key in source) {
        if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {
            target[key] = source[key];
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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