{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PulsedesignComponent {\n  static {\n    this.ɵfac = function PulsedesignComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PulsedesignComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PulsedesignComponent,\n      selectors: [[\"app-pulsedesign\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"pulse-container\"]],\n      template: function PulsedesignComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Welcome to pulse latest design\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".pulse-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2.5rem;\\n  text-align: center;\\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\\n  margin: 0;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcHVsc2VkZXNpZ24vcHVsc2VkZXNpZ24uY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYiw2REFBNkQ7QUFDL0Q7O0FBRUE7RUFDRSxZQUFZO0VBQ1osaUJBQWlCO0VBQ2pCLGtCQUFrQjtFQUNsQix3Q0FBd0M7RUFDeEMsU0FBUztBQUNYIiwic291cmNlc0NvbnRlbnQiOlsiLnB1bHNlLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBoZWlnaHQ6IDEwMHZoO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xufVxuXG5oMSB7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgdGV4dC1zaGFkb3c6IDJweCAycHggNHB4IHJnYmEoMCwwLDAsMC4zKTtcbiAgbWFyZ2luOiAwO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PulsedesignComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PulsedesignComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.ts", "C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-pulsedesign',\n  standalone: true,\n  imports: [],\n  templateUrl: './pulsedesign.component.html',\n  styleUrls: ['./pulsedesign.component.css']\n})\nexport class PulsedesignComponent {\n\n}\n", "<div class=\"pulse-container\">\n  <h1>Welcome to pulse latest design</h1>\n</div>\n"], "mappings": ";AASA,OAAM,MAAOA,oBAAoB;;;uCAApBA,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR/BP,EADF,CAAAS,cAAA,aAA6B,SACvB;UAAAT,EAAA,CAAAU,MAAA,qCAA8B;UACpCV,EADoC,CAAAW,YAAA,EAAK,EACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}