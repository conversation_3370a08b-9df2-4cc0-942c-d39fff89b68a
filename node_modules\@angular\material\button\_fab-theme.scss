@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use './m2-fab';
@use './m3-fab';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use 'sass:map';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-fab.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-fab.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-fab.get-unthemable-tokens());
    }
  }
}

@mixin _fab-variant($theme, $palette) {
  $mat-tokens: if(
    $palette,
    m2-fab.private-get-color-palette-color-tokens($theme, $palette),
    m2-fab.get-color-tokens($theme)
  );

  @include token-utils.create-token-values-mixed($mat-tokens);
}

/// Outputs color theme styles for the mat-fab.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} $color-variant: The color variant to use for the fab: primary, secondary,
//          or tertiary. (If not specified, default primary color will be used).
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-fab.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include _fab-variant($theme, null);

      .mat-mdc-fab, .mat-mdc-mini-fab {
        &.mat-primary {
          @include _fab-variant($theme, primary);
        }

        &.mat-accent {
          @include _fab-variant($theme, accent);
        }

        &.mat-warn {
          @include _fab-variant($theme, warn);
        }
      }
    }
  }
}

/// Outputs typography theme styles for the mat-fab.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-fab.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-fab.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-fab.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-fab.get-tokens($theme), density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-fab.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: fab,
      tokens: token-utils.get-overrides(m3-fab.get-tokens(), fab)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-checkbox.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant: The color variant to use for the fab
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-fab') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
