@use '@material/radio/radio-theme' as mdc-radio-theme;
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/radio' as tokens-mdc-radio;
@use '../core/tokens/m2/mat/radio' as tokens-mat-radio;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-radio.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-unthemable-tokens());
      @include token-utils.create-token-values(
        tokens-mat-radio.$prefix,
        tokens-mat-radio.get-unthemable-tokens()
      );
    }
  }
}

/// Outputs color theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the radio button: primary, secondary, tertiary,
///     or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  } @else {
    .mat-mdc-radio-button {
      &.mat-primary {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme, primary));
        @include token-utils.create-token-values(
          tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme, primary)
        );
      }

      &.mat-accent {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme));
        @include token-utils.create-token-values(
          tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme)
        );
      }

      &.mat-warn {
        @include mdc-radio-theme.theme(tokens-mdc-radio.get-color-tokens($theme, warn));
        @include token-utils.create-token-values(
          tokens-mat-radio.$prefix,
          tokens-mat-radio.get-color-tokens($theme, warn)
        );
      }
    }
  }
}

/// Outputs typography theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-typography-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-radio.$prefix,
        tokens-mat-radio.get-typography-tokens($theme)
      );
    }
  }
}

/// Outputs typography theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
    $density-scale: inspection.get-theme-density($theme);

    @include sass-utils.current-selector-or-root() {
      @include mdc-radio-theme.theme(tokens-mdc-radio.get-density-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-radio.$prefix,
        tokens-mat-radio.get-density-tokens($theme)
      );
    }
  }
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values(
    $tokens,
    (
      prefix: tokens-mdc-radio.$prefix,
      tokens: tokens-mdc-radio.get-token-slots(),
    ),
    (
      prefix: tokens-mat-radio.$prefix,
      tokens: tokens-mat-radio.get-token-slots(),
    )
  );
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-radio.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the radio button: primary, secondary, tertiary,
///     or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-radio') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  $mdc-radio-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-radio.$prefix, $options...);
  $mat-radio-tokens: token-utils.get-tokens-for($tokens, tokens-mat-radio.$prefix, $options...);
  @include mdc-radio-theme.theme($mdc-radio-tokens);
  @include token-utils.create-token-values(tokens-mat-radio.$prefix, $mat-radio-tokens);
}
