/** Possible orientations for a stepper. */
export var StepperOrientation;
(function (StepperOrientation) {
    StepperOrientation[StepperOrientation["HORIZONTAL"] = 0] = "HORIZONTAL";
    StepperOrientation[StepperOrientation["VERTICAL"] = 1] = "VERTICAL";
})(StepperOrientation || (StepperOrientation = {}));
//# sourceMappingURL=data:application/json;base64,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