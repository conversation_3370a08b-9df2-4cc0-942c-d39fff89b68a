{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, ANIMATION_MODULE_TYPE, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, booleanAttribute, EventEmitter, Output, ViewChild, NgZone, ElementRef } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/** Current version of Angular Material. */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst _c5 = [\"mat-internal-form-field\", \"\"];\nconst _c6 = [\"*\"];\nconst VERSION = new Version('18.0.0');\n\n/** @docs-private */\nclass AnimationCurves {\n  static {\n    this.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  }\n  static {\n    this.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  }\n  static {\n    this.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  }\n  static {\n    this.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  }\n}\n/** @docs-private */\nclass AnimationDurations {\n  static {\n    this.COMPLEX = '375ms';\n  }\n  static {\n    this.ENTERING = '225ms';\n  }\n  static {\n    this.EXITING = '195ms';\n  }\n}\n\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n  constructor(highContrastModeDetector, _sanityChecks, _document) {\n    this._sanityChecks = _sanityChecks;\n    this._document = _document;\n    /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n    this._hasDoneGlobalChecks = false;\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Inject in here so the reference to `Platform` can be removed in production mode.\n        const platform = inject(Platform, {\n          optional: true\n        });\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n        }\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n  /** Gets whether a specific sanity check is enabled. */\n  _checkIsEnabled(name) {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n    return !!this._sanityChecks[name];\n  }\n  static {\n    this.ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCommonModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, BidiModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [{\n    type: i1.HighContrastModeDetector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MATERIAL_SANITY_CHECKS]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || !isBrowser) {\n    return;\n  }\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement);\n  // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\nfunction mixinDisabled(base) {\n  return class extends base {\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n  };\n}\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    get color() {\n      return this._color;\n    }\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n        this._color = colorPalette;\n      }\n    }\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor;\n      // Set the default color that can be specified from the mixin.\n      this.color = defaultColor;\n    }\n  };\n}\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n  };\n}\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n  };\n}\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n  constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n    this._defaultMatcher = _defaultMatcher;\n    this.ngControl = ngControl;\n    this._parentFormGroup = _parentFormGroup;\n    this._parentForm = _parentForm;\n    this._stateChanges = _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    this.errorState = false;\n  }\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? this.ngControl.control : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\nfunction mixinErrorState(base) {\n  return class extends base {\n    /** Whether the component is in an error state. */\n    get errorState() {\n      return this._getTracker().errorState;\n    }\n    set errorState(value) {\n      this._getTracker().errorState = value;\n    }\n    /** An object used to control the error state of the component. */\n    get errorStateMatcher() {\n      return this._getTracker().matcher;\n    }\n    set errorStateMatcher(value) {\n      this._getTracker().matcher = value;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n      this._getTracker().updateErrorState();\n    }\n    _getTracker() {\n      if (!this._tracker) {\n        this._tracker = new _ErrorStateTracker(this._defaultErrorStateMatcher, this.ngControl, this._parentFormGroup, this._parentForm, this.stateChanges);\n      }\n      return this._tracker;\n    }\n    constructor(...args) {\n      super(...args);\n    }\n  };\n}\n\n/**\n * Mixin to augment a directive with an initialized property that will emits when ngOnInit ends.\n * @deprecated Track the initialized state manually.\n * @breaking-change 19.0.0\n */\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n      this._isInitialized = true;\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n  };\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n  constructor(\n  /**\n   * @deprecated Now injected via inject(), param to be removed.\n   * @breaking-change 18.0.0\n   */\n  matDateLocale) {\n    super();\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    this.useUtcForDisplay = false;\n    /** The injected locale. */\n    this._matDateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    if (matDateLocale !== undefined) {\n      this._matDateLocale = matDateLocale;\n    }\n    super.setLocale(this._matDateLocale);\n  }\n  getYear(date) {\n    return date.getFullYear();\n  }\n  getMonth(date) {\n    return date.getMonth();\n  }\n  getDate(date) {\n    return date.getDate();\n  }\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  getFirstDayOfWeek() {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n  clone(date) {\n    return new Date(date.getTime());\n  }\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n    return result;\n  }\n  today() {\n    return new Date();\n  }\n  parse(value, parseFormat) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n    return newDate;\n  }\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n  invalid() {\n    return new Date(NaN);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n  static {\n    this.ɵfac = function NativeDateAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NativeDateAdapter,\n      factory: NativeDateAdapter.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_DATE_LOCALE]\n    }]\n  }], null);\n})();\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\nclass NativeDateModule {\n  static {\n    this.ɵfac = function NativeDateModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\nclass MatNativeDateModule {\n  static {\n    this.ɵfac = function MatNativeDateModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatNativeDateModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatNativeDateModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideNativeDateAdapter()]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [provideNativeDateAdapter()]\n    }]\n  }], null, null);\n})();\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n  return [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: formats\n  }];\n}\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ShowOnDirtyErrorStateMatcher,\n      factory: ShowOnDirtyErrorStateMatcher.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static {\n    this.ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ErrorStateMatcher)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ErrorStateMatcher,\n      factory: ErrorStateMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n  static {\n    this.ɵfac = function MatLine_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLine)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLine,\n      selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n      hostAttrs: [1, \"mat-line\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n  static {\n    this.ɵfac = function MatLineModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLineModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatLineModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatLine],\n      exports: [MatLine, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n    /** Current state of the ripple. */\n    this.state = RippleState.HIDDEN;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  constructor() {\n    this._events = new Map();\n    /** Event handler that is bound and which dispatches the events to the different targets. */\n    this._delegateEventHandler = event => {\n      const target = _getEventTarget(event);\n      if (target) {\n        this._events.get(event.type)?.forEach((handlers, element) => {\n          if (element === target || element.contains(target)) {\n            handlers.forEach(handler => handler.handleEvent(event));\n          }\n        });\n      }\n    };\n  }\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  static {\n    this._eventManager = new RippleEventManager();\n  }\n  constructor(_target, _ngZone, elementOrElementRef, _platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Whether the pointer is currently down or not. */\n    this._isPointerDown = false;\n    /**\n     * Map of currently active ripple references.\n     * The ripple reference is mapped to its element event listeners.\n     * The reason why `| null` is used is that event listeners are added only\n     * when the condition is truthy (see the `_startFadeOutTransition` method).\n     */\n    this._activeRipples = new Map();\n    /** Whether pointer-up event listeners have been registered. */\n    this._pointerUpEventsRegistered = false;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n    this._elementRef = _elementRef;\n    this._animationMode = _animationMode;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n    this.radius = 0;\n    this._disabled = false;\n    /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n    this._isInitialized = false;\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatRipple_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRipple,\n      selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n      hostAttrs: [1, \"mat-ripple\"],\n      hostVars: 2,\n      hostBindings: function MatRipple_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n        }\n      },\n      inputs: {\n        color: [0, \"matRippleColor\", \"color\"],\n        unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n        centered: [0, \"matRippleCentered\", \"centered\"],\n        radius: [0, \"matRippleRadius\", \"radius\"],\n        animation: [0, \"matRippleAnimation\", \"animation\"],\n        disabled: [0, \"matRippleDisabled\", \"disabled\"],\n        trigger: [0, \"matRippleTrigger\", \"trigger\"]\n      },\n      exportAs: [\"matRipple\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nclass MatRippleModule {\n  static {\n    this.ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRippleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRippleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatRipple, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n    /** Display state of the checkbox. */\n    this.state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n    this.disabled = false;\n    /**\n     * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n     * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n     */\n    this.appearance = 'full';\n  }\n  static {\n    this.ɵfac = function MatPseudoCheckbox_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPseudoCheckbox,\n      selectors: [[\"mat-pseudo-checkbox\"]],\n      hostAttrs: [1, \"mat-pseudo-checkbox\"],\n      hostVars: 12,\n      hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        state: \"state\",\n        disabled: \"disabled\",\n        appearance: \"appearance\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function MatPseudoCheckbox_Template(rf, ctx) {},\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      standalone: true,\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nclass MatPseudoCheckboxModule {\n  static {\n    this.ɵfac = function MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatPseudoCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPseudoCheckboxModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n  constructor(parent) {\n    /** whether the option group is disabled. */\n    this.disabled = false;\n    /** Unique id for the underlying label. */\n    this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n    this._inert = parent?.inertGroups ?? false;\n  }\n  static {\n    this.ɵfac = function MatOptgroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptgroup)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOptgroup,\n      selectors: [[\"mat-optgroup\"]],\n      hostAttrs: [1, \"mat-mdc-optgroup\"],\n      hostVars: 3,\n      hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n        }\n      },\n      inputs: {\n        label: \"label\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"matOptgroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 4,\n      consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n      template: function MatOptgroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(4, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"id\", ctx._labelId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n        }\n      },\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      standalone: true,\n      template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_OPTION_PARENT_COMPONENT]\n    }, {\n      type: Optional\n    }]\n  }], {\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n  /** Whether the wrapping component is in multiple selection mode. */\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n  get selected() {\n    return this._selected;\n  }\n  /** Whether the option is disabled. */\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  /** Whether ripples for the option are disabled. */\n  get disableRipple() {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n  /** Whether to display checkmark for single-selection. */\n  get hideSingleSelectionIndicator() {\n    return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n  }\n  constructor(_element, _changeDetectorRef, _parent, group) {\n    this._element = _element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._parent = _parent;\n    this.group = group;\n    this._selected = false;\n    this._active = false;\n    this._disabled = false;\n    this._mostRecentViewValue = '';\n    /** The unique ID of the option. */\n    this.id = `mat-option-${_uniqueIdCounter++}`;\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onSelectionChange = new EventEmitter();\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    this._stateChanges = new Subject();\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._text?.nativeElement.textContent || '').trim();\n  }\n  /** Selects the option. */\n  select(emitEvent = true) {\n    if (!this._selected) {\n      this._selected = true;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Deselects the option. */\n  deselect(emitEvent = true) {\n    if (this._selected) {\n      this._selected = false;\n      this._changeDetectorRef.markForCheck();\n      if (emitEvent) {\n        this._emitSelectionChangeEvent();\n      }\n    }\n  }\n  /** Sets focus onto this option. */\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction();\n      // Prevent the page from scrolling down and form submits.\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n      this._changeDetectorRef.markForCheck();\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n  // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n  // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n  // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n      if (viewValue !== this._mostRecentViewValue) {\n        if (this._mostRecentViewValue) {\n          this._stateChanges.next();\n        }\n        this._mostRecentViewValue = viewValue;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n  static {\n    this.ɵfac = function MatOption_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOption,\n      selectors: [[\"mat-option\"]],\n      viewQuery: function MatOption_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n      hostVars: 11,\n      hostBindings: function MatOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n            return ctx._selectViaInteraction();\n          })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n          i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        id: \"id\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        onSelectionChange: \"onSelectionChange\"\n      },\n      exportAs: [\"matOption\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 8,\n      vars: 5,\n      consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementStart(2, \"span\", 2, 0);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3)(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelement(7, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n        }\n      },\n      dependencies: [MatPseudoCheckbox, MatRipple],\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[class.mdc-list-item--selected]': 'selected',\n        '[class.mat-mdc-option-multiple]': 'multiple',\n        '[class.mat-mdc-option-active]': 'active',\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[id]': 'id',\n        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n        // [WAI ARIA Listbox authoring practices guide](\n        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n        // selected option has either aria-selected or aria-checked  set to true. All options that are\n        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n        // aria-selected implementation of Chips and List components.\n        //\n        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n        // every option as \"selected\" (#21491).\n        '[attr.aria-selected]': 'selected',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-mdc-option mdc-list-item'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [MatPseudoCheckbox, MatRipple],\n      template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_OPTION_PARENT_COMPONENT]\n    }]\n  }, {\n    type: MatOptgroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_OPTGROUP]\n    }]\n  }], {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    _text: [{\n      type: ViewChild,\n      args: ['text', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nclass MatOptionModule {\n  static {\n    this.ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n      exports: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  constructor() {\n    this._document = inject(DOCUMENT, {\n      optional: true\n    });\n    this._animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    this._platform = inject(Platform);\n    this._ngZone = inject(NgZone);\n    this._hosts = new Map();\n    /**\n     * Handles creating and attaching component internals\n     * when a component is initially interacted with.\n     */\n    this._onInteraction = event => {\n      const eventTarget = _getEventTarget(event);\n      if (eventTarget instanceof HTMLElement) {\n        // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n        const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n        if (element) {\n          this._createRipple(element);\n        }\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      for (const event of rippleInteractionEvents) {\n        this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n      }\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    for (const event of rippleInteractionEvents) {\n      this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n    }\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Returns the ripple instance for the given host element. */\n  getRipple(host) {\n    const ripple = this._hosts.get(host);\n    return ripple || this._createRipple(host);\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.disabled = disabled;\n      return;\n    }\n    // Otherwise, set an attribute so we know what the\n    // disabled state should be when the ripple is initialized.\n    if (disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document) {\n      return;\n    }\n    const existingRipple = this._hosts.get(host);\n    if (existingRipple) {\n      return existingRipple;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    // Create the MatRipple.\n    const ripple = new MatRipple(new ElementRef(rippleEl), this._ngZone, this._platform, this._globalRippleOptions ? this._globalRippleOptions : undefined, this._animationMode ? this._animationMode : undefined);\n    ripple._isInitialized = true;\n    ripple.trigger = host;\n    ripple.centered = host.hasAttribute(matRippleCentered);\n    ripple.disabled = host.hasAttribute(matRippleDisabled);\n    this.attachRipple(host, ripple);\n    return ripple;\n  }\n  attachRipple(host, ripple) {\n    host.removeAttribute(matRippleUninitialized);\n    this._hosts.set(host, ripple);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      // Since this directive is created manually, it needs to be destroyed manually too.\n      // tslint:disable-next-line:no-lifecycle-invocation\n      ripple.ngOnDestroy();\n      this._hosts.delete(host);\n    }\n  }\n  static {\n    this.ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRippleLoader)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatRippleLoader,\n      factory: MatRippleLoader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n  static {\n    this.ɵfac = function _MatInternalFormField_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || _MatInternalFormField)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _MatInternalFormField,\n      selectors: [[\"div\", \"mat-internal-form-field\", \"\"]],\n      hostAttrs: [1, \"mdc-form-field\", \"mat-internal-form-field\"],\n      hostVars: 2,\n      hostBindings: function _MatInternalFormField_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition === \"before\");\n        }\n      },\n      inputs: {\n        labelPosition: \"labelPosition\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c6,\n      decls: 1,\n      vars: 0,\n      template: function _MatInternalFormField_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatInternalFormField, [{\n    type: Component,\n    args: [{\n      selector: 'div[mat-internal-form-field]',\n      standalone: true,\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mdc-form-field mat-internal-form-field',\n        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"'\n      },\n      styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"]\n    }]\n  }], null, {\n    labelPosition: [{\n      type: Input,\n      args: [{\n        required: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, RippleState, ShowOnDirtyErrorStateMatcher, VERSION, _ErrorStateTracker, _MatInternalFormField, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, provideNativeDateAdapter, setLines };", "map": {"version": 3, "names": ["i0", "Version", "InjectionToken", "inject", "NgModule", "Optional", "Inject", "LOCALE_ID", "Injectable", "Directive", "ANIMATION_MODULE_TYPE", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "booleanAttribute", "EventEmitter", "Output", "ViewChild", "NgZone", "ElementRef", "i1", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "BidiModule", "VERSION", "VERSION$1", "DOCUMENT", "i1$1", "Platform", "_isTestEnvironment", "normalizePassiveListenerOptions", "_getEventTarget", "coerceBooleanProperty", "coerceNumberProperty", "coerceElement", "Observable", "Subject", "startWith", "ENTER", "SPACE", "hasModifierKey", "_c0", "_c1", "_c2", "_c3", "_c4", "MatOption_Conditional_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "disabled", "selected", "MatOption_Conditional_5_Template", "MatOption_Conditional_6_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "group", "label", "_c5", "_c6", "AnimationCurves", "STANDARD_CURVE", "DECELERATION_CURVE", "ACCELERATION_CURVE", "SHARP_CURVE", "AnimationDurations", "COMPLEX", "ENTERING", "EXITING", "MATERIAL_SANITY_CHECKS_FACTORY", "MATERIAL_SANITY_CHECKS", "providedIn", "factory", "MatCommonModule", "constructor", "highContrastModeDetector", "_<PERSON><PERSON><PERSON><PERSON>", "_document", "_hasDoneGlobalChecks", "_applyBodyHighContrastModeCssClasses", "ngDevMode", "platform", "optional", "_checkIsEnabled", "_checkDoctypeIsDefined", "_checkThemeIsPresent", "<PERSON><PERSON><PERSON><PERSON>", "_checkCdkVersionMatch", "name", "ɵfac", "MatCommonModule_Factory", "__ngFactoryType__", "ɵɵinject", "HighContrastModeDetector", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "imports", "ɵsetClassMetadata", "args", "exports", "undefined", "decorators", "Document", "doc", "doctype", "console", "warn", "body", "testElement", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "computedStyle", "getComputedStyle", "display", "remove", "full", "mixinDisabled", "base", "_disabled", "value", "mixinColor", "defaultColor", "color", "_color", "colorPalette", "_elementRef", "nativeElement", "mixinDisableRipple", "disable<PERSON><PERSON><PERSON>", "_disableRipple", "mixinTabIndex", "defaultTabIndex", "tabIndex", "_tabIndex", "_ErrorStateTracker", "_defaultMatcher", "ngControl", "_parentFormGroup", "_parentForm", "_stateChanges", "errorState", "updateErrorState", "oldState", "parent", "matcher", "control", "newState", "isErrorState", "next", "mixinErrorState", "_getTracker", "errorStateMatcher", "_tracker", "_defaultErrorStateMatcher", "stateChanges", "mixinInitialized", "_isInitialized", "_pendingSubscribers", "initialized", "subscriber", "_notifySubscriber", "push", "_markInitialized", "Error", "for<PERSON>ach", "complete", "MAT_DATE_LOCALE", "MAT_DATE_LOCALE_FACTORY", "DateAdapter", "_localeChanges", "localeChanges", "getValidDateOrNull", "obj", "isDateInstance", "<PERSON><PERSON><PERSON><PERSON>", "deserialize", "invalid", "setLocale", "locale", "compareDate", "first", "second", "getYear", "getMonth", "getDate", "sameDate", "firstValid", "second<PERSON><PERSON><PERSON>", "clampDate", "date", "min", "max", "MAT_DATE_FORMATS", "ISO_8601_REGEX", "range", "length", "valueFunction", "valuesArray", "Array", "i", "NativeDateAdapter", "matDateLocale", "useUtcForDisplay", "_matDateLocale", "getFullYear", "getDayOfWeek", "getDay", "getMonthNames", "style", "dtf", "Intl", "DateTimeFormat", "month", "timeZone", "_format", "Date", "getDateNames", "day", "getDayOfWeekNames", "weekday", "getYearName", "year", "getFirstDayOfWeek", "getNumDaysInMonth", "_createDateWithOverflow", "clone", "getTime", "createDate", "result", "today", "parse", "parseFormat", "format", "displayFormat", "addCalendarYears", "years", "addCalendarMonths", "months", "newDate", "addCalendarDays", "days", "toIso8601", "getUTCFullYear", "_2digit", "getUTCMonth", "getUTCDate", "join", "test", "isNaN", "NaN", "d", "setFullYear", "setHours", "n", "slice", "setUTCFullYear", "setUTCHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "NativeDateAdapter_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "MAT_NATIVE_DATE_FORMATS", "dateInput", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "NativeDateModule", "NativeDateModule_Factory", "providers", "provide", "useClass", "MatNativeDateModule", "MatNativeDateModule_Factory", "provideNativeDateAdapter", "formats", "useValue", "ShowOnDirtyErrorStateMatcher", "form", "dirty", "submitted", "ShowOnDirtyErrorStateMatcher_Factory", "ErrorStateMatcher", "touched", "ErrorStateMatcher_Factory", "MatLine", "MatLine_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "standalone", "selector", "host", "setLines", "lines", "element", "prefix", "changes", "pipe", "subscribe", "setClass", "className", "isAdd", "toggle", "MatLineModule", "MatLineModule_Factory", "RippleState", "RippleRef", "_renderer", "config", "_animationForciblyDisabledThroughCss", "state", "HIDDEN", "fadeOut", "fadeOutRipple", "passiveCapturingEventOptions$1", "passive", "capture", "RippleEventManager", "_events", "Map", "_delegate<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "target", "get", "handlers", "contains", "handler", "handleEvent", "add<PERSON><PERSON><PERSON>", "ngZone", "handlersForEvent", "handlersForElement", "set", "Set", "runOutsideAngular", "document", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "delete", "size", "removeEventListener", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveCapturingEventOptions", "pointerDownEvents", "pointerUpEvents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_eventManager", "_target", "_ngZone", "elementOrElementRef", "_platform", "_isPointerDown", "_activeRipples", "_pointerUpEventsRegistered", "_containerElement", "fadeInRipple", "x", "y", "containerRect", "_containerRect", "getBoundingClientRect", "animationConfig", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "ripple", "backgroundColor", "transitionDuration", "computedStyles", "window", "userTransitionProperty", "transitionProperty", "userTransitionDuration", "animationForciblyDisabledThroughCss", "rippleRef", "transform", "FADING_IN", "persistent", "_mostRecentTransientRipple", "eventListeners", "onTransitionEnd", "_finishRippleTransition", "onTransitionCancel", "_destroyRipple", "FADING_OUT", "rippleEl", "opacity", "fadeOutAll", "_getActiveRipples", "fadeOutAllNonPersistent", "setupTriggerEvents", "_triggerElement", "_removeTriggerEvents", "_onMousedown", "_onTouchStart", "_onPointerUp", "_startFadeOutTransition", "isMostRecentTransientRipple", "VISIBLE", "isFakeMousedown", "isSyntheticEvent", "_lastTouchStartEvent", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "isVisible", "terminateOnPointerUp", "from", "keys", "trigger", "rect", "distX", "Math", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "_setupTriggerEventsIfEnabled", "_trigger", "globalOptions", "_animationMode", "_globalOptions", "_ripple<PERSON><PERSON>er", "ngOnInit", "ngOnDestroy", "launch", "configOrX", "MatRipple_Factory", "ɵɵdirectiveInject", "hostVars", "hostBindings", "MatRipple_HostBindings", "ɵɵclassProp", "unbounded", "inputs", "exportAs", "MatRippleModule", "MatRippleModule_Factory", "MatPseudoCheckbox", "appearance", "MatPseudoCheckbox_Factory", "ɵcmp", "ɵɵdefineComponent", "MatPseudoCheckbox_HostBindings", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "MatPseudoCheckbox_Template", "styles", "encapsulation", "changeDetection", "None", "OnPush", "MatPseudoCheckboxModule", "MatPseudoCheckboxModule_Factory", "MAT_OPTION_PARENT_COMPONENT", "_uniqueOptgroupIdCounter", "MAT_OPTGROUP", "MatOptgroup", "_labelId", "_inert", "inertGroups", "MatOptgroup_Factory", "MatOptgroup_HostBindings", "ɵɵattribute", "toString", "ɵɵProvidersFeature", "useExisting", "ɵɵInputTransformsFeature", "ngContentSelectors", "consts", "MatOptgroup_Template", "ɵɵprojectionDef", "ɵɵprojection", "_uniqueIdCounter", "MatOptionSelectionChange", "source", "isUserInput", "MatOption", "multiple", "_parent", "_selected", "hideSingleSelectionIndicator", "_element", "_changeDetectorRef", "_active", "_mostRecentViewValue", "id", "onSelectionChange", "active", "viewValue", "_text", "textContent", "trim", "select", "emitEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "_getHostElement", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "keyCode", "_selectViaInteraction", "preventDefault", "_getTabIndex", "ngAfterViewChecked", "emit", "MatOption_Factory", "ChangeDetectorRef", "viewQuery", "MatOption_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "MatOption_HostBindings", "ɵɵlistener", "MatOption_click_HostBindingHandler", "MatOption_keydown_HostBindingHandler", "$event", "ɵɵhostProperty", "outputs", "MatOption_Template", "ɵɵtemplate", "ɵɵconditional", "dependencies", "static", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "optionsArray", "toArray", "groups", "groupCounter", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "MatOptionModule", "MatOptionModule_Factory", "eventListenerOptions", "rippleInteractionEvents", "matRippleUninitialized", "matRippleClassName", "mat<PERSON><PERSON>pleCentered", "matRippleDisabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_globalRippleOptions", "_hosts", "_onInteraction", "eventTarget", "HTMLElement", "closest", "namespace", "_createRipple", "hosts", "destroyRipple", "configureRipple", "setAttribute", "hasAttribute", "getRipple", "setDisabled", "removeAttribute", "existingRipple", "querySelector", "getAttribute", "append", "attachRipple", "MatRippleLoader_Factory", "_MatInternalFormField", "_MatInternalFormField_Factory", "_MatInternalFormField_HostBindings", "labelPosition", "attrs", "_MatInternalFormField_Template", "required"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/material/fesm2022/core.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, InjectionToken, inject, NgModule, Optional, Inject, LOCALE_ID, Injectable, Directive, ANIMATION_MODULE_TYPE, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, booleanAttribute, EventEmitter, Output, ViewChild, NgZone, ElementRef } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { Platform, _isTestEnvironment, normalizePassiveListenerOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/** Current version of Angular Material. */\nconst VERSION = new Version('18.0.0');\n\n/** @docs-private */\nclass AnimationCurves {\n    static { this.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)'; }\n    static { this.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)'; }\n    static { this.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)'; }\n    static { this.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)'; }\n}\n/** @docs-private */\nclass AnimationDurations {\n    static { this.COMPLEX = '375ms'; }\n    static { this.ENTERING = '225ms'; }\n    static { this.EXITING = '195ms'; }\n}\n\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n    return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n    providedIn: 'root',\n    factory: MATERIAL_SANITY_CHECKS_FACTORY,\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n    constructor(highContrastModeDetector, _sanityChecks, _document) {\n        this._sanityChecks = _sanityChecks;\n        this._document = _document;\n        /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n        this._hasDoneGlobalChecks = false;\n        // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n        // in MatCommonModule.\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n        if (!this._hasDoneGlobalChecks) {\n            this._hasDoneGlobalChecks = true;\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // Inject in here so the reference to `Platform` can be removed in production mode.\n                const platform = inject(Platform, { optional: true });\n                if (this._checkIsEnabled('doctype')) {\n                    _checkDoctypeIsDefined(this._document);\n                }\n                if (this._checkIsEnabled('theme')) {\n                    _checkThemeIsPresent(this._document, !!platform?.isBrowser);\n                }\n                if (this._checkIsEnabled('version')) {\n                    _checkCdkVersionMatch();\n                }\n            }\n        }\n    }\n    /** Gets whether a specific sanity check is enabled. */\n    _checkIsEnabled(name) {\n        if (_isTestEnvironment()) {\n            return false;\n        }\n        if (typeof this._sanityChecks === 'boolean') {\n            return this._sanityChecks;\n        }\n        return !!this._sanityChecks[name];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatCommonModule, deps: [{ token: i1.HighContrastModeDetector }, { token: MATERIAL_SANITY_CHECKS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule], exports: [BidiModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule, BidiModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule],\n                    exports: [BidiModule],\n                }]\n        }], ctorParameters: () => [{ type: i1.HighContrastModeDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MATERIAL_SANITY_CHECKS]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n    if (!doc.doctype) {\n        console.warn('Current document does not have a doctype. This may cause ' +\n            'some Angular Material components not to behave as expected.');\n    }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc, isBrowser) {\n    // We need to assert that the `body` is defined, because these checks run very early\n    // and the `body` won't be defined if the consumer put their scripts in the `head`.\n    if (!doc.body || !isBrowser) {\n        return;\n    }\n    const testElement = doc.createElement('div');\n    testElement.classList.add('mat-theme-loaded-marker');\n    doc.body.appendChild(testElement);\n    const computedStyle = getComputedStyle(testElement);\n    // In some situations the computed style of the test element can be null. For example in\n    // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n    if (computedStyle && computedStyle.display !== 'none') {\n        console.warn('Could not find Angular Material core theme. Most Material ' +\n            'components may not work as expected. For more info refer ' +\n            'to the theming guide: https://material.angular.io/guide/theming');\n    }\n    testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n    if (VERSION.full !== VERSION$1.full) {\n        console.warn('The Angular Material version (' +\n            VERSION.full +\n            ') does not match ' +\n            'the Angular CDK version (' +\n            VERSION$1.full +\n            ').\\n' +\n            'Please ensure the versions of these two packages exactly match.');\n    }\n}\n\nfunction mixinDisabled(base) {\n    return class extends base {\n        get disabled() {\n            return this._disabled;\n        }\n        set disabled(value) {\n            this._disabled = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disabled = false;\n        }\n    };\n}\n\nfunction mixinColor(base, defaultColor) {\n    return class extends base {\n        get color() {\n            return this._color;\n        }\n        set color(value) {\n            const colorPalette = value || this.defaultColor;\n            if (colorPalette !== this._color) {\n                if (this._color) {\n                    this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n                }\n                if (colorPalette) {\n                    this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n                }\n                this._color = colorPalette;\n            }\n        }\n        constructor(...args) {\n            super(...args);\n            this.defaultColor = defaultColor;\n            // Set the default color that can be specified from the mixin.\n            this.color = defaultColor;\n        }\n    };\n}\n\nfunction mixinDisableRipple(base) {\n    return class extends base {\n        /** Whether the ripple effect is disabled or not. */\n        get disableRipple() {\n            return this._disableRipple;\n        }\n        set disableRipple(value) {\n            this._disableRipple = coerceBooleanProperty(value);\n        }\n        constructor(...args) {\n            super(...args);\n            this._disableRipple = false;\n        }\n    };\n}\n\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n    return class extends base {\n        get tabIndex() {\n            return this.disabled ? -1 : this._tabIndex;\n        }\n        set tabIndex(value) {\n            // If the specified tabIndex value is null or undefined, fall back to the default value.\n            this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n        }\n        constructor(...args) {\n            super(...args);\n            this._tabIndex = defaultTabIndex;\n            this.defaultTabIndex = defaultTabIndex;\n        }\n    };\n}\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n        this._defaultMatcher = _defaultMatcher;\n        this.ngControl = ngControl;\n        this._parentFormGroup = _parentFormGroup;\n        this._parentForm = _parentForm;\n        this._stateChanges = _stateChanges;\n        /** Whether the tracker is currently in an error state. */\n        this.errorState = false;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n        const oldState = this.errorState;\n        const parent = this._parentFormGroup || this._parentForm;\n        const matcher = this.matcher || this._defaultMatcher;\n        const control = this.ngControl ? this.ngControl.control : null;\n        const newState = matcher?.isErrorState(control, parent) ?? false;\n        if (newState !== oldState) {\n            this.errorState = newState;\n            this._stateChanges.next();\n        }\n    }\n}\nfunction mixinErrorState(base) {\n    return class extends base {\n        /** Whether the component is in an error state. */\n        get errorState() {\n            return this._getTracker().errorState;\n        }\n        set errorState(value) {\n            this._getTracker().errorState = value;\n        }\n        /** An object used to control the error state of the component. */\n        get errorStateMatcher() {\n            return this._getTracker().matcher;\n        }\n        set errorStateMatcher(value) {\n            this._getTracker().matcher = value;\n        }\n        /** Updates the error state based on the provided error state matcher. */\n        updateErrorState() {\n            this._getTracker().updateErrorState();\n        }\n        _getTracker() {\n            if (!this._tracker) {\n                this._tracker = new _ErrorStateTracker(this._defaultErrorStateMatcher, this.ngControl, this._parentFormGroup, this._parentForm, this.stateChanges);\n            }\n            return this._tracker;\n        }\n        constructor(...args) {\n            super(...args);\n        }\n    };\n}\n\n/**\n * Mixin to augment a directive with an initialized property that will emits when ngOnInit ends.\n * @deprecated Track the initialized state manually.\n * @breaking-change 19.0.0\n */\nfunction mixinInitialized(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            /** Whether this directive has been marked as initialized. */\n            this._isInitialized = false;\n            /**\n             * List of subscribers that subscribed before the directive was initialized. Should be notified\n             * during _markInitialized. Set to null after pending subscribers are notified, and should\n             * not expect to be populated after.\n             */\n            this._pendingSubscribers = [];\n            /**\n             * Observable stream that emits when the directive initializes. If already initialized, the\n             * subscriber is stored to be notified once _markInitialized is called.\n             */\n            this.initialized = new Observable(subscriber => {\n                // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n                // when _markInitialized is called.\n                if (this._isInitialized) {\n                    this._notifySubscriber(subscriber);\n                }\n                else {\n                    this._pendingSubscribers.push(subscriber);\n                }\n            });\n        }\n        /**\n         * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n         * of ngOnInit.\n         * @docs-private\n         */\n        _markInitialized() {\n            if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('This directive has already been marked as initialized and ' +\n                    'should not be called twice.');\n            }\n            this._isInitialized = true;\n            this._pendingSubscribers.forEach(this._notifySubscriber);\n            this._pendingSubscribers = null;\n        }\n        /** Emits and completes the subscriber stream (should only emit once). */\n        _notifySubscriber(subscriber) {\n            subscriber.next();\n            subscriber.complete();\n        }\n    };\n}\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n    providedIn: 'root',\n    factory: MAT_DATE_LOCALE_FACTORY,\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n    return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n    constructor() {\n        this._localeChanges = new Subject();\n        /** A stream that emits when the locale changes. */\n        this.localeChanges = this._localeChanges;\n    }\n    /**\n     * Given a potential date object, returns that same date object if it is\n     * a valid date, or `null` if it's not a valid date.\n     * @param obj The object to check.\n     * @returns A date or `null`.\n     */\n    getValidDateOrNull(obj) {\n        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n    }\n    /**\n     * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n     * string). The default implementation does not allow any deserialization, it simply checks that\n     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n     * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n     * support passing values from your backend directly to these properties by overriding this method\n     * to also deserialize the format used by your backend.\n     * @param value The value to be deserialized into a date object.\n     * @returns The deserialized date object, either a valid date, null if the value can be\n     *     deserialized into a null date (e.g. the empty string), or an invalid date.\n     */\n    deserialize(value) {\n        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n            return value;\n        }\n        return this.invalid();\n    }\n    /**\n     * Sets the locale used for all dates.\n     * @param locale The new locale.\n     */\n    setLocale(locale) {\n        this.locale = locale;\n        this._localeChanges.next();\n    }\n    /**\n     * Compares two dates.\n     * @param first The first date to compare.\n     * @param second The second date to compare.\n     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n     *     a number greater than 0 if the first date is later.\n     */\n    compareDate(first, second) {\n        return (this.getYear(first) - this.getYear(second) ||\n            this.getMonth(first) - this.getMonth(second) ||\n            this.getDate(first) - this.getDate(second));\n    }\n    /**\n     * Checks if two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameDate(first, second) {\n        if (first && second) {\n            let firstValid = this.isValid(first);\n            let secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareDate(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Clamp the given date between min and max dates.\n     * @param date The date to clamp.\n     * @param min The minimum value to allow. If null or omitted no min is enforced.\n     * @param max The maximum value to allow. If null or omitted no max is enforced.\n     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n     *     otherwise `date`.\n     */\n    clampDate(date, min, max) {\n        if (min && this.compareDate(date, min) < 0) {\n            return min;\n        }\n        if (max && this.compareDate(date, max) > 0) {\n            return max;\n        }\n        return date;\n    }\n}\n\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n    const valuesArray = Array(length);\n    for (let i = 0; i < length; i++) {\n        valuesArray[i] = valueFunction(i);\n    }\n    return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n    constructor(\n    /**\n     * @deprecated Now injected via inject(), param to be removed.\n     * @breaking-change 18.0.0\n     */\n    matDateLocale) {\n        super();\n        /**\n         * @deprecated No longer being used. To be removed.\n         * @breaking-change 14.0.0\n         */\n        this.useUtcForDisplay = false;\n        /** The injected locale. */\n        this._matDateLocale = inject(MAT_DATE_LOCALE, { optional: true });\n        if (matDateLocale !== undefined) {\n            this._matDateLocale = matDateLocale;\n        }\n        super.setLocale(this._matDateLocale);\n    }\n    getYear(date) {\n        return date.getFullYear();\n    }\n    getMonth(date) {\n        return date.getMonth();\n    }\n    getDate(date) {\n        return date.getDate();\n    }\n    getDayOfWeek(date) {\n        return date.getDay();\n    }\n    getMonthNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { month: style, timeZone: 'utc' });\n        return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n        const dtf = new Intl.DateTimeFormat(this.locale, { day: 'numeric', timeZone: 'utc' });\n        return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { weekday: style, timeZone: 'utc' });\n        return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { year: 'numeric', timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n        // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n        return 0;\n    }\n    getNumDaysInMonth(date) {\n        return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n        return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Check for invalid month and date (except upper bound on date which we have to check after\n            // creating the Date).\n            if (month < 0 || month > 11) {\n                throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n            }\n            if (date < 1) {\n                throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n            }\n        }\n        let result = this._createDateWithOverflow(year, month, date);\n        // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n        if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n        }\n        return result;\n    }\n    today() {\n        return new Date();\n    }\n    parse(value, parseFormat) {\n        // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n        // parameters.\n        if (typeof value == 'number') {\n            return new Date(value);\n        }\n        return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n        if (!this.isValid(date)) {\n            throw Error('NativeDateAdapter: Cannot format invalid date.');\n        }\n        const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat, timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n        return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n        let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n        // It's possible to wind up in the wrong month if the original month has more days than the new\n        // month. In this case we want to go to the last day of the desired month.\n        // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n        // guarantee this.\n        if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n            newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n        }\n        return newDate;\n    }\n    addCalendarDays(date, days) {\n        return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n        return [\n            date.getUTCFullYear(),\n            this._2digit(date.getUTCMonth() + 1),\n            this._2digit(date.getUTCDate()),\n        ].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n        if (typeof value === 'string') {\n            if (!value) {\n                return null;\n            }\n            // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n            // string is the right format first.\n            if (ISO_8601_REGEX.test(value)) {\n                let date = new Date(value);\n                if (this.isValid(date)) {\n                    return date;\n                }\n            }\n        }\n        return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n        return obj instanceof Date;\n    }\n    isValid(date) {\n        return !isNaN(date.getTime());\n    }\n    invalid() {\n        return new Date(NaN);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setFullYear` and `setHours` instead.\n        const d = new Date();\n        d.setFullYear(year, month, date);\n        d.setHours(0, 0, 0, 0);\n        return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n        return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n        const d = new Date();\n        d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n        d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n        return dtf.format(d);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateAdapter, deps: [{ token: MAT_DATE_LOCALE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateAdapter }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateAdapter, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DATE_LOCALE]\n                }] }] });\n\nconst MAT_NATIVE_DATE_FORMATS = {\n    parse: {\n        dateInput: null,\n    },\n    display: {\n        dateInput: { year: 'numeric', month: 'numeric', day: 'numeric' },\n        monthYearLabel: { year: 'numeric', month: 'short' },\n        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },\n        monthYearA11yLabel: { year: 'numeric', month: 'long' },\n    },\n};\n\nclass NativeDateModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateModule, providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: NativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }],\n                }]\n        }] });\nclass MatNativeDateModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatNativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatNativeDateModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatNativeDateModule, providers: [provideNativeDateAdapter()] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatNativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [provideNativeDateAdapter()],\n                }]\n        }] });\nfunction provideNativeDateAdapter(formats = MAT_NATIVE_DATE_FORMATS) {\n    return [\n        { provide: DateAdapter, useClass: NativeDateAdapter },\n        { provide: MAT_DATE_FORMATS, useValue: formats },\n    ];\n}\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLine, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.0.0\", type: MatLine, isStandalone: true, selector: \"[mat-line], [matLine]\", host: { classAttribute: \"mat-line\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-line], [matLine]',\n                    host: { 'class': 'mat-line' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n    // Note: doesn't need to unsubscribe, because `changes`\n    // gets completed by Angular when the view is destroyed.\n    lines.changes.pipe(startWith(lines)).subscribe(({ length }) => {\n        setClass(element, `${prefix}-2-line`, false);\n        setClass(element, `${prefix}-3-line`, false);\n        setClass(element, `${prefix}-multi-line`, false);\n        if (length === 2 || length === 3) {\n            setClass(element, `${prefix}-${length}-line`, true);\n        }\n        else if (length > 3) {\n            setClass(element, `${prefix}-multi-line`, true);\n        }\n    });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n    element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLineModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatLine], exports: [MatLine, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLineModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatLineModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatLine],\n                    exports: [MatLine, MatCommonModule],\n                }]\n        }] });\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n    RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n    RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n    RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n    RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config, \n    /* Whether animations are forcibly disabled for ripples through CSS. */\n    _animationForciblyDisabledThroughCss = false) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n        /** Current state of the ripple. */\n        this.state = RippleState.HIDDEN;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n    constructor() {\n        this._events = new Map();\n        /** Event handler that is bound and which dispatches the events to the different targets. */\n        this._delegateEventHandler = (event) => {\n            const target = _getEventTarget(event);\n            if (target) {\n                this._events.get(event.type)?.forEach((handlers, element) => {\n                    if (element === target || element.contains(target)) {\n                        handlers.forEach(handler => handler.handleEvent(event));\n                    }\n                });\n            }\n        };\n    }\n    /** Adds an event handler. */\n    addHandler(ngZone, name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (handlersForEvent) {\n            const handlersForElement = handlersForEvent.get(element);\n            if (handlersForElement) {\n                handlersForElement.add(handler);\n            }\n            else {\n                handlersForEvent.set(element, new Set([handler]));\n            }\n        }\n        else {\n            this._events.set(name, new Map([[element, new Set([handler])]]));\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n            });\n        }\n    }\n    /** Removes an event handler. */\n    removeHandler(name, element, handler) {\n        const handlersForEvent = this._events.get(name);\n        if (!handlersForEvent) {\n            return;\n        }\n        const handlersForElement = handlersForEvent.get(element);\n        if (!handlersForElement) {\n            return;\n        }\n        handlersForElement.delete(handler);\n        if (handlersForElement.size === 0) {\n            handlersForEvent.delete(element);\n        }\n        if (handlersForEvent.size === 0) {\n            this._events.delete(name);\n            document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n        }\n    }\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    static { this._eventManager = new RippleEventManager(); }\n    constructor(_target, _ngZone, elementOrElementRef, _platform) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Whether the pointer is currently down or not. */\n        this._isPointerDown = false;\n        /**\n         * Map of currently active ripple references.\n         * The ripple reference is mapped to its element event listeners.\n         * The reason why `| null` is used is that event listeners are added only\n         * when the condition is truthy (see the `_startFadeOutTransition` method).\n         */\n        this._activeRipples = new Map();\n        /** Whether pointer-up event listeners have been registered. */\n        this._pointerUpEventsRegistered = false;\n        // Only do anything if we're on the browser.\n        if (_platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const enterDuration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${enterDuration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical to ensure that the `scale` animates properly.\n        // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n        // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n        const computedStyles = window.getComputedStyle(ripple);\n        const userTransitionProperty = computedStyles.transitionProperty;\n        const userTransitionDuration = computedStyles.transitionDuration;\n        // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n        // `transition: none` or `display: none`). This is technically unexpected since animations are\n        // controlled through the animation config, but this exists for backwards compatibility. This\n        // logic does not need to be super accurate since it covers some edge cases which can be easily\n        // avoided by users.\n        const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n            // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n            // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n            userTransitionDuration === '0s' ||\n            userTransitionDuration === '0s, 0s' ||\n            // If the container is 0x0, it's likely `display: none`.\n            (containerRect.width === 0 && containerRect.height === 0);\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n        // Start the enter animation by setting the transform/scale to 100%. The animation will\n        // execute as part of this statement because we forced a style recalculation before.\n        // Note: We use a 3d transform here in order to avoid an issue in Safari where\n        // the ripples aren't clipped when inside the shadow DOM (see #24028).\n        ripple.style.transform = 'scale3d(1, 1, 1)';\n        rippleRef.state = RippleState.FADING_IN;\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        let eventListeners = null;\n        // Do not register the `transition` event listener if fade-in and fade-out duration\n        // are set to zero. The events won't fire anyway and we can save resources here.\n        if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n            this._ngZone.runOutsideAngular(() => {\n                const onTransitionEnd = () => this._finishRippleTransition(rippleRef);\n                const onTransitionCancel = () => this._destroyRipple(rippleRef);\n                ripple.addEventListener('transitionend', onTransitionEnd);\n                // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n                // directly as otherwise we would keep it part of the ripple container forever.\n                // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n                ripple.addEventListener('transitioncancel', onTransitionCancel);\n                eventListeners = { onTransitionEnd, onTransitionCancel };\n            });\n        }\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.set(rippleRef, eventListeners);\n        // In case there is no fade-in transition duration, we need to manually call the transition\n        // end listener because `transitionend` doesn't fire if there is no transition.\n        if (animationForciblyDisabledThroughCss || !enterDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        // For ripples already fading out or hidden, this should be a noop.\n        if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        // This starts the fade-out transition and will fire the transition end listener that\n        // removes the ripple element from the DOM.\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = RippleState.FADING_OUT;\n        // In case there is no fade-out transition duration, we need to manually call the\n        // transition end listener because `transitionend` doesn't fire if there is no transition.\n        if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n            this._finishRippleTransition(rippleRef);\n        }\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._getActiveRipples().forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        // Use event delegation for the trigger events since they're\n        // set up during creation and are performance-sensitive.\n        pointerDownEvents.forEach(type => {\n            RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n        });\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            // The events for hiding the ripple are bound directly on the trigger, because:\n            // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n            // delegation will be diminished by having to look through all the data structures often.\n            // 2. They aren't as performance-sensitive, because they're bound only after the user\n            // has interacted with an element.\n            this._ngZone.runOutsideAngular(() => {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n                });\n            });\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Method that will be called if the fade-in or fade-in transition completed. */\n    _finishRippleTransition(rippleRef) {\n        if (rippleRef.state === RippleState.FADING_IN) {\n            this._startFadeOutTransition(rippleRef);\n        }\n        else if (rippleRef.state === RippleState.FADING_OUT) {\n            this._destroyRipple(rippleRef);\n        }\n    }\n    /**\n     * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n     * is not held down anymore.\n     */\n    _startFadeOutTransition(rippleRef) {\n        const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n        const { persistent } = rippleRef.config;\n        rippleRef.state = RippleState.VISIBLE;\n        // When the timer runs out while the user has kept their pointer down, we want to\n        // keep only the persistent ripples and the latest transient ripple. We do this,\n        // because we don't want stacked transient ripples to appear after their enter\n        // animation has finished.\n        if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n            rippleRef.fadeOut();\n        }\n    }\n    /** Destroys the given ripple by removing it from the DOM and updating its state. */\n    _destroyRipple(rippleRef) {\n        const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n        this._activeRipples.delete(rippleRef);\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // If the current ref is the most recent transient ripple, unset it\n        // avoid memory leaks.\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        rippleRef.state = RippleState.HIDDEN;\n        if (eventListeners !== null) {\n            rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n            rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n        }\n        rippleRef.element.remove();\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            // According to the typings the touches should always be defined, but in some cases\n            // the browser appears to not assign them in tests which leads to flakes.\n            if (touches) {\n                for (let i = 0; i < touches.length; i++) {\n                    this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n                }\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._getActiveRipples().forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === RippleState.VISIBLE ||\n                (ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    _getActiveRipples() {\n        return Array.from(this._activeRipples.keys());\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        const trigger = this._triggerElement;\n        if (trigger) {\n            pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n                this._pointerUpEventsRegistered = false;\n            }\n        }\n    }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n        this._elementRef = _elementRef;\n        this._animationMode = _animationMode;\n        /**\n         * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n         * will be the distance from the center of the ripple to the furthest corner of the host element's\n         * bounding rectangle.\n         */\n        this.radius = 0;\n        this._disabled = false;\n        /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n        this._isInitialized = false;\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationMode === 'NoopAnimations' ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRipple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1$1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.0.0\", type: MatRipple, isStandalone: true, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1$1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\nclass MatRippleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatRipple], exports: [MatRipple, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRipple],\n                    exports: [MatRipple, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n    constructor(_animationMode) {\n        this._animationMode = _animationMode;\n        /** Display state of the checkbox. */\n        this.state = 'unchecked';\n        /** Whether the checkbox is disabled. */\n        this.disabled = false;\n        /**\n         * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n         * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n         */\n        this.appearance = 'full';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckbox, deps: [{ token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.0\", type: MatPseudoCheckbox, isStandalone: true, selector: \"mat-pseudo-checkbox\", inputs: { state: \"state\", disabled: \"disabled\", appearance: \"appearance\" }, host: { properties: { \"class.mat-pseudo-checkbox-indeterminate\": \"state === \\\"indeterminate\\\"\", \"class.mat-pseudo-checkbox-checked\": \"state === \\\"checked\\\"\", \"class.mat-pseudo-checkbox-disabled\": \"disabled\", \"class.mat-pseudo-checkbox-minimal\": \"appearance === \\\"minimal\\\"\", \"class.mat-pseudo-checkbox-full\": \"appearance === \\\"full\\\"\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-pseudo-checkbox\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckbox, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, selector: 'mat-pseudo-checkbox', template: '', host: {\n                        'class': 'mat-pseudo-checkbox',\n                        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n                        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n                        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n                        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n                        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, standalone: true, styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { state: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }] } });\n\nclass MatPseudoCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule, MatPseudoCheckbox], exports: [MatPseudoCheckbox] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatPseudoCheckbox],\n                    exports: [MatPseudoCheckbox],\n                }]\n        }] });\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup {\n    constructor(parent) {\n        /** whether the option group is disabled. */\n        this.disabled = false;\n        /** Unique id for the underlying label. */\n        this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n        this._inert = parent?.inertGroups ?? false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptgroup, deps: [{ token: MAT_OPTION_PARENT_COMPONENT, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"18.0.0\", type: MatOptgroup, isStandalone: true, selector: \"mat-optgroup\", inputs: { label: \"label\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\" }, classAttribute: \"mat-mdc-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], ngImport: i0, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mat-mdc-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], standalone: true, template: \"<span\\n  class=\\\"mat-mdc-optgroup-label\\\"\\n  role=\\\"presentation\\\"\\n  [class.mdc-list-item--disabled]=\\\"disabled\\\"\\n  [id]=\\\"_labelId\\\">\\n  <span class=\\\"mdc-list-item__primary-text\\\">{{ label }} <ng-content></ng-content></span>\\n</span>\\n\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color);font-family:var(--mat-optgroup-label-text-font);line-height:var(--mat-optgroup-label-text-line-height);font-size:var(--mat-optgroup-label-text-size);letter-spacing:var(--mat-optgroup-label-text-tracking);font-weight:var(--mat-optgroup-label-text-weight)}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;min-height:48px}.mat-mdc-optgroup-label:focus{outline:none}[dir=rtl] .mat-mdc-optgroup-label,.mat-mdc-optgroup-label[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal}\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { label: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption {\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return !!(this._parent && this._parent.disableRipple);\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n        return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    constructor(_element, _changeDetectorRef, _parent, group) {\n        this._element = _element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._parent = _parent;\n        this.group = group;\n        this._selected = false;\n        this._active = false;\n        this._disabled = false;\n        this._mostRecentViewValue = '';\n        /** The unique ID of the option. */\n        this.id = `mat-option-${_uniqueIdCounter++}`;\n        /** Event emitted when the option is selected or deselected. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onSelectionChange = new EventEmitter();\n        /** Emits when the state of the option changes and any parents have to be notified. */\n        this._stateChanges = new Subject();\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select(emitEvent = true) {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Deselects the option. */\n    deselect(emitEvent = true) {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            if (emitEvent) {\n                this._emitSelectionChangeEvent();\n            }\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                if (this._mostRecentViewValue) {\n                    this._stateChanges.next();\n                }\n                this._mostRecentViewValue = viewValue;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOption, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_OPTION_PARENT_COMPONENT, optional: true }, { token: MAT_OPTGROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"18.0.0\", type: MatOption, isStandalone: true, selector: \"mat-option\", inputs: { value: \"value\", id: \"id\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { onSelectionChange: \"onSelectionChange\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-list-item--selected\": \"selected\", \"class.mat-mdc-option-multiple\": \"multiple\", \"class.mat-mdc-option-active\": \"active\", \"class.mdc-list-item--disabled\": \"disabled\", \"id\": \"id\", \"attr.aria-selected\": \"selected\", \"attr.aria-disabled\": \"disabled.toString()\" }, classAttribute: \"mat-mdc-option mdc-list-item\" }, viewQueries: [{ propertyName: \"_text\", first: true, predicate: [\"text\"], descendants: true, static: true }], exportAs: [\"matOption\"], ngImport: i0, template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"component\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\", \"appearance\"] }, { kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[class.mdc-list-item--selected]': 'selected',\n                        '[class.mat-mdc-option-multiple]': 'multiple',\n                        '[class.mat-mdc-option-active]': 'active',\n                        '[class.mdc-list-item--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Set aria-selected to false for non-selected items and true for selected items. Conform to\n                        // [WAI ARIA Listbox authoring practices guide](\n                        //  https://www.w3.org/WAI/ARIA/apg/patterns/listbox/), \"If any options are selected, each\n                        // selected option has either aria-selected or aria-checked  set to true. All options that are\n                        // selectable but not selected have either aria-selected or aria-checked set to false.\" Align\n                        // aria-selected implementation of Chips and List components.\n                        //\n                        // Set `aria-selected=\"false\"` on not-selected listbox options to fix VoiceOver announcing\n                        // every option as \"selected\" (#21491).\n                        '[attr.aria-selected]': 'selected',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-mdc-option mdc-list-item',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, imports: [MatPseudoCheckbox, MatRipple], template: \"<!-- Set aria-hidden=\\\"true\\\" to this DOM node and other decorative nodes in this file. This might\\n be contributing to issue where sometimes VoiceOver focuses on a TextNode in the a11y tree instead\\n of the Option node (#23202). Most assistive technology will generally ignore non-role,\\n non-text-content elements. Adding aria-hidden seems to make VoiceOver behave more consistently. -->\\n@if (multiple) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        [state]=\\\"selected ? 'checked' : 'unchecked'\\\"\\n        aria-hidden=\\\"true\\\"></mat-pseudo-checkbox>\\n}\\n\\n<ng-content select=\\\"mat-icon\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__primary-text\\\" #text><ng-content></ng-content></span>\\n\\n<!-- Render checkmark at the end for single-selection. -->\\n@if (!multiple && selected && !hideSingleSelectionIndicator) {\\n    <mat-pseudo-checkbox\\n        class=\\\"mat-mdc-option-pseudo-checkbox\\\"\\n        [disabled]=\\\"disabled\\\"\\n        state=\\\"checked\\\"\\n        aria-hidden=\\\"true\\\"\\n        appearance=\\\"minimal\\\"></mat-pseudo-checkbox>\\n}\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n@if (group && group._inert) {\\n    <span class=\\\"cdk-visually-hidden\\\">({{ group.label }})</span>\\n}\\n\\n<div class=\\\"mat-mdc-option-ripple mat-mdc-focus-indicator\\\" aria-hidden=\\\"true\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\" [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }] }, { type: MatOptgroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTGROUP]\n                }] }], propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], onSelectionChange: [{\n                type: Output\n            }], _text: [{\n                type: ViewChild,\n                args: ['text', { static: true }]\n            }] } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\nclass MatOptionModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup], exports: [MatOption, MatOptgroup] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptionModule, imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n                    exports: [MatOption, MatOptgroup],\n                }]\n        }] });\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = { capture: true };\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n    constructor() {\n        this._document = inject(DOCUMENT, { optional: true });\n        this._animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this._globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, { optional: true });\n        this._platform = inject(Platform);\n        this._ngZone = inject(NgZone);\n        this._hosts = new Map();\n        /**\n         * Handles creating and attaching component internals\n         * when a component is initially interacted with.\n         */\n        this._onInteraction = (event) => {\n            const eventTarget = _getEventTarget(event);\n            if (eventTarget instanceof HTMLElement) {\n                // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n                const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n                if (element) {\n                    this._createRipple(element);\n                }\n            }\n        };\n        this._ngZone.runOutsideAngular(() => {\n            for (const event of rippleInteractionEvents) {\n                this._document?.addEventListener(event, this._onInteraction, eventListenerOptions);\n            }\n        });\n    }\n    ngOnDestroy() {\n        const hosts = this._hosts.keys();\n        for (const host of hosts) {\n            this.destroyRipple(host);\n        }\n        for (const event of rippleInteractionEvents) {\n            this._document?.removeEventListener(event, this._onInteraction, eventListenerOptions);\n        }\n    }\n    /**\n     * Configures the ripple that will be rendered by the ripple loader.\n     *\n     * Stores the given information about how the ripple should be configured on the host\n     * element so that it can later be retrived & used when the ripple is actually created.\n     */\n    configureRipple(host, config) {\n        // Indicates that the ripple has not yet been rendered for this component.\n        host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n        // Store the additional class name(s) that should be added to the ripple element.\n        if (config.className || !host.hasAttribute(matRippleClassName)) {\n            host.setAttribute(matRippleClassName, config.className || '');\n        }\n        // Store whether the ripple should be centered.\n        if (config.centered) {\n            host.setAttribute(matRippleCentered, '');\n        }\n        if (config.disabled) {\n            host.setAttribute(matRippleDisabled, '');\n        }\n    }\n    /** Returns the ripple instance for the given host element. */\n    getRipple(host) {\n        const ripple = this._hosts.get(host);\n        return ripple || this._createRipple(host);\n    }\n    /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n    setDisabled(host, disabled) {\n        const ripple = this._hosts.get(host);\n        // If the ripple has already been instantiated, just disable it.\n        if (ripple) {\n            ripple.disabled = disabled;\n            return;\n        }\n        // Otherwise, set an attribute so we know what the\n        // disabled state should be when the ripple is initialized.\n        if (disabled) {\n            host.setAttribute(matRippleDisabled, '');\n        }\n        else {\n            host.removeAttribute(matRippleDisabled);\n        }\n    }\n    /** Creates a MatRipple and appends it to the given element. */\n    _createRipple(host) {\n        if (!this._document) {\n            return;\n        }\n        const existingRipple = this._hosts.get(host);\n        if (existingRipple) {\n            return existingRipple;\n        }\n        // Create the ripple element.\n        host.querySelector('.mat-ripple')?.remove();\n        const rippleEl = this._document.createElement('span');\n        rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n        host.append(rippleEl);\n        // Create the MatRipple.\n        const ripple = new MatRipple(new ElementRef(rippleEl), this._ngZone, this._platform, this._globalRippleOptions ? this._globalRippleOptions : undefined, this._animationMode ? this._animationMode : undefined);\n        ripple._isInitialized = true;\n        ripple.trigger = host;\n        ripple.centered = host.hasAttribute(matRippleCentered);\n        ripple.disabled = host.hasAttribute(matRippleDisabled);\n        this.attachRipple(host, ripple);\n        return ripple;\n    }\n    attachRipple(host, ripple) {\n        host.removeAttribute(matRippleUninitialized);\n        this._hosts.set(host, ripple);\n    }\n    destroyRipple(host) {\n        const ripple = this._hosts.get(host);\n        if (ripple) {\n            // Since this directive is created manually, it needs to be destroyed manually too.\n            // tslint:disable-next-line:no-lifecycle-invocation\n            ripple.ngOnDestroy();\n            this._hosts.delete(host);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleLoader, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleLoader, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MatRippleLoader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\nclass _MatInternalFormField {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: _MatInternalFormField, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.0\", type: _MatInternalFormField, isStandalone: true, selector: \"div[mat-internal-form-field]\", inputs: { labelPosition: \"labelPosition\" }, host: { properties: { \"class.mdc-form-field--align-end\": \"labelPosition === \\\"before\\\"\" }, classAttribute: \"mdc-form-field mat-internal-form-field\" }, ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: _MatInternalFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[mat-internal-form-field]', standalone: true, template: '<ng-content></ng-content>', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'mdc-form-field mat-internal-form-field',\n                        '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"',\n                    }, styles: [\".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}\"] }]\n        }], propDecorators: { labelPosition: [{\n                type: Input,\n                args: [{ required: true }]\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleLoader, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, RippleState, ShowOnDirtyErrorStateMatcher, VERSION, _ErrorStateTracker, _MatInternalFormField, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, provideNativeDateAdapter, setLines };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AACzR,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,+BAA+B,EAAEC,gCAAgC,QAAQ,mBAAmB;AACrG,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,OAAO,IAAIC,SAAS,QAAQ,cAAc;AACnD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,QAAQ,EAAEC,kBAAkB,EAAEC,+BAA+B,EAAEC,eAAe,QAAQ,uBAAuB;AACtH,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,uBAAuB;AAClG,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,uBAAuB;;AAEpE;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAoEoGhD,EAAE,CAAAkD,SAAA,4BAyiDk6C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAziDr6CnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,UAAA,aAAAF,MAAA,CAAAG,QAyiDqzC,CAAC,UAAAH,MAAA,CAAAI,QAAA,0BAAuD,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAziDh3ChD,EAAE,CAAAkD,SAAA,4BAyiDu4D,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAziD14DnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,UAAA,aAAAF,MAAA,CAAAG,QAyiDuxD,CAAC;EAAA;AAAA;AAAA,SAAAG,iCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAziD1xDhD,EAAE,CAAA0D,cAAA,aAyiDkiE,CAAC;IAziDriE1D,EAAE,CAAA2D,MAAA,EAyiDqjE,CAAC;IAziDxjE3D,EAAE,CAAA4D,YAAA,CAyiD4jE,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAG,MAAA,GAziD/jEnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA6D,SAAA,CAyiDqjE,CAAC;IAziDxjE7D,EAAE,CAAA8D,kBAAA,MAAAX,MAAA,CAAAY,KAAA,CAAAC,KAAA,KAyiDqjE,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AA5mD5pE,MAAMzC,OAAO,GAAG,IAAIxB,OAAO,CAAC,QAAQ,CAAC;;AAErC;AACA,MAAMkE,eAAe,CAAC;EAClB;IAAS,IAAI,CAACC,cAAc,GAAG,6BAA6B;EAAE;EAC9D;IAAS,IAAI,CAACC,kBAAkB,GAAG,6BAA6B;EAAE;EAClE;IAAS,IAAI,CAACC,kBAAkB,GAAG,2BAA2B;EAAE;EAChE;IAAS,IAAI,CAACC,WAAW,GAAG,6BAA6B;EAAE;AAC/D;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACC,OAAO,GAAG,OAAO;EAAE;EACjC;IAAS,IAAI,CAACC,QAAQ,GAAG,OAAO;EAAE;EAClC;IAAS,IAAI,CAACC,OAAO,GAAG,OAAO;EAAE;AACrC;;AAEA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAO,IAAI;AACf;AACA;AACA,MAAMC,sBAAsB,GAAG,IAAI3E,cAAc,CAAC,mBAAmB,EAAE;EACnE4E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAACC,wBAAwB,EAAEC,aAAa,EAAEC,SAAS,EAAE;IAC5D,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC;IACA;IACAH,wBAAwB,CAACI,oCAAoC,CAAC,CAAC;IAC/D,IAAI,CAAC,IAAI,CAACD,oBAAoB,EAAE;MAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA,MAAMC,QAAQ,GAAGrF,MAAM,CAAC0B,QAAQ,EAAE;UAAE4D,QAAQ,EAAE;QAAK,CAAC,CAAC;QACrD,IAAI,IAAI,CAACC,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCC,sBAAsB,CAAC,IAAI,CAACP,SAAS,CAAC;QAC1C;QACA,IAAI,IAAI,CAACM,eAAe,CAAC,OAAO,CAAC,EAAE;UAC/BE,oBAAoB,CAAC,IAAI,CAACR,SAAS,EAAE,CAAC,CAACI,QAAQ,EAAEK,SAAS,CAAC;QAC/D;QACA,IAAI,IAAI,CAACH,eAAe,CAAC,SAAS,CAAC,EAAE;UACjCI,qBAAqB,CAAC,CAAC;QAC3B;MACJ;IACJ;EACJ;EACA;EACAJ,eAAeA,CAACK,IAAI,EAAE;IAClB,IAAIjE,kBAAkB,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,OAAO,IAAI,CAACqD,aAAa,KAAK,SAAS,EAAE;MACzC,OAAO,IAAI,CAACA,aAAa;IAC7B;IACA,OAAO,CAAC,CAAC,IAAI,CAACA,aAAa,CAACY,IAAI,CAAC;EACrC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFlB,eAAe,EAAzBhF,EAAE,CAAAmG,QAAA,CAAyC9E,EAAE,CAAC+E,wBAAwB,GAAtEpG,EAAE,CAAAmG,QAAA,CAAiFtB,sBAAsB,MAAzG7E,EAAE,CAAAmG,QAAA,CAAoIxE,QAAQ;IAAA,CAA2C;EAAE;EAC3R;IAAS,IAAI,CAAC0E,IAAI,kBAD8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EACSvB;IAAe,EAAiD;EAAE;EAC7K;IAAS,IAAI,CAACwB,IAAI,kBAF8ExG,EAAE,CAAAyG,gBAAA;MAAAC,OAAA,GAEoClF,UAAU,EAAEA,UAAU;IAAA,EAAI;EAAE;AACtK;AACA;EAAA,QAAA+D,SAAA,oBAAAA,SAAA,KAJoGvF,EAAE,CAAA2G,iBAAA,CAIX3B,eAAe,EAAc,CAAC;IAC7GuB,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAClF,UAAU,CAAC;MACrBqF,OAAO,EAAE,CAACrF,UAAU;IACxB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+E,IAAI,EAAElF,EAAE,CAAC+E;EAAyB,CAAC,EAAE;IAAEG,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtFR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAAC/B,sBAAsB;IACjC,CAAC;EAAE,CAAC,EAAE;IAAE0B,IAAI,EAAES,QAAQ;IAAED,UAAU,EAAE,CAAC;MACjCR,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAACjF,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA,SAASgE,sBAAsBA,CAACsB,GAAG,EAAE;EACjC,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,GACpE,6DAA6D,CAAC;EACtE;AACJ;AACA;AACA,SAASxB,oBAAoBA,CAACqB,GAAG,EAAEpB,SAAS,EAAE;EAC1C;EACA;EACA,IAAI,CAACoB,GAAG,CAACI,IAAI,IAAI,CAACxB,SAAS,EAAE;IACzB;EACJ;EACA,MAAMyB,WAAW,GAAGL,GAAG,CAACM,aAAa,CAAC,KAAK,CAAC;EAC5CD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACpDR,GAAG,CAACI,IAAI,CAACK,WAAW,CAACJ,WAAW,CAAC;EACjC,MAAMK,aAAa,GAAGC,gBAAgB,CAACN,WAAW,CAAC;EACnD;EACA;EACA;EACA,IAAIK,aAAa,IAAIA,aAAa,CAACE,OAAO,KAAK,MAAM,EAAE;IACnDV,OAAO,CAACC,IAAI,CAAC,4DAA4D,GACrE,2DAA2D,GAC3D,iEAAiE,CAAC;EAC1E;EACAE,WAAW,CAACQ,MAAM,CAAC,CAAC;AACxB;AACA;AACA,SAAShC,qBAAqBA,CAAA,EAAG;EAC7B,IAAIrE,OAAO,CAACsG,IAAI,KAAKrG,SAAS,CAACqG,IAAI,EAAE;IACjCZ,OAAO,CAACC,IAAI,CAAC,gCAAgC,GACzC3F,OAAO,CAACsG,IAAI,GACZ,mBAAmB,GACnB,2BAA2B,GAC3BrG,SAAS,CAACqG,IAAI,GACd,MAAM,GACN,iEAAiE,CAAC;EAC1E;AACJ;AAEA,SAASC,aAAaA,CAACC,IAAI,EAAE;EACzB,OAAO,cAAcA,IAAI,CAAC;IACtB,IAAI3E,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC4E,SAAS;IACzB;IACA,IAAI5E,QAAQA,CAAC6E,KAAK,EAAE;MAChB,IAAI,CAACD,SAAS,GAAGjG,qBAAqB,CAACkG,KAAK,CAAC;IACjD;IACAlD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACsB,SAAS,GAAG,KAAK;IAC1B;EACJ,CAAC;AACL;AAEA,SAASE,UAAUA,CAACH,IAAI,EAAEI,YAAY,EAAE;EACpC,OAAO,cAAcJ,IAAI,CAAC;IACtB,IAAIK,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACC,MAAM;IACtB;IACA,IAAID,KAAKA,CAACH,KAAK,EAAE;MACb,MAAMK,YAAY,GAAGL,KAAK,IAAI,IAAI,CAACE,YAAY;MAC/C,IAAIG,YAAY,KAAK,IAAI,CAACD,MAAM,EAAE;QAC9B,IAAI,IAAI,CAACA,MAAM,EAAE;UACb,IAAI,CAACE,WAAW,CAACC,aAAa,CAAClB,SAAS,CAACM,MAAM,CAAC,OAAO,IAAI,CAACS,MAAM,EAAE,CAAC;QACzE;QACA,IAAIC,YAAY,EAAE;UACd,IAAI,CAACC,WAAW,CAACC,aAAa,CAAClB,SAAS,CAACC,GAAG,CAAC,OAAOe,YAAY,EAAE,CAAC;QACvE;QACA,IAAI,CAACD,MAAM,GAAGC,YAAY;MAC9B;IACJ;IACAvD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACyB,YAAY,GAAGA,YAAY;MAChC;MACA,IAAI,CAACC,KAAK,GAAGD,YAAY;IAC7B;EACJ,CAAC;AACL;AAEA,SAASM,kBAAkBA,CAACV,IAAI,EAAE;EAC9B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIW,aAAaA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACC,cAAc;IAC9B;IACA,IAAID,aAAaA,CAACT,KAAK,EAAE;MACrB,IAAI,CAACU,cAAc,GAAG5G,qBAAqB,CAACkG,KAAK,CAAC;IACtD;IACAlD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACiC,cAAc,GAAG,KAAK;IAC/B;EACJ,CAAC;AACL;AAEA,SAASC,aAAaA,CAACb,IAAI,EAAEc,eAAe,GAAG,CAAC,EAAE;EAC9C,OAAO,cAAcd,IAAI,CAAC;IACtB,IAAIe,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC1F,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC2F,SAAS;IAC9C;IACA,IAAID,QAAQA,CAACb,KAAK,EAAE;MAChB;MACA,IAAI,CAACc,SAAS,GAAGd,KAAK,IAAI,IAAI,GAAGjG,oBAAoB,CAACiG,KAAK,CAAC,GAAG,IAAI,CAACY,eAAe;IACvF;IACA9D,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd,IAAI,CAACqC,SAAS,GAAGF,eAAe;MAChC,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,CAAC;EACrBjE,WAAWA,CAACkE,eAAe,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,aAAa,EAAE;IAClF,IAAI,CAACJ,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;EAC3B;EACA;EACAC,gBAAgBA,CAAA,EAAG;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACF,UAAU;IAChC,MAAMG,MAAM,GAAG,IAAI,CAACN,gBAAgB,IAAI,IAAI,CAACC,WAAW;IACxD,MAAMM,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACT,eAAe;IACpD,MAAMU,OAAO,GAAG,IAAI,CAACT,SAAS,GAAG,IAAI,CAACA,SAAS,CAACS,OAAO,GAAG,IAAI;IAC9D,MAAMC,QAAQ,GAAGF,OAAO,EAAEG,YAAY,CAACF,OAAO,EAAEF,MAAM,CAAC,IAAI,KAAK;IAChE,IAAIG,QAAQ,KAAKJ,QAAQ,EAAE;MACvB,IAAI,CAACF,UAAU,GAAGM,QAAQ;MAC1B,IAAI,CAACP,aAAa,CAACS,IAAI,CAAC,CAAC;IAC7B;EACJ;AACJ;AACA,SAASC,eAAeA,CAAChC,IAAI,EAAE;EAC3B,OAAO,cAAcA,IAAI,CAAC;IACtB;IACA,IAAIuB,UAAUA,CAAA,EAAG;MACb,OAAO,IAAI,CAACU,WAAW,CAAC,CAAC,CAACV,UAAU;IACxC;IACA,IAAIA,UAAUA,CAACrB,KAAK,EAAE;MAClB,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACV,UAAU,GAAGrB,KAAK;IACzC;IACA;IACA,IAAIgC,iBAAiBA,CAAA,EAAG;MACpB,OAAO,IAAI,CAACD,WAAW,CAAC,CAAC,CAACN,OAAO;IACrC;IACA,IAAIO,iBAAiBA,CAAChC,KAAK,EAAE;MACzB,IAAI,CAAC+B,WAAW,CAAC,CAAC,CAACN,OAAO,GAAGzB,KAAK;IACtC;IACA;IACAsB,gBAAgBA,CAAA,EAAG;MACf,IAAI,CAACS,WAAW,CAAC,CAAC,CAACT,gBAAgB,CAAC,CAAC;IACzC;IACAS,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,IAAIlB,kBAAkB,CAAC,IAAI,CAACmB,yBAAyB,EAAE,IAAI,CAACjB,SAAS,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACgB,YAAY,CAAC;MACtJ;MACA,OAAO,IAAI,CAACF,QAAQ;IACxB;IACAnF,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;IAClB;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS2D,gBAAgBA,CAACtC,IAAI,EAAE;EAC5B,OAAO,cAAcA,IAAI,CAAC;IACtBhD,WAAWA,CAAC,GAAG2B,IAAI,EAAE;MACjB,KAAK,CAAC,GAAGA,IAAI,CAAC;MACd;MACA,IAAI,CAAC4D,cAAc,GAAG,KAAK;MAC3B;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B;AACZ;AACA;AACA;MACY,IAAI,CAACC,WAAW,GAAG,IAAItI,UAAU,CAACuI,UAAU,IAAI;QAC5C;QACA;QACA,IAAI,IAAI,CAACH,cAAc,EAAE;UACrB,IAAI,CAACI,iBAAiB,CAACD,UAAU,CAAC;QACtC,CAAC,MACI;UACD,IAAI,CAACF,mBAAmB,CAACI,IAAI,CAACF,UAAU,CAAC;QAC7C;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;IACQG,gBAAgBA,CAAA,EAAG;MACf,IAAI,IAAI,CAACN,cAAc,KAAK,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMwF,KAAK,CAAC,4DAA4D,GACpE,6BAA6B,CAAC;MACtC;MACA,IAAI,CAACP,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,mBAAmB,CAACO,OAAO,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACxD,IAAI,CAACH,mBAAmB,GAAG,IAAI;IACnC;IACA;IACAG,iBAAiBA,CAACD,UAAU,EAAE;MAC1BA,UAAU,CAACX,IAAI,CAAC,CAAC;MACjBW,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB;EACJ,CAAC;AACL;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIhL,cAAc,CAAC,iBAAiB,EAAE;EAC1D4E,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEoG;AACb,CAAC,CAAC;AACF;AACA,SAASA,uBAAuBA,CAAA,EAAG;EAC/B,OAAOhL,MAAM,CAACI,SAAS,CAAC;AAC5B;AACA;AACA,MAAM6K,WAAW,CAAC;EACdnG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoG,cAAc,GAAG,IAAIhJ,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACiJ,aAAa,GAAG,IAAI,CAACD,cAAc;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,kBAAkBA,CAACC,GAAG,EAAE;IACpB,OAAO,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,WAAWA,CAACxD,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,IAAK,IAAI,CAACsD,cAAc,CAACtD,KAAK,CAAC,IAAI,IAAI,CAACuD,OAAO,CAACvD,KAAK,CAAE,EAAE;MACtE,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACyD,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACT,cAAc,CAACrB,IAAI,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+B,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACvB,OAAQ,IAAI,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI,CAACE,OAAO,CAACD,MAAM,CAAC,IAC9C,IAAI,CAACE,QAAQ,CAACH,KAAK,CAAC,GAAG,IAAI,CAACG,QAAQ,CAACF,MAAM,CAAC,IAC5C,IAAI,CAACG,OAAO,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACI,OAAO,CAACH,MAAM,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,QAAQA,CAACL,KAAK,EAAEC,MAAM,EAAE;IACpB,IAAID,KAAK,IAAIC,MAAM,EAAE;MACjB,IAAIK,UAAU,GAAG,IAAI,CAACZ,OAAO,CAACM,KAAK,CAAC;MACpC,IAAIO,WAAW,GAAG,IAAI,CAACb,OAAO,CAACO,MAAM,CAAC;MACtC,IAAIK,UAAU,IAAIC,WAAW,EAAE;QAC3B,OAAO,CAAC,IAAI,CAACR,WAAW,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;MACA,OAAOK,UAAU,IAAIC,WAAW;IACpC;IACA,OAAOP,KAAK,IAAIC,MAAM;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,SAASA,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAID,GAAG,IAAI,IAAI,CAACX,WAAW,CAACU,IAAI,EAAEC,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,IAAIC,GAAG,IAAI,IAAI,CAACZ,WAAW,CAACU,IAAI,EAAEE,GAAG,CAAC,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG;IACd;IACA,OAAOF,IAAI;EACf;AACJ;AAEA,MAAMG,gBAAgB,GAAG,IAAI1M,cAAc,CAAC,kBAAkB,CAAC;;AAE/D;AACA;AACA;AACA;AACA;AACA,MAAM2M,cAAc,GAAG,oFAAoF;AAC3G;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAClC,MAAMC,WAAW,GAAGC,KAAK,CAACH,MAAM,CAAC;EACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC7BF,WAAW,CAACE,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,CAAC;EACrC;EACA,OAAOF,WAAW;AACtB;AACA;AACA,MAAMG,iBAAiB,SAAShC,WAAW,CAAC;EACxCnG,WAAWA;EACX;AACJ;AACA;AACA;EACIoI,aAAa,EAAE;IACX,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACC,cAAc,GAAGpN,MAAM,CAAC+K,eAAe,EAAE;MAAEzF,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjE,IAAI4H,aAAa,KAAKvG,SAAS,EAAE;MAC7B,IAAI,CAACyG,cAAc,GAAGF,aAAa;IACvC;IACA,KAAK,CAACxB,SAAS,CAAC,IAAI,CAAC0B,cAAc,CAAC;EACxC;EACArB,OAAOA,CAACO,IAAI,EAAE;IACV,OAAOA,IAAI,CAACe,WAAW,CAAC,CAAC;EAC7B;EACArB,QAAQA,CAACM,IAAI,EAAE;IACX,OAAOA,IAAI,CAACN,QAAQ,CAAC,CAAC;EAC1B;EACAC,OAAOA,CAACK,IAAI,EAAE;IACV,OAAOA,IAAI,CAACL,OAAO,CAAC,CAAC;EACzB;EACAqB,YAAYA,CAAChB,IAAI,EAAE;IACf,OAAOA,IAAI,CAACiB,MAAM,CAAC,CAAC;EACxB;EACAC,aAAaA,CAACC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEkC,KAAK,EAAEJ,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACnF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAEhB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClE;EACAiB,YAAYA,CAAA,EAAG;IACX,MAAMP,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEuC,GAAG,EAAE,SAAS;MAAEJ,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,EAAE,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtE;EACAmB,iBAAiBA,CAACV,KAAK,EAAE;IACrB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAEyC,OAAO,EAAEX,KAAK;MAAEK,QAAQ,EAAE;IAAM,CAAC,CAAC;IACrF,OAAOnB,KAAK,CAAC,CAAC,EAAEK,CAAC,IAAI,IAAI,CAACe,OAAO,CAACL,GAAG,EAAE,IAAIM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAEhB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrE;EACAqB,WAAWA,CAAC/B,IAAI,EAAE;IACd,MAAMoB,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE2C,IAAI,EAAE,SAAS;MAAER,QAAQ,EAAE;IAAM,CAAC,CAAC;IACtF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACAiC,iBAAiBA,CAAA,EAAG;IAChB;IACA,OAAO,CAAC;EACZ;EACAC,iBAAiBA,CAAClC,IAAI,EAAE;IACpB,OAAO,IAAI,CAACL,OAAO,CAAC,IAAI,CAACwC,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACrG;EACAoC,KAAKA,CAACpC,IAAI,EAAE;IACR,OAAO,IAAI0B,IAAI,CAAC1B,IAAI,CAACqC,OAAO,CAAC,CAAC,CAAC;EACnC;EACAC,UAAUA,CAACN,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IAC1B,IAAI,OAAOlH,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA,IAAIyI,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;QACzB,MAAMjD,KAAK,CAAC,wBAAwBiD,KAAK,4CAA4C,CAAC;MAC1F;MACA,IAAIvB,IAAI,GAAG,CAAC,EAAE;QACV,MAAM1B,KAAK,CAAC,iBAAiB0B,IAAI,mCAAmC,CAAC;MACzE;IACJ;IACA,IAAIuC,MAAM,GAAG,IAAI,CAACJ,uBAAuB,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAC5D;IACA,IAAIuC,MAAM,CAAC7C,QAAQ,CAAC,CAAC,IAAI6B,KAAK,KAAK,OAAOzI,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/E,MAAMwF,KAAK,CAAC,iBAAiB0B,IAAI,2BAA2BuB,KAAK,IAAI,CAAC;IAC1E;IACA,OAAOgB,MAAM;EACjB;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAId,IAAI,CAAC,CAAC;EACrB;EACAe,KAAKA,CAAC/G,KAAK,EAAEgH,WAAW,EAAE;IACtB;IACA;IACA,IAAI,OAAOhH,KAAK,IAAI,QAAQ,EAAE;MAC1B,OAAO,IAAIgG,IAAI,CAAChG,KAAK,CAAC;IAC1B;IACA,OAAOA,KAAK,GAAG,IAAIgG,IAAI,CAACA,IAAI,CAACe,KAAK,CAAC/G,KAAK,CAAC,CAAC,GAAG,IAAI;EACrD;EACAiH,MAAMA,CAAC3C,IAAI,EAAE4C,aAAa,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC3D,OAAO,CAACe,IAAI,CAAC,EAAE;MACrB,MAAM1B,KAAK,CAAC,gDAAgD,CAAC;IACjE;IACA,MAAM8C,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;MAAE,GAAGuD,aAAa;MAAEpB,QAAQ,EAAE;IAAM,CAAC,CAAC;IACvF,OAAO,IAAI,CAACC,OAAO,CAACL,GAAG,EAAEpB,IAAI,CAAC;EAClC;EACA6C,gBAAgBA,CAAC7C,IAAI,EAAE8C,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACC,iBAAiB,CAAC/C,IAAI,EAAE8C,KAAK,GAAG,EAAE,CAAC;EACnD;EACAC,iBAAiBA,CAAC/C,IAAI,EAAEgD,MAAM,EAAE;IAC5B,IAAIC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,EAAE,IAAI,CAACrD,OAAO,CAACK,IAAI,CAAC,CAAC;IAChH;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACN,QAAQ,CAACuD,OAAO,CAAC,IAAI,CAAE,CAAC,IAAI,CAACvD,QAAQ,CAACM,IAAI,CAAC,GAAGgD,MAAM,IAAI,EAAE,GAAI,EAAE,IAAI,EAAE,EAAE;MAC7EC,OAAO,GAAG,IAAI,CAACd,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACwD,OAAO,CAAC,EAAE,IAAI,CAACvD,QAAQ,CAACuD,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5F;IACA,OAAOA,OAAO;EAClB;EACAC,eAAeA,CAAClD,IAAI,EAAEmD,IAAI,EAAE;IACxB,OAAO,IAAI,CAAChB,uBAAuB,CAAC,IAAI,CAAC1C,OAAO,CAACO,IAAI,CAAC,EAAE,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC,EAAE,IAAI,CAACL,OAAO,CAACK,IAAI,CAAC,GAAGmD,IAAI,CAAC;EAC3G;EACAC,SAASA,CAACpD,IAAI,EAAE;IACZ,OAAO,CACHA,IAAI,CAACqD,cAAc,CAAC,CAAC,EACrB,IAAI,CAACC,OAAO,CAACtD,IAAI,CAACuD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EACpC,IAAI,CAACD,OAAO,CAACtD,IAAI,CAACwD,UAAU,CAAC,CAAC,CAAC,CAClC,CAACC,IAAI,CAAC,GAAG,CAAC;EACf;EACA;AACJ;AACA;AACA;AACA;EACIvE,WAAWA,CAACxD,KAAK,EAAE;IACf,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAACA,KAAK,EAAE;QACR,OAAO,IAAI;MACf;MACA;MACA;MACA,IAAI0E,cAAc,CAACsD,IAAI,CAAChI,KAAK,CAAC,EAAE;QAC5B,IAAIsE,IAAI,GAAG,IAAI0B,IAAI,CAAChG,KAAK,CAAC;QAC1B,IAAI,IAAI,CAACuD,OAAO,CAACe,IAAI,CAAC,EAAE;UACpB,OAAOA,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK,CAACd,WAAW,CAACxD,KAAK,CAAC;EACnC;EACAsD,cAAcA,CAACD,GAAG,EAAE;IAChB,OAAOA,GAAG,YAAY2C,IAAI;EAC9B;EACAzC,OAAOA,CAACe,IAAI,EAAE;IACV,OAAO,CAAC2D,KAAK,CAAC3D,IAAI,CAACqC,OAAO,CAAC,CAAC,CAAC;EACjC;EACAlD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAIuC,IAAI,CAACkC,GAAG,CAAC;EACxB;EACA;EACAzB,uBAAuBA,CAACH,IAAI,EAAET,KAAK,EAAEvB,IAAI,EAAE;IACvC;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,CAAC,CAAC;IACpBmC,CAAC,CAACC,WAAW,CAAC9B,IAAI,EAAET,KAAK,EAAEvB,IAAI,CAAC;IAChC6D,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB,OAAOF,CAAC;EACZ;EACA;AACJ;AACA;AACA;AACA;EACIP,OAAOA,CAACU,CAAC,EAAE;IACP,OAAO,CAAC,IAAI,GAAGA,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxC,OAAOA,CAACL,GAAG,EAAEpB,IAAI,EAAE;IACf;IACA;IACA,MAAM6D,CAAC,GAAG,IAAInC,IAAI,CAAC,CAAC;IACpBmC,CAAC,CAACK,cAAc,CAAClE,IAAI,CAACe,WAAW,CAAC,CAAC,EAAEf,IAAI,CAACN,QAAQ,CAAC,CAAC,EAAEM,IAAI,CAACL,OAAO,CAAC,CAAC,CAAC;IACrEkE,CAAC,CAACM,WAAW,CAACnE,IAAI,CAACoE,QAAQ,CAAC,CAAC,EAAEpE,IAAI,CAACqE,UAAU,CAAC,CAAC,EAAErE,IAAI,CAACsE,UAAU,CAAC,CAAC,EAAEtE,IAAI,CAACuE,eAAe,CAAC,CAAC,CAAC;IAC5F,OAAOnD,GAAG,CAACuB,MAAM,CAACkB,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAACtK,IAAI,YAAAiL,0BAAA/K,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkH,iBAAiB,EApiB3BpN,EAAE,CAAAmG,QAAA,CAoiB2C+E,eAAe;IAAA,CAA6D;EAAE;EAC3N;IAAS,IAAI,CAACgG,KAAK,kBAriB6ElR,EAAE,CAAAmR,kBAAA;MAAAC,KAAA,EAqiBYhE,iBAAiB;MAAArI,OAAA,EAAjBqI,iBAAiB,CAAApH;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAviBoGvF,EAAE,CAAA2G,iBAAA,CAuiBXyG,iBAAiB,EAAc,CAAC;IAC/G7G,IAAI,EAAE/F;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+F,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAACsE,eAAe;IAC1B,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMmG,uBAAuB,GAAG;EAC5BnC,KAAK,EAAE;IACHoC,SAAS,EAAE;EACf,CAAC;EACDzJ,OAAO,EAAE;IACLyJ,SAAS,EAAE;MAAE7C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,SAAS;MAAEK,GAAG,EAAE;IAAU,CAAC;IAChEkD,cAAc,EAAE;MAAE9C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAQ,CAAC;IACnDwD,aAAa,EAAE;MAAE/C,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE,MAAM;MAAEK,GAAG,EAAE;IAAU,CAAC;IACjEoD,kBAAkB,EAAE;MAAEhD,IAAI,EAAE,SAAS;MAAET,KAAK,EAAE;IAAO;EACzD;AACJ,CAAC;AAED,MAAM0D,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC1L,IAAI,YAAA2L,yBAAAzL,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwL,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACrL,IAAI,kBA9jB8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EA8jBSmL;IAAgB,EAAG;EAAE;EAChI;IAAS,IAAI,CAAClL,IAAI,kBA/jB8ExG,EAAE,CAAAyG,gBAAA;MAAAmL,SAAA,EA+jBsC,CAAC;QAAEC,OAAO,EAAEzG,WAAW;QAAE0G,QAAQ,EAAE1E;MAAkB,CAAC;IAAC,EAAG;EAAE;AACxM;AACA;EAAA,QAAA7H,SAAA,oBAAAA,SAAA,KAjkBoGvF,EAAE,CAAA2G,iBAAA,CAikBX+K,gBAAgB,EAAc,CAAC;IAC9GnL,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCgL,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEzG,WAAW;QAAE0G,QAAQ,EAAE1E;MAAkB,CAAC;IACrE,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM2E,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAAC/L,IAAI,YAAAgM,4BAAA9L,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6L,mBAAmB;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAAC1L,IAAI,kBAzkB8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EAykBSwL;IAAmB,EAAG;EAAE;EACnI;IAAS,IAAI,CAACvL,IAAI,kBA1kB8ExG,EAAE,CAAAyG,gBAAA;MAAAmL,SAAA,EA0kByC,CAACK,wBAAwB,CAAC,CAAC;IAAC,EAAG;EAAE;AAChL;AACA;EAAA,QAAA1M,SAAA,oBAAAA,SAAA,KA5kBoGvF,EAAE,CAAA2G,iBAAA,CA4kBXoL,mBAAmB,EAAc,CAAC;IACjHxL,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCgL,SAAS,EAAE,CAACK,wBAAwB,CAAC,CAAC;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,SAASA,wBAAwBA,CAACC,OAAO,GAAGb,uBAAuB,EAAE;EACjE,OAAO,CACH;IAAEQ,OAAO,EAAEzG,WAAW;IAAE0G,QAAQ,EAAE1E;EAAkB,CAAC,EACrD;IAAEyE,OAAO,EAAEjF,gBAAgB;IAAEuF,QAAQ,EAAED;EAAQ,CAAC,CACnD;AACL;;AAEA;AACA,MAAME,4BAA4B,CAAC;EAC/BrI,YAAYA,CAACF,OAAO,EAAEwI,IAAI,EAAE;IACxB,OAAO,CAAC,EAAExI,OAAO,IAAIA,OAAO,CAAC+B,OAAO,KAAK/B,OAAO,CAACyI,KAAK,IAAKD,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EACxF;EACA;IAAS,IAAI,CAACvM,IAAI,YAAAwM,qCAAAtM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkM,4BAA4B;IAAA,CAAoD;EAAE;EAC5L;IAAS,IAAI,CAAClB,KAAK,kBA/lB6ElR,EAAE,CAAAmR,kBAAA;MAAAC,KAAA,EA+lBYgB,4BAA4B;MAAArN,OAAA,EAA5BqN,4BAA4B,CAAApM;IAAA,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAT,SAAA,oBAAAA,SAAA,KAjmBoGvF,EAAE,CAAA2G,iBAAA,CAimBXyL,4BAA4B,EAAc,CAAC;IAC1H7L,IAAI,EAAE/F;EACV,CAAC,CAAC;AAAA;AACV;AACA,MAAMiS,iBAAiB,CAAC;EACpB1I,YAAYA,CAACF,OAAO,EAAEwI,IAAI,EAAE;IACxB,OAAO,CAAC,EAAExI,OAAO,IAAIA,OAAO,CAAC+B,OAAO,KAAK/B,OAAO,CAAC6I,OAAO,IAAKL,IAAI,IAAIA,IAAI,CAACE,SAAU,CAAC,CAAC;EAC1F;EACA;IAAS,IAAI,CAACvM,IAAI,YAAA2M,0BAAAzM,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuM,iBAAiB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACvB,KAAK,kBA1mB6ElR,EAAE,CAAAmR,kBAAA;MAAAC,KAAA,EA0mBYqB,iBAAiB;MAAA1N,OAAA,EAAjB0N,iBAAiB,CAAAzM,IAAA;MAAAlB,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA5mBoGvF,EAAE,CAAA2G,iBAAA,CA4mBX8L,iBAAiB,EAAc,CAAC;IAC/GlM,IAAI,EAAE/F,UAAU;IAChBoG,IAAI,EAAE,CAAC;MAAE9B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAM8N,OAAO,CAAC;EACV;IAAS,IAAI,CAAC5M,IAAI,YAAA6M,gBAAA3M,iBAAA;MAAA,YAAAA,iBAAA,IAAwF0M,OAAO;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACE,IAAI,kBAxnB8E9S,EAAE,CAAA+S,iBAAA;MAAAxM,IAAA,EAwnBJqM,OAAO;MAAAI,SAAA;MAAAC,SAAA;MAAAC,UAAA;IAAA,EAA8G;EAAE;AACzN;AACA;EAAA,QAAA3N,SAAA,oBAAAA,SAAA,KA1nBoGvF,EAAE,CAAA2G,iBAAA,CA0nBXiM,OAAO,EAAc,CAAC;IACrGrM,IAAI,EAAE9F,SAAS;IACfmG,IAAI,EAAE,CAAC;MACCuM,QAAQ,EAAE,uBAAuB;MACjCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAW,CAAC;MAC7BF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC9C;EACA;EACAF,KAAK,CAACG,OAAO,CAACC,IAAI,CAACpR,SAAS,CAACgR,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IAAE5G;EAAO,CAAC,KAAK;IAC3D6G,QAAQ,CAACL,OAAO,EAAE,GAAGC,MAAM,SAAS,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAE,GAAGC,MAAM,SAAS,EAAE,KAAK,CAAC;IAC5CI,QAAQ,CAACL,OAAO,EAAE,GAAGC,MAAM,aAAa,EAAE,KAAK,CAAC;IAChD,IAAIzG,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;MAC9B6G,QAAQ,CAACL,OAAO,EAAE,GAAGC,MAAM,IAAIzG,MAAM,OAAO,EAAE,IAAI,CAAC;IACvD,CAAC,MACI,IAAIA,MAAM,GAAG,CAAC,EAAE;MACjB6G,QAAQ,CAACL,OAAO,EAAE,GAAGC,MAAM,aAAa,EAAE,IAAI,CAAC;IACnD;EACJ,CAAC,CAAC;AACN;AACA;AACA,SAASI,QAAQA,CAACL,OAAO,EAAEM,SAAS,EAAEC,KAAK,EAAE;EACzCP,OAAO,CAAC7K,aAAa,CAAClB,SAAS,CAACuM,MAAM,CAACF,SAAS,EAAEC,KAAK,CAAC;AAC5D;AACA,MAAME,aAAa,CAAC;EAChB;IAAS,IAAI,CAAChO,IAAI,YAAAiO,sBAAA/N,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8N,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAAC3N,IAAI,kBA3pB8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EA2pBSyN;IAAa,EAA6E;EAAE;EACvM;IAAS,IAAI,CAACxN,IAAI,kBA5pB8ExG,EAAE,CAAAyG,gBAAA;MAAAC,OAAA,GA4pBkC1B,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9K;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA9pBoGvF,EAAE,CAAA2G,iBAAA,CA8pBXqN,aAAa,EAAc,CAAC;IAC3GzN,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,EAAE4N,OAAO,CAAC;MACnC/L,OAAO,EAAE,CAAC+L,OAAO,EAAE5N,eAAe;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,IAAIkP,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACvDA,WAAW,CAACA,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACnDA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzDA,WAAW,CAACA,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACrD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZlP,WAAWA,CAACmP,SAAS,EACrB;EACAb,OAAO,EACP;EACAc,MAAM,EACN;EACAC,oCAAoC,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACb,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACc,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,oCAAoC,GAAGA,oCAAoC;IAChF;IACA,IAAI,CAACC,KAAK,GAAGL,WAAW,CAACM,MAAM;EACnC;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACL,SAAS,CAACM,aAAa,CAAC,IAAI,CAAC;EACtC;AACJ;;AAEA;AACA,MAAMC,8BAA8B,GAAG5S,+BAA+B,CAAC;EACnE6S,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMC,kBAAkB,CAAC;EACrB7P,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8P,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACC,qBAAqB,GAAIC,KAAK,IAAK;MACpC,MAAMC,MAAM,GAAGnT,eAAe,CAACkT,KAAK,CAAC;MACrC,IAAIC,MAAM,EAAE;QACR,IAAI,CAACJ,OAAO,CAACK,GAAG,CAACF,KAAK,CAAC3O,IAAI,CAAC,EAAEyE,OAAO,CAAC,CAACqK,QAAQ,EAAE9B,OAAO,KAAK;UACzD,IAAIA,OAAO,KAAK4B,MAAM,IAAI5B,OAAO,CAAC+B,QAAQ,CAACH,MAAM,CAAC,EAAE;YAChDE,QAAQ,CAACrK,OAAO,CAACuK,OAAO,IAAIA,OAAO,CAACC,WAAW,CAACN,KAAK,CAAC,CAAC;UAC3D;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;EACL;EACA;EACAO,UAAUA,CAACC,MAAM,EAAE3P,IAAI,EAAEwN,OAAO,EAAEgC,OAAO,EAAE;IACvC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAACrP,IAAI,CAAC;IAC/C,IAAI4P,gBAAgB,EAAE;MAClB,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC7B,OAAO,CAAC;MACxD,IAAIqC,kBAAkB,EAAE;QACpBA,kBAAkB,CAACnO,GAAG,CAAC8N,OAAO,CAAC;MACnC,CAAC,MACI;QACDI,gBAAgB,CAACE,GAAG,CAACtC,OAAO,EAAE,IAAIuC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC,MACI;MACD,IAAI,CAACR,OAAO,CAACc,GAAG,CAAC9P,IAAI,EAAE,IAAIiP,GAAG,CAAC,CAAC,CAACzB,OAAO,EAAE,IAAIuC,GAAG,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChEG,MAAM,CAACK,iBAAiB,CAAC,MAAM;QAC3BC,QAAQ,CAACC,gBAAgB,CAAClQ,IAAI,EAAE,IAAI,CAACkP,qBAAqB,EAAEN,8BAA8B,CAAC;MAC/F,CAAC,CAAC;IACN;EACJ;EACA;EACAuB,aAAaA,CAACnQ,IAAI,EAAEwN,OAAO,EAAEgC,OAAO,EAAE;IAClC,MAAMI,gBAAgB,GAAG,IAAI,CAACZ,OAAO,CAACK,GAAG,CAACrP,IAAI,CAAC;IAC/C,IAAI,CAAC4P,gBAAgB,EAAE;MACnB;IACJ;IACA,MAAMC,kBAAkB,GAAGD,gBAAgB,CAACP,GAAG,CAAC7B,OAAO,CAAC;IACxD,IAAI,CAACqC,kBAAkB,EAAE;MACrB;IACJ;IACAA,kBAAkB,CAACO,MAAM,CAACZ,OAAO,CAAC;IAClC,IAAIK,kBAAkB,CAACQ,IAAI,KAAK,CAAC,EAAE;MAC/BT,gBAAgB,CAACQ,MAAM,CAAC5C,OAAO,CAAC;IACpC;IACA,IAAIoC,gBAAgB,CAACS,IAAI,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACrB,OAAO,CAACoB,MAAM,CAACpQ,IAAI,CAAC;MACzBiQ,QAAQ,CAACK,mBAAmB,CAACtQ,IAAI,EAAE,IAAI,CAACkP,qBAAqB,EAAEN,8BAA8B,CAAC;IAClG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2B,4BAA4B,GAAG;EACjCC,aAAa,EAAE,GAAG;EAClBC,YAAY,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,GAAG;AACpC;AACA,MAAMC,4BAA4B,GAAG3U,+BAA+B,CAAC;EACjE6S,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAM8B,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AACrD;AACA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,aAAa,GAAG,IAAIhC,kBAAkB,CAAC,CAAC;EAAE;EACxD7P,WAAWA,CAAC8R,OAAO,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,SAAS,EAAE;IAC1D,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAIpC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACqC,0BAA0B,GAAG,KAAK;IACvC;IACA,IAAIH,SAAS,CAACrR,SAAS,EAAE;MACrB,IAAI,CAACyR,iBAAiB,GAAGnV,aAAa,CAAC8U,mBAAmB,CAAC;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEpD,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMqD,aAAa,GAAI,IAAI,CAACC,cAAc,GACtC,IAAI,CAACA,cAAc,IAAI,IAAI,CAACL,iBAAiB,CAACM,qBAAqB,CAAC,CAAE;IAC1E,MAAMC,eAAe,GAAG;MAAE,GAAGvB,4BAA4B;MAAE,GAAGjC,MAAM,CAACyD;IAAU,CAAC;IAChF,IAAIzD,MAAM,CAAC0D,QAAQ,EAAE;MACjBP,CAAC,GAAGE,aAAa,CAACM,IAAI,GAAGN,aAAa,CAACO,KAAK,GAAG,CAAC;MAChDR,CAAC,GAAGC,aAAa,CAACQ,GAAG,GAAGR,aAAa,CAACS,MAAM,GAAG,CAAC;IACpD;IACA,MAAMC,MAAM,GAAG/D,MAAM,CAAC+D,MAAM,IAAIC,wBAAwB,CAACb,CAAC,EAAEC,CAAC,EAAEC,aAAa,CAAC;IAC7E,MAAMY,OAAO,GAAGd,CAAC,GAAGE,aAAa,CAACM,IAAI;IACtC,MAAMO,OAAO,GAAGd,CAAC,GAAGC,aAAa,CAACQ,GAAG;IACrC,MAAM3B,aAAa,GAAGsB,eAAe,CAACtB,aAAa;IACnD,MAAMiC,MAAM,GAAGxC,QAAQ,CAACzO,aAAa,CAAC,KAAK,CAAC;IAC5CiR,MAAM,CAAChR,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC1C+Q,MAAM,CAAC5K,KAAK,CAACoK,IAAI,GAAG,GAAGM,OAAO,GAAGF,MAAM,IAAI;IAC3CI,MAAM,CAAC5K,KAAK,CAACsK,GAAG,GAAG,GAAGK,OAAO,GAAGH,MAAM,IAAI;IAC1CI,MAAM,CAAC5K,KAAK,CAACuK,MAAM,GAAG,GAAGC,MAAM,GAAG,CAAC,IAAI;IACvCI,MAAM,CAAC5K,KAAK,CAACqK,KAAK,GAAG,GAAGG,MAAM,GAAG,CAAC,IAAI;IACtC;IACA;IACA,IAAI/D,MAAM,CAAC/L,KAAK,IAAI,IAAI,EAAE;MACtBkQ,MAAM,CAAC5K,KAAK,CAAC6K,eAAe,GAAGpE,MAAM,CAAC/L,KAAK;IAC/C;IACAkQ,MAAM,CAAC5K,KAAK,CAAC8K,kBAAkB,GAAG,GAAGnC,aAAa,IAAI;IACtD,IAAI,CAACe,iBAAiB,CAAC5P,WAAW,CAAC8Q,MAAM,CAAC;IAC1C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAGC,MAAM,CAAChR,gBAAgB,CAAC4Q,MAAM,CAAC;IACtD,MAAMK,sBAAsB,GAAGF,cAAc,CAACG,kBAAkB;IAChE,MAAMC,sBAAsB,GAAGJ,cAAc,CAACD,kBAAkB;IAChE;IACA;IACA;IACA;IACA;IACA,MAAMM,mCAAmC,GAAGH,sBAAsB,KAAK,MAAM;IACzE;IACA;IACAE,sBAAsB,KAAK,IAAI,IAC/BA,sBAAsB,KAAK,QAAQ;IACnC;IACCrB,aAAa,CAACO,KAAK,KAAK,CAAC,IAAIP,aAAa,CAACS,MAAM,KAAK,CAAE;IAC7D;IACA,MAAMc,SAAS,GAAG,IAAI9E,SAAS,CAAC,IAAI,EAAEqE,MAAM,EAAEnE,MAAM,EAAE2E,mCAAmC,CAAC;IAC1F;IACA;IACA;IACA;IACAR,MAAM,CAAC5K,KAAK,CAACsL,SAAS,GAAG,kBAAkB;IAC3CD,SAAS,CAAC1E,KAAK,GAAGL,WAAW,CAACiF,SAAS;IACvC,IAAI,CAAC9E,MAAM,CAAC+E,UAAU,EAAE;MACpB,IAAI,CAACC,0BAA0B,GAAGJ,SAAS;IAC/C;IACA,IAAIK,cAAc,GAAG,IAAI;IACzB;IACA;IACA,IAAI,CAACN,mCAAmC,KAAKzC,aAAa,IAAIsB,eAAe,CAACrB,YAAY,CAAC,EAAE;MACzF,IAAI,CAACQ,OAAO,CAACjB,iBAAiB,CAAC,MAAM;QACjC,MAAMwD,eAAe,GAAGA,CAAA,KAAM,IAAI,CAACC,uBAAuB,CAACP,SAAS,CAAC;QACrE,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM,IAAI,CAACC,cAAc,CAACT,SAAS,CAAC;QAC/DT,MAAM,CAACvC,gBAAgB,CAAC,eAAe,EAAEsD,eAAe,CAAC;QACzD;QACA;QACA;QACAf,MAAM,CAACvC,gBAAgB,CAAC,kBAAkB,EAAEwD,kBAAkB,CAAC;QAC/DH,cAAc,GAAG;UAAEC,eAAe;UAAEE;QAAmB,CAAC;MAC5D,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACrC,cAAc,CAACvB,GAAG,CAACoD,SAAS,EAAEK,cAAc,CAAC;IAClD;IACA;IACA,IAAIN,mCAAmC,IAAI,CAACzC,aAAa,EAAE;MACvD,IAAI,CAACiD,uBAAuB,CAACP,SAAS,CAAC;IAC3C;IACA,OAAOA,SAAS;EACpB;EACA;EACAvE,aAAaA,CAACuE,SAAS,EAAE;IACrB;IACA,IAAIA,SAAS,CAAC1E,KAAK,KAAKL,WAAW,CAACyF,UAAU,IAAIV,SAAS,CAAC1E,KAAK,KAAKL,WAAW,CAACM,MAAM,EAAE;MACtF;IACJ;IACA,MAAMoF,QAAQ,GAAGX,SAAS,CAAC1F,OAAO;IAClC,MAAMsE,eAAe,GAAG;MAAE,GAAGvB,4BAA4B;MAAE,GAAG2C,SAAS,CAAC5E,MAAM,CAACyD;IAAU,CAAC;IAC1F;IACA;IACA8B,QAAQ,CAAChM,KAAK,CAAC8K,kBAAkB,GAAG,GAAGb,eAAe,CAACrB,YAAY,IAAI;IACvEoD,QAAQ,CAAChM,KAAK,CAACiM,OAAO,GAAG,GAAG;IAC5BZ,SAAS,CAAC1E,KAAK,GAAGL,WAAW,CAACyF,UAAU;IACxC;IACA;IACA,IAAIV,SAAS,CAAC3E,oCAAoC,IAAI,CAACuD,eAAe,CAACrB,YAAY,EAAE;MACjF,IAAI,CAACgD,uBAAuB,CAACP,SAAS,CAAC;IAC3C;EACJ;EACA;EACAa,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC/O,OAAO,CAACwN,MAAM,IAAIA,MAAM,CAAC/D,OAAO,CAAC,CAAC,CAAC;EAChE;EACA;EACAuF,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACD,iBAAiB,CAAC,CAAC,CAAC/O,OAAO,CAACwN,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,CAACnE,MAAM,CAAC+E,UAAU,EAAE;QAC3BZ,MAAM,CAAC/D,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACA;EACAwF,kBAAkBA,CAAChD,mBAAmB,EAAE;IACpC,MAAM1D,OAAO,GAAGpR,aAAa,CAAC8U,mBAAmB,CAAC;IAClD,IAAI,CAAC,IAAI,CAACC,SAAS,CAACrR,SAAS,IAAI,CAAC0N,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC2G,eAAe,EAAE;MAC3E;IACJ;IACA;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACD,eAAe,GAAG3G,OAAO;IAC9B;IACA;IACAoD,iBAAiB,CAAC3L,OAAO,CAACzE,IAAI,IAAI;MAC9BsQ,cAAc,CAACC,aAAa,CAACrB,UAAU,CAAC,IAAI,CAACuB,OAAO,EAAEzQ,IAAI,EAAEgN,OAAO,EAAE,IAAI,CAAC;IAC9E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIiC,WAAWA,CAACN,KAAK,EAAE;IACf,IAAIA,KAAK,CAAC3O,IAAI,KAAK,WAAW,EAAE;MAC5B,IAAI,CAAC6T,YAAY,CAAClF,KAAK,CAAC;IAC5B,CAAC,MACI,IAAIA,KAAK,CAAC3O,IAAI,KAAK,YAAY,EAAE;MAClC,IAAI,CAAC8T,aAAa,CAACnF,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACoF,YAAY,CAAC,CAAC;IACvB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACjD,0BAA0B,EAAE;MAClC;MACA;MACA;MACA;MACA;MACA,IAAI,CAACL,OAAO,CAACjB,iBAAiB,CAAC,MAAM;QACjCa,eAAe,CAAC5L,OAAO,CAACzE,IAAI,IAAI;UAC5B,IAAI,CAAC2T,eAAe,CAACjE,gBAAgB,CAAC1P,IAAI,EAAE,IAAI,EAAEmQ,4BAA4B,CAAC;QACnF,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACW,0BAA0B,GAAG,IAAI;IAC1C;EACJ;EACA;EACAmC,uBAAuBA,CAACP,SAAS,EAAE;IAC/B,IAAIA,SAAS,CAAC1E,KAAK,KAAKL,WAAW,CAACiF,SAAS,EAAE;MAC3C,IAAI,CAACoB,uBAAuB,CAACtB,SAAS,CAAC;IAC3C,CAAC,MACI,IAAIA,SAAS,CAAC1E,KAAK,KAAKL,WAAW,CAACyF,UAAU,EAAE;MACjD,IAAI,CAACD,cAAc,CAACT,SAAS,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACIsB,uBAAuBA,CAACtB,SAAS,EAAE;IAC/B,MAAMuB,2BAA2B,GAAGvB,SAAS,KAAK,IAAI,CAACI,0BAA0B;IACjF,MAAM;MAAED;IAAW,CAAC,GAAGH,SAAS,CAAC5E,MAAM;IACvC4E,SAAS,CAAC1E,KAAK,GAAGL,WAAW,CAACuG,OAAO;IACrC;IACA;IACA;IACA;IACA,IAAI,CAACrB,UAAU,KAAK,CAACoB,2BAA2B,IAAI,CAAC,IAAI,CAACrD,cAAc,CAAC,EAAE;MACvE8B,SAAS,CAACxE,OAAO,CAAC,CAAC;IACvB;EACJ;EACA;EACAiF,cAAcA,CAACT,SAAS,EAAE;IACtB,MAAMK,cAAc,GAAG,IAAI,CAAClC,cAAc,CAAChC,GAAG,CAAC6D,SAAS,CAAC,IAAI,IAAI;IACjE,IAAI,CAAC7B,cAAc,CAACjB,MAAM,CAAC8C,SAAS,CAAC;IACrC;IACA,IAAI,CAAC,IAAI,CAAC7B,cAAc,CAAChB,IAAI,EAAE;MAC3B,IAAI,CAACuB,cAAc,GAAG,IAAI;IAC9B;IACA;IACA;IACA,IAAIsB,SAAS,KAAK,IAAI,CAACI,0BAA0B,EAAE;MAC/C,IAAI,CAACA,0BAA0B,GAAG,IAAI;IAC1C;IACAJ,SAAS,CAAC1E,KAAK,GAAGL,WAAW,CAACM,MAAM;IACpC,IAAI8E,cAAc,KAAK,IAAI,EAAE;MACzBL,SAAS,CAAC1F,OAAO,CAAC8C,mBAAmB,CAAC,eAAe,EAAEiD,cAAc,CAACC,eAAe,CAAC;MACtFN,SAAS,CAAC1F,OAAO,CAAC8C,mBAAmB,CAAC,kBAAkB,EAAEiD,cAAc,CAACG,kBAAkB,CAAC;IAChG;IACAR,SAAS,CAAC1F,OAAO,CAACzL,MAAM,CAAC,CAAC;EAC9B;EACA;EACAsS,YAAYA,CAAClF,KAAK,EAAE;IAChB;IACA;IACA,MAAMwF,eAAe,GAAGpZ,+BAA+B,CAAC4T,KAAK,CAAC;IAC9D,MAAMyF,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,IAC9CzM,IAAI,CAAC0M,GAAG,CAAC,CAAC,GAAG,IAAI,CAACD,oBAAoB,GAAGnE,wBAAwB;IACrE,IAAI,CAAC,IAAI,CAACM,OAAO,CAAC+D,cAAc,IAAI,CAACJ,eAAe,IAAI,CAACC,gBAAgB,EAAE;MACvE,IAAI,CAACxD,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACI,YAAY,CAACrC,KAAK,CAAC6F,OAAO,EAAE7F,KAAK,CAAC8F,OAAO,EAAE,IAAI,CAACjE,OAAO,CAACkE,YAAY,CAAC;IAC9E;EACJ;EACA;EACAZ,aAAaA,CAACnF,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC6B,OAAO,CAAC+D,cAAc,IAAI,CAACvZ,gCAAgC,CAAC2T,KAAK,CAAC,EAAE;MAC1E;MACA;MACA;MACA,IAAI,CAAC0F,oBAAoB,GAAGzM,IAAI,CAAC0M,GAAG,CAAC,CAAC;MACtC,IAAI,CAAC1D,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,MAAM+D,OAAO,GAAGhG,KAAK,CAACiG,cAAc;MACpC;MACA;MACA,IAAID,OAAO,EAAE;QACT,KAAK,IAAI/N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+N,OAAO,CAACnO,MAAM,EAAEI,CAAC,EAAE,EAAE;UACrC,IAAI,CAACoK,YAAY,CAAC2D,OAAO,CAAC/N,CAAC,CAAC,CAAC4N,OAAO,EAAEG,OAAO,CAAC/N,CAAC,CAAC,CAAC6N,OAAO,EAAE,IAAI,CAACjE,OAAO,CAACkE,YAAY,CAAC;QACxF;MACJ;IACJ;EACJ;EACA;EACAX,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACnD,cAAc,EAAE;MACtB;IACJ;IACA,IAAI,CAACA,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAAC4C,iBAAiB,CAAC,CAAC,CAAC/O,OAAO,CAACwN,MAAM,IAAI;MACvC;MACA;MACA,MAAM4C,SAAS,GAAG5C,MAAM,CAACjE,KAAK,KAAKL,WAAW,CAACuG,OAAO,IACjDjC,MAAM,CAACnE,MAAM,CAACgH,oBAAoB,IAAI7C,MAAM,CAACjE,KAAK,KAAKL,WAAW,CAACiF,SAAU;MAClF,IAAI,CAACX,MAAM,CAACnE,MAAM,CAAC+E,UAAU,IAAIgC,SAAS,EAAE;QACxC5C,MAAM,CAAC/D,OAAO,CAAC,CAAC;MACpB;IACJ,CAAC,CAAC;EACN;EACAsF,iBAAiBA,CAAA,EAAG;IAChB,OAAO7M,KAAK,CAACoO,IAAI,CAAC,IAAI,CAAClE,cAAc,CAACmE,IAAI,CAAC,CAAC,CAAC;EACjD;EACA;EACApB,oBAAoBA,CAAA,EAAG;IACnB,MAAMqB,OAAO,GAAG,IAAI,CAACtB,eAAe;IACpC,IAAIsB,OAAO,EAAE;MACT7E,iBAAiB,CAAC3L,OAAO,CAACzE,IAAI,IAAIsQ,cAAc,CAACC,aAAa,CAACZ,aAAa,CAAC3P,IAAI,EAAEiV,OAAO,EAAE,IAAI,CAAC,CAAC;MAClG,IAAI,IAAI,CAACnE,0BAA0B,EAAE;QACjCT,eAAe,CAAC5L,OAAO,CAACzE,IAAI,IAAIiV,OAAO,CAACnF,mBAAmB,CAAC9P,IAAI,EAAE,IAAI,EAAEmQ,4BAA4B,CAAC,CAAC;QACtG,IAAI,CAACW,0BAA0B,GAAG,KAAK;MAC3C;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA,SAASgB,wBAAwBA,CAACb,CAAC,EAAEC,CAAC,EAAEgE,IAAI,EAAE;EAC1C,MAAMC,KAAK,GAAGC,IAAI,CAAChP,GAAG,CAACgP,IAAI,CAACC,GAAG,CAACpE,CAAC,GAAGiE,IAAI,CAACzD,IAAI,CAAC,EAAE2D,IAAI,CAACC,GAAG,CAACpE,CAAC,GAAGiE,IAAI,CAACI,KAAK,CAAC,CAAC;EACzE,MAAMC,KAAK,GAAGH,IAAI,CAAChP,GAAG,CAACgP,IAAI,CAACC,GAAG,CAACnE,CAAC,GAAGgE,IAAI,CAACvD,GAAG,CAAC,EAAEyD,IAAI,CAACC,GAAG,CAACnE,CAAC,GAAGgE,IAAI,CAACM,MAAM,CAAC,CAAC;EACzE,OAAOJ,IAAI,CAACK,IAAI,CAACN,KAAK,GAAGA,KAAK,GAAGI,KAAK,GAAGA,KAAK,CAAC;AACnD;;AAEA;AACA,MAAMG,yBAAyB,GAAG,IAAI/b,cAAc,CAAC,2BAA2B,CAAC;AACjF,MAAMgc,SAAS,CAAC;EACZ;AACJ;AACA;AACA;EACI,IAAI5Y,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC4E,SAAS;EACzB;EACA,IAAI5E,QAAQA,CAAC6E,KAAK,EAAE;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC6R,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,CAAC9R,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACgU,4BAA4B,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACI,IAAIX,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACY,QAAQ,IAAI,IAAI,CAAC3T,WAAW,CAACC,aAAa;EAC1D;EACA,IAAI8S,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACY,QAAQ,GAAGZ,OAAO;IACvB,IAAI,CAACW,4BAA4B,CAAC,CAAC;EACvC;EACAlX,WAAWA,CAACwD,WAAW,EAAEiN,MAAM,EAAElQ,QAAQ,EAAE6W,aAAa,EAAEC,cAAc,EAAE;IACtE,IAAI,CAAC7T,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC6T,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAClE,MAAM,GAAG,CAAC;IACf,IAAI,CAAClQ,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACsC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC+R,cAAc,GAAGF,aAAa,IAAI,CAAC,CAAC;IACzC,IAAI,CAACG,eAAe,GAAG,IAAI3F,cAAc,CAAC,IAAI,EAAEnB,MAAM,EAAEjN,WAAW,EAAEjD,QAAQ,CAAC;EAClF;EACAiX,QAAQA,CAAA,EAAG;IACP,IAAI,CAACjS,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2R,4BAA4B,CAAC,CAAC;EACvC;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,eAAe,CAACrC,oBAAoB,CAAC,CAAC;EAC/C;EACA;EACAL,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC0C,eAAe,CAAC1C,UAAU,CAAC,CAAC;EACrC;EACA;EACAE,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACwC,eAAe,CAACxC,uBAAuB,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACI,IAAIiB,YAAYA,CAAA,EAAG;IACf,OAAO;MACHlD,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBK,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB9P,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBwP,SAAS,EAAE;QACP,GAAG,IAAI,CAACyE,cAAc,CAACzE,SAAS;QAChC,IAAI,IAAI,CAACwE,cAAc,KAAK,gBAAgB,GAAG;UAAE/F,aAAa,EAAE,CAAC;UAAEC,YAAY,EAAE;QAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1F,GAAG,IAAI,CAACsB;MACZ,CAAC;MACDuD,oBAAoB,EAAE,IAAI,CAACkB,cAAc,CAAClB;IAC9C,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,IAAIP,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxX,QAAQ,IAAI,CAAC,CAAC,IAAI,CAACiZ,cAAc,CAACjZ,QAAQ;EAC1D;EACA;EACA6Y,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAAC7Y,QAAQ,IAAI,IAAI,CAACkH,cAAc,EAAE;MACvC,IAAI,CAACgS,eAAe,CAACvC,kBAAkB,CAAC,IAAI,CAACuB,OAAO,CAAC;IACzD;EACJ;EACA;EACAmB,MAAMA,CAACC,SAAS,EAAEnF,CAAC,GAAG,CAAC,EAAEpD,MAAM,EAAE;IAC7B,IAAI,OAAOuI,SAAS,KAAK,QAAQ,EAAE;MAC/B,OAAO,IAAI,CAACJ,eAAe,CAACjF,YAAY,CAACqF,SAAS,EAAEnF,CAAC,EAAE;QAAE,GAAG,IAAI,CAACwD,YAAY;QAAE,GAAG5G;MAAO,CAAC,CAAC;IAC/F,CAAC,MACI;MACD,OAAO,IAAI,CAACmI,eAAe,CAACjF,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;QAAE,GAAG,IAAI,CAAC0D,YAAY;QAAE,GAAG2B;MAAU,CAAC,CAAC;IAC1F;EACJ;EACA;IAAS,IAAI,CAAC5W,IAAI,YAAA6W,kBAAA3W,iBAAA;MAAA,YAAAA,iBAAA,IAAwFgW,SAAS,EA/qCnBlc,EAAE,CAAA8c,iBAAA,CA+qCmC9c,EAAE,CAACoB,UAAU,GA/qClDpB,EAAE,CAAA8c,iBAAA,CA+qC6D9c,EAAE,CAACmB,MAAM,GA/qCxEnB,EAAE,CAAA8c,iBAAA,CA+qCmFlb,IAAI,CAACC,QAAQ,GA/qClG7B,EAAE,CAAA8c,iBAAA,CA+qC6Gb,yBAAyB,MA/qCxIjc,EAAE,CAAA8c,iBAAA,CA+qCmKpc,qBAAqB;IAAA,CAA4D;EAAE;EACxV;IAAS,IAAI,CAACoS,IAAI,kBAhrC8E9S,EAAE,CAAA+S,iBAAA;MAAAxM,IAAA,EAgrCJ2V,SAAS;MAAAlJ,SAAA;MAAAC,SAAA;MAAA8J,QAAA;MAAAC,YAAA,WAAAC,uBAAAja,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhrCPhD,EAAE,CAAAkd,WAAA,yBAAAja,GAAA,CAAAka,SAgrCI,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAA9U,KAAA;QAAA6U,SAAA;QAAApF,QAAA;QAAAK,MAAA;QAAAN,SAAA;QAAAxU,QAAA;QAAAkY,OAAA;MAAA;MAAA6B,QAAA;MAAAnK,UAAA;IAAA,EAAmgB;EAAE;AAChnB;AACA;EAAA,QAAA3N,SAAA,oBAAAA,SAAA,KAlrCoGvF,EAAE,CAAA2G,iBAAA,CAkrCXuV,SAAS,EAAc,CAAC;IACvG3V,IAAI,EAAE9F,SAAS;IACfmG,IAAI,EAAE,CAAC;MACCuM,QAAQ,EAAE,2BAA2B;MACrCkK,QAAQ,EAAE,WAAW;MACrBjK,IAAI,EAAE;QACF,OAAO,EAAE,YAAY;QACrB,8BAA8B,EAAE;MACpC,CAAC;MACDF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3M,IAAI,EAAEvG,EAAE,CAACoB;EAAW,CAAC,EAAE;IAAEmF,IAAI,EAAEvG,EAAE,CAACmB;EAAO,CAAC,EAAE;IAAEoF,IAAI,EAAE3E,IAAI,CAACC;EAAS,CAAC,EAAE;IAAE0E,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtHR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAACqV,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAE1V,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAAClG,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4H,KAAK,EAAE,CAAC;MACjC/B,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEuW,SAAS,EAAE,CAAC;MACZ5W,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEmR,QAAQ,EAAE,CAAC;MACXxR,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEwR,MAAM,EAAE,CAAC;MACT7R,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEkR,SAAS,EAAE,CAAC;MACZvR,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEtD,QAAQ,EAAE,CAAC;MACXiD,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE4U,OAAO,EAAE,CAAC;MACVjV,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0W,eAAe,CAAC;EAClB;IAAS,IAAI,CAACtX,IAAI,YAAAuX,wBAAArX,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoX,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACjX,IAAI,kBAhuC8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EAguCS+W;IAAe,EAAiF;EAAE;EAC7M;IAAS,IAAI,CAAC9W,IAAI,kBAjuC8ExG,EAAE,CAAAyG,gBAAA;MAAAC,OAAA,GAiuCoC1B,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAChL;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAnuCoGvF,EAAE,CAAA2G,iBAAA,CAmuCX2W,eAAe,EAAc,CAAC;IAC7G/W,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,EAAEkX,SAAS,CAAC;MACrCrV,OAAO,EAAE,CAACqV,SAAS,EAAElX,eAAe;IACxC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwY,iBAAiB,CAAC;EACpBvY,WAAWA,CAACqX,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAAC/H,KAAK,GAAG,WAAW;IACxB;IACA,IAAI,CAACjR,QAAQ,GAAG,KAAK;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACma,UAAU,GAAG,MAAM;EAC5B;EACA;IAAS,IAAI,CAACzX,IAAI,YAAA0X,0BAAAxX,iBAAA;MAAA,YAAAA,iBAAA,IAAwFsX,iBAAiB,EArwC3Bxd,EAAE,CAAA8c,iBAAA,CAqwC2Cpc,qBAAqB;IAAA,CAA4D;EAAE;EAChO;IAAS,IAAI,CAACid,IAAI,kBAtwC8E3d,EAAE,CAAA4d,iBAAA;MAAArX,IAAA,EAswCJiX,iBAAiB;MAAAxK,SAAA;MAAAC,SAAA;MAAA8J,QAAA;MAAAC,YAAA,WAAAa,+BAAA7a,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtwCfhD,EAAE,CAAAkd,WAAA,sCAAAja,GAAA,CAAAsR,KAAA,KAswCM,eAAM,CAAC,gCAAAtR,GAAA,CAAAsR,KAAA,KAAP,SAAM,CAAC,iCAAAtR,GAAA,CAAAK,QAAD,CAAC,gCAAAL,GAAA,CAAAwa,UAAA,KAAF,SAAC,CAAC,6BAAAxa,GAAA,CAAAwa,UAAA,KAAF,MAAC,CAAC,4BAAAxa,GAAA,CAAAqZ,cAAA,KAAE,gBAAH,CAAC;QAAA;MAAA;MAAAc,MAAA;QAAA7I,KAAA;QAAAjR,QAAA;QAAAma,UAAA;MAAA;MAAAvK,UAAA;MAAA4K,QAAA,GAtwCf9d,EAAE,CAAA+d,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAnb,EAAA,EAAAC,GAAA;MAAAmb,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAswCqsH;EAAE;AAC7yH;AACA;EAAA,QAAA/Y,SAAA,oBAAAA,SAAA,KAxwCoGvF,EAAE,CAAA2G,iBAAA,CAwwCX6W,iBAAiB,EAAc,CAAC;IAC/GjX,IAAI,EAAE3F,SAAS;IACfgG,IAAI,EAAE,CAAC;MAAEyX,aAAa,EAAExd,iBAAiB,CAAC0d,IAAI;MAAED,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAErL,QAAQ,EAAE,qBAAqB;MAAE+K,QAAQ,EAAE,EAAE;MAAE9K,IAAI,EAAE;QAC1I,OAAO,EAAE,qBAAqB;QAC9B,2CAA2C,EAAE,2BAA2B;QACxE,qCAAqC,EAAE,qBAAqB;QAC5D,sCAAsC,EAAE,UAAU;QAClD,qCAAqC,EAAE,0BAA0B;QACjE,kCAAkC,EAAE,uBAAuB;QAC3D,iCAAiC,EAAE;MACvC,CAAC;MAAEF,UAAU,EAAE,IAAI;MAAEkL,MAAM,EAAE,CAAC,y9FAAy9F;IAAE,CAAC;EACtgG,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7X,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAAClG,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE6T,KAAK,EAAE,CAAC;MACjChO,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE2C,QAAQ,EAAE,CAAC;MACXiD,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE8c,UAAU,EAAE,CAAC;MACblX,IAAI,EAAE5F;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8d,uBAAuB,CAAC;EAC1B;IAAS,IAAI,CAACzY,IAAI,YAAA0Y,gCAAAxY,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuY,uBAAuB;IAAA,CAAkD;EAAE;EACrL;IAAS,IAAI,CAACpY,IAAI,kBAlyC8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EAkyCSkY;IAAuB,EAAgF;EAAE;EACpN;IAAS,IAAI,CAACjY,IAAI,kBAnyC8ExG,EAAE,CAAAyG,gBAAA;MAAAC,OAAA,GAmyC4C1B,eAAe;IAAA,EAAI;EAAE;AACvK;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAryCoGvF,EAAE,CAAA2G,iBAAA,CAqyCX8X,uBAAuB,EAAc,CAAC;IACrHlY,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC1B,eAAe,EAAEwY,iBAAiB,CAAC;MAC7C3W,OAAO,EAAE,CAAC2W,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMmB,2BAA2B,GAAG,IAAIze,cAAc,CAAC,6BAA6B,CAAC;;AAErF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI0e,wBAAwB,GAAG,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAI3e,cAAc,CAAC,aAAa,CAAC;AACtD;AACA;AACA;AACA,MAAM4e,WAAW,CAAC;EACd7Z,WAAWA,CAAC0E,MAAM,EAAE;IAChB;IACA,IAAI,CAACrG,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACyb,QAAQ,GAAG,sBAAsBH,wBAAwB,EAAE,EAAE;IAClE,IAAI,CAACI,MAAM,GAAGrV,MAAM,EAAEsV,WAAW,IAAI,KAAK;EAC9C;EACA;IAAS,IAAI,CAACjZ,IAAI,YAAAkZ,oBAAAhZ,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4Y,WAAW,EAx1CrB9e,EAAE,CAAA8c,iBAAA,CAw1CqC6B,2BAA2B;IAAA,CAA4D;EAAE;EAChO;IAAS,IAAI,CAAChB,IAAI,kBAz1C8E3d,EAAE,CAAA4d,iBAAA;MAAArX,IAAA,EAy1CJuY,WAAW;MAAA9L,SAAA;MAAAC,SAAA;MAAA8J,QAAA;MAAAC,YAAA,WAAAmC,yBAAAnc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAz1CThD,EAAE,CAAAof,WAAA,SAAAnc,GAAA,CAAA+b,MAAA,GAy1CK,IAAI,GAAG,OAAO,mBAAA/b,GAAA,CAAA+b,MAAA,GAAd,IAAI,GAAG/b,GAAA,CAAAK,QAAA,CAAA+b,QAAA,CAAkB,CAAC,qBAAApc,GAAA,CAAA+b,MAAA,GAA1B,IAAI,GAAA/b,GAAA,CAAA8b,QAAA;QAAA;MAAA;MAAA3B,MAAA;QAAApZ,KAAA;QAAAV,QAAA,8BAA2GvC,gBAAgB;MAAA;MAAAsc,QAAA;MAAAnK,UAAA;MAAA4K,QAAA,GAz1CtI9d,EAAE,CAAAsf,kBAAA,CAy1C6W,CAAC;QAAEzN,OAAO,EAAEgN,YAAY;QAAEU,WAAW,EAAET;MAAY,CAAC,CAAC,GAz1Cpa9e,EAAE,CAAAwf,wBAAA,EAAFxf,EAAE,CAAA+d,mBAAA;MAAA0B,kBAAA,EAAA9c,GAAA;MAAAqb,KAAA;MAAAC,IAAA;MAAAyB,MAAA;MAAAxB,QAAA,WAAAyB,qBAAA3c,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhD,EAAE,CAAA4f,eAAA,CAAAld,GAAA;UAAF1C,EAAE,CAAA0D,cAAA,aAy1C+lB,CAAC,aAA+C,CAAC;UAz1ClpB1D,EAAE,CAAA2D,MAAA,EAy1C2pB,CAAC;UAz1C9pB3D,EAAE,CAAA6f,YAAA,EAy1CorB,CAAC;UAz1CvrB7f,EAAE,CAAA4D,YAAA,CAy1C2rB,CAAC,CAAQ,CAAC;UAz1CvsB5D,EAAE,CAAA6f,YAAA,KAy1CqwB,CAAC;QAAA;QAAA,IAAA7c,EAAA;UAz1CxwBhD,EAAE,CAAAkd,WAAA,4BAAAja,GAAA,CAAAK,QAy1CykB,CAAC;UAz1C5kBtD,EAAE,CAAAqD,UAAA,OAAAJ,GAAA,CAAA8b,QAy1C8lB,CAAC;UAz1CjmB/e,EAAE,CAAA6D,SAAA,EAy1C2pB,CAAC;UAz1C9pB7D,EAAE,CAAA8D,kBAAA,KAAAb,GAAA,CAAAe,KAAA,KAy1C2pB,CAAC;QAAA;MAAA;MAAAoa,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAsnC;EAAE;AAC13D;AACA;EAAA,QAAA/Y,SAAA,oBAAAA,SAAA,KA31CoGvF,EAAE,CAAA2G,iBAAA,CA21CXmY,WAAW,EAAc,CAAC;IACzGvY,IAAI,EAAE3F,SAAS;IACfgG,IAAI,EAAE,CAAC;MAAEuM,QAAQ,EAAE,cAAc;MAAEkK,QAAQ,EAAE,aAAa;MAAEgB,aAAa,EAAExd,iBAAiB,CAAC0d,IAAI;MAAED,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAEpL,IAAI,EAAE;QAC9I,OAAO,EAAE,kBAAkB;QAC3B,aAAa,EAAE,yBAAyB;QACxC,sBAAsB,EAAE,qCAAqC;QAC7D,wBAAwB,EAAE;MAC9B,CAAC;MAAExB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEgN,YAAY;QAAEU,WAAW,EAAET;MAAY,CAAC,CAAC;MAAE5L,UAAU,EAAE,IAAI;MAAEgL,QAAQ,EAAE,kTAAkT;MAAEE,MAAM,EAAE,CAAC,45BAA45B;IAAE,CAAC;EACz0C,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7X,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CR,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAAC+X,2BAA2B;IACtC,CAAC,EAAE;MACCpY,IAAI,EAAElG;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2D,KAAK,EAAE,CAAC;MACjCuC,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE2C,QAAQ,EAAE,CAAC;MACXiD,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC;QAAEsS,SAAS,EAAEnY;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,IAAI+e,gBAAgB,GAAG,CAAC;AACxB;AACA,MAAMC,wBAAwB,CAAC;EAC3B9a,WAAWA,CACX;EACA+a,MAAM,EACN;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI,CAACA,OAAO,CAACD,QAAQ;EAChD;EACA;EACA,IAAI5c,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC8c,SAAS;EACzB;EACA;EACA,IAAI/c,QAAQA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACS,KAAK,IAAI,IAAI,CAACA,KAAK,CAACT,QAAQ,IAAK,IAAI,CAAC4E,SAAS;EAChE;EACA,IAAI5E,QAAQA,CAAC6E,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;EAC1B;EACA;EACA,IAAIS,aAAaA,CAAA,EAAG;IAChB,OAAO,CAAC,EAAE,IAAI,CAACwX,OAAO,IAAI,IAAI,CAACA,OAAO,CAACxX,aAAa,CAAC;EACzD;EACA;EACA,IAAI0X,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,CAAC,EAAE,IAAI,CAACF,OAAO,IAAI,IAAI,CAACA,OAAO,CAACE,4BAA4B,CAAC;EACxE;EACArb,WAAWA,CAACsb,QAAQ,EAAEC,kBAAkB,EAAEJ,OAAO,EAAErc,KAAK,EAAE;IACtD,IAAI,CAACwc,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACrc,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsc,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB,IAAI,CAACvY,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwY,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,EAAE,GAAG,cAAcb,gBAAgB,EAAE,EAAE;IAC5C;IACA;IACA,IAAI,CAACc,iBAAiB,GAAG,IAAI5f,YAAY,CAAC,CAAC;IAC3C;IACA,IAAI,CAACuI,aAAa,GAAG,IAAIlH,OAAO,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIwe,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACJ,OAAO;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIK,SAASA,CAAA,EAAG;IACZ;IACA,OAAO,CAAC,IAAI,CAACC,KAAK,EAAErY,aAAa,CAACsY,WAAW,IAAI,EAAE,EAAEC,IAAI,CAAC,CAAC;EAC/D;EACA;EACAC,MAAMA,CAACC,SAAS,GAAG,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACG,kBAAkB,CAACY,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAC,QAAQA,CAACH,SAAS,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACd,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACG,kBAAkB,CAACY,YAAY,CAAC,CAAC;MACtC,IAAID,SAAS,EAAE;QACX,IAAI,CAACE,yBAAyB,CAAC,CAAC;MACpC;IACJ;EACJ;EACA;EACAE,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACpB;IACA;IACA,MAAMlO,OAAO,GAAG,IAAI,CAACmO,eAAe,CAAC,CAAC;IACtC,IAAI,OAAOnO,OAAO,CAACgO,KAAK,KAAK,UAAU,EAAE;MACrChO,OAAO,CAACgO,KAAK,CAACE,OAAO,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAClB,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACD,kBAAkB,CAACY,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIQ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACD,kBAAkB,CAACY,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACAS,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,SAAS;EACzB;EACA;EACAgB,cAAcA,CAAC5M,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAAC6M,OAAO,KAAKxf,KAAK,IAAI2S,KAAK,CAAC6M,OAAO,KAAKvf,KAAK,KAAK,CAACC,cAAc,CAACyS,KAAK,CAAC,EAAE;MAChF,IAAI,CAAC8M,qBAAqB,CAAC,CAAC;MAC5B;MACA9M,KAAK,CAAC+M,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACID,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC1e,QAAQ,EAAE;MAChB,IAAI,CAAC+c,SAAS,GAAG,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,IAAI;MACvD,IAAI,CAACG,kBAAkB,CAACY,YAAY,CAAC,CAAC;MACtC,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACAa,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5e,QAAQ,GAAG,IAAI,GAAG,GAAG;EACrC;EACA;EACAoe,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,QAAQ,CAAC7X,aAAa;EACtC;EACAyZ,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC9B,SAAS,EAAE;MAChB,MAAMS,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,KAAK,IAAI,CAACJ,oBAAoB,EAAE;QACzC,IAAI,IAAI,CAACA,oBAAoB,EAAE;UAC3B,IAAI,CAACnX,aAAa,CAACS,IAAI,CAAC,CAAC;QAC7B;QACA,IAAI,CAAC0W,oBAAoB,GAAGI,SAAS;MACzC;IACJ;EACJ;EACApE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnT,aAAa,CAAC0B,QAAQ,CAAC,CAAC;EACjC;EACA;EACAoW,yBAAyBA,CAACpB,WAAW,GAAG,KAAK,EAAE;IAC3C,IAAI,CAACW,iBAAiB,CAACwB,IAAI,CAAC,IAAIrC,wBAAwB,CAAC,IAAI,EAAEE,WAAW,CAAC,CAAC;EAChF;EACA;IAAS,IAAI,CAACja,IAAI,YAAAqc,kBAAAnc,iBAAA;MAAA,YAAAA,iBAAA,IAAwFga,SAAS,EAxiDnBlgB,EAAE,CAAA8c,iBAAA,CAwiDmC9c,EAAE,CAACoB,UAAU,GAxiDlDpB,EAAE,CAAA8c,iBAAA,CAwiD6D9c,EAAE,CAACsiB,iBAAiB,GAxiDnFtiB,EAAE,CAAA8c,iBAAA,CAwiD8F6B,2BAA2B,MAxiD3H3e,EAAE,CAAA8c,iBAAA,CAwiDsJ+B,YAAY;IAAA,CAA4D;EAAE;EAClU;IAAS,IAAI,CAAClB,IAAI,kBAziD8E3d,EAAE,CAAA4d,iBAAA;MAAArX,IAAA,EAyiDJ2Z,SAAS;MAAAlN,SAAA;MAAAuP,SAAA,WAAAC,gBAAAxf,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAziDPhD,EAAE,CAAAyiB,WAAA,CAAA7f,GAAA;QAAA;QAAA,IAAAI,EAAA;UAAA,IAAA0f,EAAA;UAAF1iB,EAAE,CAAA2iB,cAAA,CAAAD,EAAA,GAAF1iB,EAAE,CAAA4iB,WAAA,QAAA3f,GAAA,CAAA8d,KAAA,GAAA2B,EAAA,CAAA1W,KAAA;QAAA;MAAA;MAAAiH,SAAA,WAyiDkO,QAAQ;MAAA8J,QAAA;MAAAC,YAAA,WAAA6F,uBAAA7f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAziD5OhD,EAAE,CAAA8iB,UAAA,mBAAAC,mCAAA;YAAA,OAyiDJ9f,GAAA,CAAA+e,qBAAA,CAAsB,CAAC;UAAA,CAAf,CAAC,qBAAAgB,qCAAAC,MAAA;YAAA,OAAThgB,GAAA,CAAA6e,cAAA,CAAAmB,MAAqB,CAAC;UAAA,CAAd,CAAC;QAAA;QAAA,IAAAjgB,EAAA;UAziDPhD,EAAE,CAAAkjB,cAAA,OAAAjgB,GAAA,CAAA0d,EAyiDI,CAAC;UAziDP3gB,EAAE,CAAAof,WAAA,kBAAAnc,GAAA,CAAAM,QAAA,mBAyiDJN,GAAA,CAAAK,QAAA,CAAA+b,QAAA,CAAkB,CAAC;UAziDjBrf,EAAE,CAAAkd,WAAA,4BAAAja,GAAA,CAAAM,QAyiDI,CAAC,4BAAAN,GAAA,CAAAkd,QAAD,CAAC,0BAAAld,GAAA,CAAA4d,MAAD,CAAC,4BAAA5d,GAAA,CAAAK,QAAD,CAAC;QAAA;MAAA;MAAA8Z,MAAA;QAAAjV,KAAA;QAAAwY,EAAA;QAAArd,QAAA,8BAAqHvC,gBAAgB;MAAA;MAAAoiB,OAAA;QAAAvC,iBAAA;MAAA;MAAAvD,QAAA;MAAAnK,UAAA;MAAA4K,QAAA,GAziD5I9d,EAAE,CAAAwf,wBAAA,EAAFxf,EAAE,CAAA+d,mBAAA;MAAA0B,kBAAA,EAAA3c,GAAA;MAAAkb,KAAA;MAAAC,IAAA;MAAAyB,MAAA;MAAAxB,QAAA,WAAAkF,mBAAApgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhD,EAAE,CAAA4f,eAAA,CAAA/c,GAAA;UAAF7C,EAAE,CAAAqjB,UAAA,IAAAtgB,gCAAA,gCAyiDwsC,CAAC;UAziD3sC/C,EAAE,CAAA6f,YAAA,EAyiDs9C,CAAC;UAziDz9C7f,EAAE,CAAA0D,cAAA,gBAyiD4gD,CAAC;UAziD/gD1D,EAAE,CAAA6f,YAAA,KAyiDqiD,CAAC;UAziDxiD7f,EAAE,CAAA4D,YAAA,CAyiD4iD,CAAC;UAziD/iD5D,EAAE,CAAAqjB,UAAA,IAAA7f,gCAAA,gCAyiD0qD,CAAC,IAAAC,gCAAA,iBAA6U,CAAC;UAziD3/DzD,EAAE,CAAAkD,SAAA,YAyiDywE,CAAC;QAAA;QAAA,IAAAF,EAAA;UAziD5wEhD,EAAE,CAAAsjB,aAAA,CAAArgB,GAAA,CAAAkd,QAAA,SAyiDq6C,CAAC;UAziDx6CngB,EAAE,CAAA6D,SAAA,EAyiD04D,CAAC;UAziD74D7D,EAAE,CAAAsjB,aAAA,EAAArgB,GAAA,CAAAkd,QAAA,IAAAld,GAAA,CAAAM,QAAA,KAAAN,GAAA,CAAAqd,4BAAA,SAyiD04D,CAAC;UAziD74DtgB,EAAE,CAAA6D,SAAA,CAyiD+jE,CAAC;UAziDlkE7D,EAAE,CAAAsjB,aAAA,CAAArgB,GAAA,CAAAc,KAAA,IAAAd,GAAA,CAAAc,KAAA,CAAAib,MAAA,SAyiD+jE,CAAC;UAziDlkEhf,EAAE,CAAA6D,SAAA,CAyiD8sE,CAAC;UAziDjtE7D,EAAE,CAAAqD,UAAA,qBAAAJ,GAAA,CAAAye,eAAA,EAyiD8sE,CAAC,sBAAAze,GAAA,CAAAK,QAAA,IAAAL,GAAA,CAAA2F,aAAiD,CAAC;QAAA;MAAA;MAAA2a,YAAA,GAAqvG/F,iBAAiB,EAA6GtB,SAAS;MAAAkC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AACliM;AACA;EAAA,QAAA/Y,SAAA,oBAAAA,SAAA,KA3iDoGvF,EAAE,CAAA2G,iBAAA,CA2iDXuZ,SAAS,EAAc,CAAC;IACvG3Z,IAAI,EAAE3F,SAAS;IACfgG,IAAI,EAAE,CAAC;MAAEuM,QAAQ,EAAE,YAAY;MAAEkK,QAAQ,EAAE,WAAW;MAAEjK,IAAI,EAAE;QAClD,MAAM,EAAE,QAAQ;QAChB,iCAAiC,EAAE,UAAU;QAC7C,iCAAiC,EAAE,UAAU;QAC7C,+BAA+B,EAAE,QAAQ;QACzC,iCAAiC,EAAE,UAAU;QAC7C,MAAM,EAAE,IAAI;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sBAAsB,EAAE,UAAU;QAClC,sBAAsB,EAAE,qBAAqB;QAC7C,SAAS,EAAE,yBAAyB;QACpC,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE;MACb,CAAC;MAAEiL,aAAa,EAAExd,iBAAiB,CAAC0d,IAAI;MAAED,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAEtL,UAAU,EAAE,IAAI;MAAExM,OAAO,EAAE,CAAC8W,iBAAiB,EAAEtB,SAAS,CAAC;MAAEgC,QAAQ,EAAE,49CAA49C;MAAEE,MAAM,EAAE,CAAC,irGAAirG;IAAE,CAAC;EACv0J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7X,IAAI,EAAEvG,EAAE,CAACoB;EAAW,CAAC,EAAE;IAAEmF,IAAI,EAAEvG,EAAE,CAACsiB;EAAkB,CAAC,EAAE;IAAE/b,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxGR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAAC+X,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAEpY,IAAI,EAAEuY,WAAW;IAAE/X,UAAU,EAAE,CAAC;MACpCR,IAAI,EAAElG;IACV,CAAC,EAAE;MACCkG,IAAI,EAAEjG,MAAM;MACZsG,IAAI,EAAE,CAACiY,YAAY;IACvB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE1W,KAAK,EAAE,CAAC;MACjC5B,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAEggB,EAAE,EAAE,CAAC;MACLpa,IAAI,EAAE5F;IACV,CAAC,CAAC;IAAE2C,QAAQ,EAAE,CAAC;MACXiD,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC;QAAEsS,SAAS,EAAEnY;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6f,iBAAiB,EAAE,CAAC;MACpBra,IAAI,EAAEtF;IACV,CAAC,CAAC;IAAE8f,KAAK,EAAE,CAAC;MACRxa,IAAI,EAAErF,SAAS;MACf0F,IAAI,EAAE,CAAC,MAAM,EAAE;QAAE4c,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,WAAW,EAAEjC,OAAO,EAAEkC,YAAY,EAAE;EACvE,IAAIA,YAAY,CAAC5W,MAAM,EAAE;IACrB,IAAI6W,YAAY,GAAGnC,OAAO,CAACoC,OAAO,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAGH,YAAY,CAACE,OAAO,CAAC,CAAC;IACnC,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI5W,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuW,WAAW,GAAG,CAAC,EAAEvW,CAAC,EAAE,EAAE;MACtC,IAAIyW,YAAY,CAACzW,CAAC,CAAC,CAACpJ,KAAK,IAAI6f,YAAY,CAACzW,CAAC,CAAC,CAACpJ,KAAK,KAAK+f,MAAM,CAACC,YAAY,CAAC,EAAE;QACzEA,YAAY,EAAE;MAClB;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,YAAY,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,WAAW,EAAE;EAC9F,IAAIH,YAAY,GAAGE,qBAAqB,EAAE;IACtC,OAAOF,YAAY;EACvB;EACA,IAAIA,YAAY,GAAGC,YAAY,GAAGC,qBAAqB,GAAGC,WAAW,EAAE;IACnE,OAAOzI,IAAI,CAAChP,GAAG,CAAC,CAAC,EAAEsX,YAAY,GAAGG,WAAW,GAAGF,YAAY,CAAC;EACjE;EACA,OAAOC,qBAAqB;AAChC;AAEA,MAAME,eAAe,CAAC;EAClB;IAAS,IAAI,CAACre,IAAI,YAAAse,wBAAApe,iBAAA;MAAA,YAAAA,iBAAA,IAAwFme,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAAChe,IAAI,kBAnoD8ErG,EAAE,CAAAsG,gBAAA;MAAAC,IAAA,EAmoDS8d;IAAe,EAAoI;EAAE;EAChQ;IAAS,IAAI,CAAC7d,IAAI,kBApoD8ExG,EAAE,CAAAyG,gBAAA;MAAAC,OAAA,GAooDoC4W,eAAe,EAAEtY,eAAe,EAAEyZ,uBAAuB;IAAA,EAAI;EAAE;AACzM;AACA;EAAA,QAAAlZ,SAAA,oBAAAA,SAAA,KAtoDoGvF,EAAE,CAAA2G,iBAAA,CAsoDX0d,eAAe,EAAc,CAAC;IAC7G9d,IAAI,EAAEnG,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCF,OAAO,EAAE,CAAC4W,eAAe,EAAEtY,eAAe,EAAEyZ,uBAAuB,EAAEyB,SAAS,EAAEpB,WAAW,CAAC;MAC5FjY,OAAO,EAAE,CAACqZ,SAAS,EAAEpB,WAAW;IACpC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMyF,oBAAoB,GAAG;EAAE1P,OAAO,EAAE;AAAK,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA,MAAM2P,uBAAuB,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;AAClF;AACA,MAAMC,sBAAsB,GAAG,iCAAiC;AAChE;AACA,MAAMC,kBAAkB,GAAG,8BAA8B;AACzD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA,MAAMC,iBAAiB,GAAG,4BAA4B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB5f,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,SAAS,GAAGjF,MAAM,CAACwB,QAAQ,EAAE;MAAE8D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,IAAI,CAAC6W,cAAc,GAAGnc,MAAM,CAACO,qBAAqB,EAAE;MAAE+E,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACqf,oBAAoB,GAAG3kB,MAAM,CAAC8b,yBAAyB,EAAE;MAAExW,QAAQ,EAAE;IAAK,CAAC,CAAC;IACjF,IAAI,CAACyR,SAAS,GAAG/W,MAAM,CAAC0B,QAAQ,CAAC;IACjC,IAAI,CAACmV,OAAO,GAAG7W,MAAM,CAACgB,MAAM,CAAC;IAC7B,IAAI,CAAC4jB,MAAM,GAAG,IAAI/P,GAAG,CAAC,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACgQ,cAAc,GAAI9P,KAAK,IAAK;MAC7B,MAAM+P,WAAW,GAAGjjB,eAAe,CAACkT,KAAK,CAAC;MAC1C,IAAI+P,WAAW,YAAYC,WAAW,EAAE;QACpC;QACA,MAAM3R,OAAO,GAAG0R,WAAW,CAACE,OAAO,CAAC,IAAIV,sBAAsB,KAAK,IAAI,CAACK,oBAAoB,EAAEM,SAAS,IAAI,EAAE,IAAI,CAAC;QAClH,IAAI7R,OAAO,EAAE;UACT,IAAI,CAAC8R,aAAa,CAAC9R,OAAO,CAAC;QAC/B;MACJ;IACJ,CAAC;IACD,IAAI,CAACyD,OAAO,CAACjB,iBAAiB,CAAC,MAAM;MACjC,KAAK,MAAMb,KAAK,IAAIsP,uBAAuB,EAAE;QACzC,IAAI,CAACpf,SAAS,EAAE6Q,gBAAgB,CAACf,KAAK,EAAE,IAAI,CAAC8P,cAAc,EAAET,oBAAoB,CAAC;MACtF;IACJ,CAAC,CAAC;EACN;EACA7H,WAAWA,CAAA,EAAG;IACV,MAAM4I,KAAK,GAAG,IAAI,CAACP,MAAM,CAACxJ,IAAI,CAAC,CAAC;IAChC,KAAK,MAAMnI,IAAI,IAAIkS,KAAK,EAAE;MACtB,IAAI,CAACC,aAAa,CAACnS,IAAI,CAAC;IAC5B;IACA,KAAK,MAAM8B,KAAK,IAAIsP,uBAAuB,EAAE;MACzC,IAAI,CAACpf,SAAS,EAAEiR,mBAAmB,CAACnB,KAAK,EAAE,IAAI,CAAC8P,cAAc,EAAET,oBAAoB,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,eAAeA,CAACpS,IAAI,EAAEiB,MAAM,EAAE;IAC1B;IACAjB,IAAI,CAACqS,YAAY,CAAChB,sBAAsB,EAAE,IAAI,CAACK,oBAAoB,EAAEM,SAAS,IAAI,EAAE,CAAC;IACrF;IACA,IAAI/Q,MAAM,CAACR,SAAS,IAAI,CAACT,IAAI,CAACsS,YAAY,CAAChB,kBAAkB,CAAC,EAAE;MAC5DtR,IAAI,CAACqS,YAAY,CAACf,kBAAkB,EAAErQ,MAAM,CAACR,SAAS,IAAI,EAAE,CAAC;IACjE;IACA;IACA,IAAIQ,MAAM,CAAC0D,QAAQ,EAAE;MACjB3E,IAAI,CAACqS,YAAY,CAACd,iBAAiB,EAAE,EAAE,CAAC;IAC5C;IACA,IAAItQ,MAAM,CAAC/Q,QAAQ,EAAE;MACjB8P,IAAI,CAACqS,YAAY,CAACb,iBAAiB,EAAE,EAAE,CAAC;IAC5C;EACJ;EACA;EACAe,SAASA,CAACvS,IAAI,EAAE;IACZ,MAAMoF,MAAM,GAAG,IAAI,CAACuM,MAAM,CAAC3P,GAAG,CAAChC,IAAI,CAAC;IACpC,OAAOoF,MAAM,IAAI,IAAI,CAAC6M,aAAa,CAACjS,IAAI,CAAC;EAC7C;EACA;EACAwS,WAAWA,CAACxS,IAAI,EAAE9P,QAAQ,EAAE;IACxB,MAAMkV,MAAM,GAAG,IAAI,CAACuM,MAAM,CAAC3P,GAAG,CAAChC,IAAI,CAAC;IACpC;IACA,IAAIoF,MAAM,EAAE;MACRA,MAAM,CAAClV,QAAQ,GAAGA,QAAQ;MAC1B;IACJ;IACA;IACA;IACA,IAAIA,QAAQ,EAAE;MACV8P,IAAI,CAACqS,YAAY,CAACb,iBAAiB,EAAE,EAAE,CAAC;IAC5C,CAAC,MACI;MACDxR,IAAI,CAACyS,eAAe,CAACjB,iBAAiB,CAAC;IAC3C;EACJ;EACA;EACAS,aAAaA,CAACjS,IAAI,EAAE;IAChB,IAAI,CAAC,IAAI,CAAChO,SAAS,EAAE;MACjB;IACJ;IACA,MAAM0gB,cAAc,GAAG,IAAI,CAACf,MAAM,CAAC3P,GAAG,CAAChC,IAAI,CAAC;IAC5C,IAAI0S,cAAc,EAAE;MAChB,OAAOA,cAAc;IACzB;IACA;IACA1S,IAAI,CAAC2S,aAAa,CAAC,aAAa,CAAC,EAAEje,MAAM,CAAC,CAAC;IAC3C,MAAM8R,QAAQ,GAAG,IAAI,CAACxU,SAAS,CAACmC,aAAa,CAAC,MAAM,CAAC;IACrDqS,QAAQ,CAACpS,SAAS,CAACC,GAAG,CAAC,YAAY,EAAE2L,IAAI,CAAC4S,YAAY,CAACtB,kBAAkB,CAAC,CAAC;IAC3EtR,IAAI,CAAC6S,MAAM,CAACrM,QAAQ,CAAC;IACrB;IACA,MAAMpB,MAAM,GAAG,IAAI0D,SAAS,CAAC,IAAI9a,UAAU,CAACwY,QAAQ,CAAC,EAAE,IAAI,CAAC5C,OAAO,EAAE,IAAI,CAACE,SAAS,EAAE,IAAI,CAAC4N,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,GAAGhe,SAAS,EAAE,IAAI,CAACwV,cAAc,GAAG,IAAI,CAACA,cAAc,GAAGxV,SAAS,CAAC;IAC9M0R,MAAM,CAAChO,cAAc,GAAG,IAAI;IAC5BgO,MAAM,CAACgD,OAAO,GAAGpI,IAAI;IACrBoF,MAAM,CAACT,QAAQ,GAAG3E,IAAI,CAACsS,YAAY,CAACf,iBAAiB,CAAC;IACtDnM,MAAM,CAAClV,QAAQ,GAAG8P,IAAI,CAACsS,YAAY,CAACd,iBAAiB,CAAC;IACtD,IAAI,CAACsB,YAAY,CAAC9S,IAAI,EAAEoF,MAAM,CAAC;IAC/B,OAAOA,MAAM;EACjB;EACA0N,YAAYA,CAAC9S,IAAI,EAAEoF,MAAM,EAAE;IACvBpF,IAAI,CAACyS,eAAe,CAACpB,sBAAsB,CAAC;IAC5C,IAAI,CAACM,MAAM,CAAClP,GAAG,CAACzC,IAAI,EAAEoF,MAAM,CAAC;EACjC;EACA+M,aAAaA,CAACnS,IAAI,EAAE;IAChB,MAAMoF,MAAM,GAAG,IAAI,CAACuM,MAAM,CAAC3P,GAAG,CAAChC,IAAI,CAAC;IACpC,IAAIoF,MAAM,EAAE;MACR;MACA;MACAA,MAAM,CAACkE,WAAW,CAAC,CAAC;MACpB,IAAI,CAACqI,MAAM,CAAC5O,MAAM,CAAC/C,IAAI,CAAC;IAC5B;EACJ;EACA;IAAS,IAAI,CAACpN,IAAI,YAAAmgB,wBAAAjgB,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2e,eAAe;IAAA,CAAoD;EAAE;EAC/K;IAAS,IAAI,CAAC3T,KAAK,kBA3xD6ElR,EAAE,CAAAmR,kBAAA;MAAAC,KAAA,EA2xDYyT,eAAe;MAAA9f,OAAA,EAAf8f,eAAe,CAAA7e,IAAA;MAAAlB,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KA7xDoGvF,EAAE,CAAA2G,iBAAA,CA6xDXke,eAAe,EAAc,CAAC;IAC7Gte,IAAI,EAAE/F,UAAU;IAChBoG,IAAI,EAAE,CAAC;MAAE9B,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMshB,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAACpgB,IAAI,YAAAqgB,8BAAAngB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkgB,qBAAqB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACzI,IAAI,kBAzyD8E3d,EAAE,CAAA4d,iBAAA;MAAArX,IAAA,EAyyDJ6f,qBAAqB;MAAApT,SAAA;MAAAC,SAAA;MAAA8J,QAAA;MAAAC,YAAA,WAAAsJ,mCAAAtjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzyDnBhD,EAAE,CAAAkd,WAAA,8BAAAja,GAAA,CAAAsjB,aAAA,KAyyDc,QAAE,CAAC;QAAA;MAAA;MAAAnJ,MAAA;QAAAmJ,aAAA;MAAA;MAAArT,UAAA;MAAA4K,QAAA,GAzyDnB9d,EAAE,CAAA+d,mBAAA;MAAAyI,KAAA,EAAAviB,GAAA;MAAAwb,kBAAA,EAAAvb,GAAA;MAAA8Z,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAuI,+BAAAzjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhD,EAAE,CAAA4f,eAAA;UAAF5f,EAAE,CAAA6f,YAAA,EAyyDqU,CAAC;QAAA;MAAA;MAAAzB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6uC;EAAE;AAC3pD;AACA;EAAA,QAAA/Y,SAAA,oBAAAA,SAAA,KA3yDoGvF,EAAE,CAAA2G,iBAAA,CA2yDXyf,qBAAqB,EAAc,CAAC;IACnH7f,IAAI,EAAE3F,SAAS;IACfgG,IAAI,EAAE,CAAC;MAAEuM,QAAQ,EAAE,8BAA8B;MAAED,UAAU,EAAE,IAAI;MAAEgL,QAAQ,EAAE,2BAA2B;MAAEG,aAAa,EAAExd,iBAAiB,CAAC0d,IAAI;MAAED,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAEpL,IAAI,EAAE;QAC9L,OAAO,EAAE,wCAAwC;QACjD,mCAAmC,EAAE;MACzC,CAAC;MAAEgL,MAAM,EAAE,CAAC,+mCAA+mC;IAAE,CAAC;EAC1oC,CAAC,CAAC,QAAkB;IAAEmI,aAAa,EAAE,CAAC;MAC9BhgB,IAAI,EAAE5F,KAAK;MACXiG,IAAI,EAAE,CAAC;QAAE8f,QAAQ,EAAE;MAAK,CAAC;IAC7B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASviB,eAAe,EAAEK,kBAAkB,EAAE4G,WAAW,EAAEqH,iBAAiB,EAAE5N,sBAAsB,EAAE+H,gBAAgB,EAAE1B,eAAe,EAAEC,uBAAuB,EAAEkG,uBAAuB,EAAEwN,YAAY,EAAEF,2BAA2B,EAAE1C,yBAAyB,EAAEjX,eAAe,EAAE4N,OAAO,EAAEoB,aAAa,EAAEjC,mBAAmB,EAAE+M,WAAW,EAAEoB,SAAS,EAAEmE,eAAe,EAAEtE,wBAAwB,EAAEvC,iBAAiB,EAAEiB,uBAAuB,EAAEvC,SAAS,EAAE2I,eAAe,EAAEvH,eAAe,EAAElQ,iBAAiB,EAAEsE,gBAAgB,EAAEyC,SAAS,EAAE0C,cAAc,EAAE3C,WAAW,EAAE9B,4BAA4B,EAAE3Q,OAAO,EAAEyH,kBAAkB,EAAEkd,qBAAqB,EAAE3C,6BAA6B,EAAEO,wBAAwB,EAAE1N,4BAA4B,EAAElO,UAAU,EAAEO,kBAAkB,EAAEX,aAAa,EAAEiC,eAAe,EAAEM,gBAAgB,EAAEzB,aAAa,EAAEmJ,wBAAwB,EAAEoB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}