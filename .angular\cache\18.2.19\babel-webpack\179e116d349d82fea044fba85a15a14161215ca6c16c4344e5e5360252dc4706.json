{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MutationObserverFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  _mutationObserverFactory = inject(MutationObserverFactory);\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  _observedElements = new Map();\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContentObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  _contentObserver = inject(ContentObserver);\n  _elementRef = inject(ElementRef);\n  /** Event emitted for each change in the element's content. */\n  event = new EventEmitter();\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  _disabled = false;\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  _debounce;\n  _currentSubscription = null;\n  constructor() {}\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkObserveContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"],\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ObserversModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "NgZone", "ElementRef", "EventEmitter", "booleanAttribute", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "map", "filter", "debounceTime", "c", "coerceNumberProperty", "a", "coerceElement", "shouldIgnoreRecord", "record", "type", "target", "Comment", "i", "addedNodes", "length", "removedNodes", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "MutationObserverFactory_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "ContentObserver", "_mutationObserverFactory", "_observedElements", "Map", "_ngZone", "constructor", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "pipe", "records", "subscribe", "run", "next", "unsubscribe", "_unobserveElement", "runOutsideAngular", "has", "mutations", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "ContentObserver_Factory", "CdkObserveContent", "_contentObserver", "_elementRef", "event", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_currentSubscription", "ngAfterContentInit", "CdkObserveContent_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "selector", "alias", "transform", "ObserversModule", "ObserversModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { c as coerceNumberProperty, a as coerceElement } from './element-x4z00URv.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n    // Ignore changes to comment text.\n    if (record.type === 'characterData' && record.target instanceof Comment) {\n        return true;\n    }\n    // Ignore addition / removal of comments.\n    if (record.type === 'childList') {\n        for (let i = 0; i < record.addedNodes.length; i++) {\n            if (!(record.addedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        for (let i = 0; i < record.removedNodes.length; i++) {\n            if (!(record.removedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Observe everything else.\n    return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    _mutationObserverFactory = inject(MutationObserverFactory);\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    _observedElements = new Map();\n    _ngZone = inject(NgZone);\n    constructor() { }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream\n                .pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length))\n                .subscribe(records => {\n                this._ngZone.run(() => {\n                    observer.next(records);\n                });\n            });\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._observedElements.has(element)) {\n                const stream = new Subject();\n                const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n                if (observer) {\n                    observer.observe(element, {\n                        characterData: true,\n                        childList: true,\n                        subtree: true,\n                    });\n                }\n                this._observedElements.set(element, { observer, stream, count: 1 });\n            }\n            else {\n                this._observedElements.get(element).count++;\n            }\n            return this._observedElements.get(element).stream;\n        });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    _contentObserver = inject(ContentObserver);\n    _elementRef = inject(ElementRef);\n    /** Event emitted for each change in the element's content. */\n    event = new EventEmitter();\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    _disabled = false;\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    _debounce;\n    _currentSubscription = null;\n    constructor() { }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkObserveContent, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkObserveContent, isStandalone: true, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\", booleanAttribute], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: () => [], propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkObserveContentDisabled', transform: booleanAttribute }]\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, imports: [CdkObserveContent], exports: [CdkObserveContent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkObserveContent],\n                    exports: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1I,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;AAC1D,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,wBAAwB;;AAEtF;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC;EACA,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,IAAID,MAAM,CAACE,MAAM,YAAYC,OAAO,EAAE;IACrE,OAAO,IAAI;EACf;EACA;EACA,IAAIH,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IAC7B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,EAAEJ,MAAM,CAACK,UAAU,CAACD,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC5C,OAAO,KAAK;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACO,YAAY,CAACD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,EAAEJ,MAAM,CAACO,YAAY,CAACH,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMK,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;EAC1F;EACA,OAAOE,IAAI,YAAAC,gCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFN,uBAAuB;EAAA;EAC1H,OAAOO,KAAK,kBAD6EpC,EAAE,CAAAqC,kBAAA;IAAAC,KAAA,EACYT,uBAAuB;IAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;IAAAO,UAAA,EAAc;EAAM;AACtJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FzC,EAAE,CAAA0C,iBAAA,CAGJb,uBAAuB,EAAc,CAAC;IACrHP,IAAI,EAAErB,UAAU;IAChB0C,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,eAAe,CAAC;EAClBC,wBAAwB,GAAG3C,MAAM,CAAC2B,uBAAuB,CAAC;EAC1D;EACAiB,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7BC,OAAO,GAAG9C,MAAM,CAACC,MAAM,CAAC;EACxB8C,WAAWA,CAAA,EAAG,CAAE;EAChBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,iBAAiB,CAACK,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EAClF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAGlC,aAAa,CAACqC,YAAY,CAAC;IAC3C,OAAO,IAAI7C,UAAU,CAAE8C,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;MAC5C,MAAMO,YAAY,GAAGF,MAAM,CACtBG,IAAI,CAAChD,GAAG,CAACiD,OAAO,IAAIA,OAAO,CAAChD,MAAM,CAACO,MAAM,IAAI,CAACD,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACgD,OAAO,IAAI,CAAC,CAACA,OAAO,CAACnC,MAAM,CAAC,CAAC,CAChHoC,SAAS,CAACD,OAAO,IAAI;QACtB,IAAI,CAACd,OAAO,CAACgB,GAAG,CAAC,MAAM;UACnBP,QAAQ,CAACQ,IAAI,CAACH,OAAO,CAAC;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAO,MAAM;QACTF,YAAY,CAACM,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,iBAAiB,CAACd,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,eAAeA,CAACN,OAAO,EAAE;IACrB,OAAO,IAAI,CAACL,OAAO,CAACoB,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACtB,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;QACtC,MAAMK,MAAM,GAAG,IAAI9C,OAAO,CAAC,CAAC;QAC5B,MAAM6C,QAAQ,GAAG,IAAI,CAACZ,wBAAwB,CAACf,MAAM,CAACwC,SAAS,IAAIZ,MAAM,CAACO,IAAI,CAACK,SAAS,CAAC,CAAC;QAC1F,IAAIb,QAAQ,EAAE;UACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;YACtBkB,aAAa,EAAE,IAAI;YACnBC,SAAS,EAAE,IAAI;YACfC,OAAO,EAAE;UACb,CAAC,CAAC;QACN;QACA,IAAI,CAAC3B,iBAAiB,CAAC4B,GAAG,CAACrB,OAAO,EAAE;UAAEI,QAAQ;UAAEC,MAAM;UAAEiB,KAAK,EAAE;QAAE,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;MAC/C;MACA,OAAO,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACK,MAAM;IACrD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIS,iBAAiBA,CAACd,OAAO,EAAE;IACvB,IAAI,IAAI,CAACP,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;MACrC,IAAI,CAACP,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAAC7B,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;QAC5C,IAAI,CAACrB,gBAAgB,CAACD,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACP,iBAAiB,CAACuB,GAAG,CAAChB,OAAO,CAAC,EAAE;MACrC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACZ,iBAAiB,CAAC8B,GAAG,CAACvB,OAAO,CAAC;MAChE,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACoB,UAAU,CAAC,CAAC;MACzB;MACAnB,MAAM,CAACoB,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAChC,iBAAiB,CAACiC,MAAM,CAAC1B,OAAO,CAAC;IAC1C;EACJ;EACA,OAAOpB,IAAI,YAAA+C,wBAAA7C,iBAAA;IAAA,YAAAA,iBAAA,IAAwFS,eAAe;EAAA;EAClH,OAAOR,KAAK,kBAlF6EpC,EAAE,CAAAqC,kBAAA;IAAAC,KAAA,EAkFYM,eAAe;IAAAL,OAAA,EAAfK,eAAe,CAAAX,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC9I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApF6FzC,EAAE,CAAA0C,iBAAA,CAoFJE,eAAe,EAAc,CAAC;IAC7GtB,IAAI,EAAErB,UAAU;IAChB0C,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMyC,iBAAiB,CAAC;EACpBC,gBAAgB,GAAGhF,MAAM,CAAC0C,eAAe,CAAC;EAC1CuC,WAAW,GAAGjF,MAAM,CAACE,UAAU,CAAC;EAChC;EACAgF,KAAK,GAAG,IAAI/E,YAAY,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;EACI,IAAIgF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EACAH,SAAS,GAAG,KAAK;EACjB;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAG1E,oBAAoB,CAACsE,KAAK,CAAC;IAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;EACAE,SAAS;EACTC,oBAAoB,GAAG,IAAI;EAC3B3C,WAAWA,CAAA,EAAG,CAAE;EAChB4C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACD,oBAAoB,IAAI,CAAC,IAAI,CAACP,QAAQ,EAAE;MAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ;EACAvC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsC,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAM9B,MAAM,GAAG,IAAI,CAACwB,gBAAgB,CAAC3B,OAAO,CAAC,IAAI,CAAC4B,WAAW,CAAC;IAC9D,IAAI,CAACS,oBAAoB,GAAG,CAAC,IAAI,CAACF,QAAQ,GAAGhC,MAAM,CAACG,IAAI,CAAC9C,YAAY,CAAC,IAAI,CAAC2E,QAAQ,CAAC,CAAC,GAAGhC,MAAM,EAAEK,SAAS,CAAC,IAAI,CAACqB,KAAK,CAAC;EACzH;EACAI,YAAYA,CAAA,EAAG;IACX,IAAI,CAACI,oBAAoB,EAAE1B,WAAW,CAAC,CAAC;EAC5C;EACA,OAAOjC,IAAI,YAAA6D,0BAAA3D,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8C,iBAAiB;EAAA;EACpH,OAAOc,IAAI,kBAzI8E/F,EAAE,CAAAgG,iBAAA;IAAA1E,IAAA,EAyIJ2D,iBAAiB;IAAAgB,SAAA;IAAAC,MAAA;MAAAb,QAAA,+CAAqH/E,gBAAgB;MAAAoF,QAAA;IAAA;IAAAS,OAAA;MAAAf,KAAA;IAAA;IAAAgB,QAAA;IAAAC,UAAA;IAAAC,QAAA,GAzIpJtG,EAAE,CAAAuG,wBAAA;EAAA;AA0I/F;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA3I6FzC,EAAE,CAAA0C,iBAAA,CA2IJuC,iBAAiB,EAAc,CAAC;IAC/G3D,IAAI,EAAEf,SAAS;IACfoC,IAAI,EAAE,CAAC;MACC6D,QAAQ,EAAE,qBAAqB;MAC/BJ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEhB,KAAK,EAAE,CAAC;MAChD9D,IAAI,EAAEd,MAAM;MACZmC,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE0C,QAAQ,EAAE,CAAC;MACX/D,IAAI,EAAEb,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAE8D,KAAK,EAAE,2BAA2B;QAAEC,SAAS,EAAEpG;MAAiB,CAAC;IAC9E,CAAC,CAAC;IAAEoF,QAAQ,EAAE,CAAC;MACXpE,IAAI,EAAEb;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkG,eAAe,CAAC;EAClB,OAAO1E,IAAI,YAAA2E,wBAAAzE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwE,eAAe;EAAA;EAClH,OAAOE,IAAI,kBA5J8E7G,EAAE,CAAA8G,gBAAA;IAAAxF,IAAA,EA4JSqF;EAAe;EACnH,OAAOI,IAAI,kBA7J8E/G,EAAE,CAAAgH,gBAAA;IAAAC,SAAA,EA6JqC,CAACpF,uBAAuB;EAAC;AAC7J;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA/J6FzC,EAAE,CAAA0C,iBAAA,CA+JJiE,eAAe,EAAc,CAAC;IAC7GrF,IAAI,EAAEZ,QAAQ;IACdiC,IAAI,EAAE,CAAC;MACCuE,OAAO,EAAE,CAACjC,iBAAiB,CAAC;MAC5BkC,OAAO,EAAE,CAAClC,iBAAiB,CAAC;MAC5BgC,SAAS,EAAE,CAACpF,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASoD,iBAAiB,EAAErC,eAAe,EAAEf,uBAAuB,EAAE8E,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}