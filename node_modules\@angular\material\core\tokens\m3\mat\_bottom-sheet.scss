@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, bottom-sheet);

/// Generates custom tokens for the mat-bottom-sheet.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-bottom-sheet
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-utils.generate-typography-tokens($systems, container-text, body-large),
    (
      container-shape: token-utils.hardcode(28px, $exclude-hardcoded),
      container-text-color: map.get($systems, md-sys-color, on-surface),
      container-background-color: map.get($systems, md-sys-color, surface-container-low),
    ),
  );

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
