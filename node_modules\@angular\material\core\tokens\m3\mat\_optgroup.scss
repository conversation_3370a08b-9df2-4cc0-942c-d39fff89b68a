@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, optgroup);

/// Generates custom tokens for the mat-optgroup.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-optgroup
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-utils.generate-typography-tokens($systems, label-text, title-small),
    (
      label-text-color: map.get($systems, md-sys-color, on-surface-variant),
    )
  );

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
