@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, standard-button-toggle);

/// Generates custom tokens for the mat-button-toggle.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-button-toggle
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-utils.generate-typography-tokens($systems, label-text, label-large),
  (
    shape: map.get($systems, md-sys-shape, corner-full),
    hover-state-layer-opacity: map.get($systems, md-sys-state, hover-state-layer-opacity),
    focus-state-layer-opacity: map.get($systems, md-sys-state, focus-state-layer-opacity),
    text-color: map.get($systems, md-sys-color, on-surface),
    background-color: token-utils.hardcode(transparent, $exclude-hardcoded),
    state-layer-color: map.get($systems, md-sys-color, on-surface),
    selected-state-background-color: map.get($systems, md-sys-color, secondary-container),
    selected-state-text-color: map.get($systems, md-sys-color, on-secondary-container),
    disabled-state-text-color: sass-utils.safe-color-change(
      map.get($systems, md-sys-color, on-surface),
      $alpha: 0.38,
    ),
    disabled-state-background-color: token-utils.hardcode(transparent, $exclude-hardcoded),
    disabled-selected-state-text-color: sass-utils.safe-color-change(
      map.get($systems, md-sys-color, on-surface),
      $alpha: 0.38,
    ),
    disabled-selected-state-background-color: sass-utils.safe-color-change(
      map.get($systems, md-sys-color, on-surface),
      $alpha: 0.12,
    ),
    divider-color: map.get($systems, md-sys-color, outline),
  ), (
    // Color variants:
    primary: (
      selected-state-background-color: map.get($systems, md-sys-color, primary-container),
      selected-state-text-color: map.get($systems, md-sys-color, on-primary-container),
    ),
    secondary: (), // Default, no overrides needed
    tertiary: (
      selected-state-background-color: map.get($systems, md-sys-color, tertiary-container),
      selected-state-text-color: map.get($systems, md-sys-color, on-tertiary-container),
    ),
    error: (
      selected-state-background-color: map.get($systems, md-sys-color, error-container),
      selected-state-text-color: map.get($systems, md-sys-color, on-error-container),
    )
  ));

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
