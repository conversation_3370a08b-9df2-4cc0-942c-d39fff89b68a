{"ast": null, "code": "import _asyncToGenerator from \"C:/cal_wf/Augment/5-json-pulse/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, runInInjectionContext, InjectionToken, ɵPendingTasks, PLATFORM_ID, ɵConsole, ɵformatRuntimeError, Inject, ɵRuntimeError, makeEnvironmentProviders, NgModule, TransferState, makeStateKey, ɵperformanceMarkFeature, APP_BOOTSTRAP_LISTENER, ApplicationRef, ɵwhenStable, ɵtruncateMiddle } from '@angular/core';\nimport { of, Observable, from } from 'rxjs';\nimport { concatMap, filter, map, finalize, switchMap, tap } from 'rxjs/operators';\nimport * as i1 from '@angular/common';\nimport { isPlatformServer, DOCUMENT, ɵparseCookieValue } from '@angular/common';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    this.normalizedNames = new Map();\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n    this.lazyUpdate = null;\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n            if (this.headers.has(key)) {\n              this.headers.get(key).push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map();\n      headers.forEach((values, name) => {\n        this.setHeaderEntries(name, values);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  setHeaderEntries(name, values) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map(value => value.toString());\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  constructor(options = {}) {\n    this.updates = null;\n    this.cloneFrom = null;\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  constructor() {\n    this.map = new Map();\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n    this.body = null;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    this.reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n    this.withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n    this.responseType = 'json';\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n      if (!!options.context) {\n        this.context = options.context;\n      }\n      if (!!options.params) {\n        this.params = options.params;\n      }\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    this.headers ??= new HttpHeaders();\n    // If no context have been passed in, construct a new HttpContext instance.\n    this.context ??= new HttpContext();\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (typeof this.body === 'string' || isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body)) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return 'application/json';\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = update.transferCache ?? this.transferCache;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials ?? this.withCredentials;\n    const reportProgress = update.reportProgress ?? this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.ResponseHeader;\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.Response;\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    this.name = 'HttpErrorResponse';\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n    this.ok = false;\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n  HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n  HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n  HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n  HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n  HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n  HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n  HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n  HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n  HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n  HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n  HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n  HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n  HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n  HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n  HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n  HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n  HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n  HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n  HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n  HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n  HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n  HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n  HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n  HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n  HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n  HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n  HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n  HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n  HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n  HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n  HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n  HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n  HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n  HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n  HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n  HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n  HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n  HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n  HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n  HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n  HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n  HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n  HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n  HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n  HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n  HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n  HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n  HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n  HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n  HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n  HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n * TODO(adev): review\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new Error('Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new Error('Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new Error('Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n  static {\n    this.ɵfac = function HttpClient_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpClient)(i0.ɵɵinject(HttpHandler));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpClient,\n      factory: HttpClient.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], () => [{\n    type: HttpHandler\n  }], null);\n})();\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\nconst REQUEST_URL_HEADER = `X-Request-URL`;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n  constructor() {\n    // We use an arrow function to always reference the current global implementation of `fetch`.\n    // This is helpful for cases when the global `fetch` implementation is modified by external code,\n    // see https://github.com/angular/angular/issues/57527.\n    this.fetchImpl = inject(FetchFactory, {\n      optional: true\n    })?.fetch ?? ((...args) => globalThis.fetch(...args));\n    this.ngZone = inject(NgZone);\n  }\n  handle(request) {\n    return new Observable(observer => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, error => observer.error(new HttpErrorResponse({\n        error\n      })));\n      return () => aborter.abort();\n    });\n  }\n  doRequest(request, signal, observer) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const init = _this.createRequestInit(request);\n      let response;\n      try {\n        // Run fetch outside of Angular zone.\n        // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n        // the response should eventually timeout which causes extra CD cycles every 500ms\n        const fetchPromise = _this.ngZone.runOutsideAngular(() => _this.fetchImpl(request.urlWithParams, {\n          signal,\n          ...init\n        }));\n        // Make sure Zone.js doesn't trigger false-positive unhandled promise\n        // error in case the Promise is rejected synchronously. See function\n        // description for additional information.\n        silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n        // Send the `Sent` event before awaiting the response.\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        response = yield fetchPromise;\n      } catch (error) {\n        observer.error(new HttpErrorResponse({\n          error,\n          status: error.status ?? 0,\n          statusText: error.statusText,\n          url: request.urlWithParams,\n          headers: error.headers\n        }));\n        return;\n      }\n      const headers = new HttpHeaders(response.headers);\n      const statusText = response.statusText;\n      const url = getResponseUrl$1(response) ?? request.urlWithParams;\n      let status = response.status;\n      let body = null;\n      if (request.reportProgress) {\n        observer.next(new HttpHeaderResponse({\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n      if (response.body) {\n        // Read Progress\n        const contentLength = response.headers.get('content-length');\n        const chunks = [];\n        const reader = response.body.getReader();\n        let receivedLength = 0;\n        let decoder;\n        let partialText;\n        // We have to check whether the Zone is defined in the global scope because this may be called\n        // when the zone is nooped.\n        const reqZone = typeof Zone !== 'undefined' && Zone.current;\n        // Perform response processing outside of Angular zone to\n        // ensure no excessive change detection runs are executed\n        // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n        yield _this.ngZone.runOutsideAngular(/*#__PURE__*/_asyncToGenerator(function* () {\n          while (true) {\n            const {\n              done,\n              value\n            } = yield reader.read();\n            if (done) {\n              break;\n            }\n            chunks.push(value);\n            receivedLength += value.length;\n            if (request.reportProgress) {\n              partialText = request.responseType === 'text' ? (partialText ?? '') + (decoder ??= new TextDecoder()).decode(value, {\n                stream: true\n              }) : undefined;\n              const reportProgress = () => observer.next({\n                type: HttpEventType.DownloadProgress,\n                total: contentLength ? +contentLength : undefined,\n                loaded: receivedLength,\n                partialText\n              });\n              reqZone ? reqZone.run(reportProgress) : reportProgress();\n            }\n          }\n        }));\n        // Combine all chunks.\n        const chunksAll = _this.concatChunks(chunks, receivedLength);\n        try {\n          const contentType = response.headers.get('Content-Type') ?? '';\n          body = _this.parseBody(request, chunksAll, contentType);\n        } catch (error) {\n          // Body loading or parsing failed\n          observer.error(new HttpErrorResponse({\n            error,\n            headers: new HttpHeaders(response.headers),\n            status: response.status,\n            statusText: response.statusText,\n            url: getResponseUrl$1(response) ?? request.urlWithParams\n          }));\n          return;\n        }\n      }\n      // Same behavior as the XhrBackend\n      if (status === 0) {\n        status = body ? HTTP_STATUS_CODE_OK : 0;\n      }\n      // ok determines whether the response will be transmitted on the event or\n      // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n      // but a successful status code can still result in an error if the user\n      // asked for JSON data and the body cannot be parsed as such.\n      const ok = status >= 200 && status < 300;\n      if (ok) {\n        observer.next(new HttpResponse({\n          body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n        // The full body has been received and delivered, no further events\n        // are possible. This request is complete.\n        observer.complete();\n      } else {\n        observer.error(new HttpErrorResponse({\n          error: body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n    })();\n  }\n  parseBody(request, binContent, contentType) {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n        return text === '' ? null : JSON.parse(text);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {\n          type: contentType\n        });\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n  createRequestInit(req) {\n    // We could share some of this logic with the XhrBackend\n    const headers = {};\n    const credentials = req.withCredentials ? 'include' : undefined;\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => headers[name] = values.join(','));\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has('Accept')) {\n      headers['Accept'] = 'application/json, text/plain, */*';\n    }\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has('Content-Type')) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers['Content-Type'] = detectedType;\n      }\n    }\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials\n    };\n  }\n  concatChunks(chunks, totalLength) {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n    return chunksAll;\n  }\n  static {\n    this.ɵfac = function FetchBackend_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FetchBackend)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FetchBackend,\n      factory: FetchBackend.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FetchBackend, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {}\nfunction noop() {}\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n  promise.then(noop, noop);\n}\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      }) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    const pendingTasks = inject(ɵPendingTasks);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const taskId = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\nlet fetchBackendWarningDisplayed = false;\n/** Internal function to reset the flag in tests */\nfunction resetFetchBackendWarningFlag() {\n  fetchBackendWarningDisplayed = false;\n}\nclass HttpInterceptorHandler extends HttpHandler {\n  constructor(backend, injector) {\n    super();\n    this.backend = backend;\n    this.injector = injector;\n    this.chain = null;\n    this.pendingTasks = inject(ɵPendingTasks);\n    this.contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n      if (isServer && !(this.backend instanceof FetchBackend)) {\n        fetchBackendWarningDisplayed = true;\n        injector.get(ɵConsole).warn(ɵformatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' + \"to use `fetch` APIs. It's strongly recommended to \" + 'enable `fetch` for applications that use Server-Side Rendering ' + 'for better performance and compatibility. ' + 'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' + 'call at the root of the application.'));\n      }\n    }\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set([...this.injector.get(HTTP_INTERCEPTOR_FNS), ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, [])]));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    if (this.contributeToStability) {\n      const taskId = this.pendingTasks.add();\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n    } else {\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n  }\n  static {\n    this.ɵfac = function HttpInterceptorHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpInterceptorHandler,\n      factory: HttpInterceptorHandler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: HttpBackend\n  }, {\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n    this.resolvedPromise = Promise.resolve();\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: HTTP_STATUS_CODE_OK,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument ??= this.document.implementation.createHTMLDocument();\n    foreignDocument.adoptNode(script);\n  }\n  static {\n    this.ɵfac = function JsonpClientBackend_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: JsonpClientBackend,\n      factory: JsonpClientBackend.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], () => [{\n    type: JsonpCallbackContext\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static {\n    this.ɵfac = function JsonpInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: JsonpInterceptor,\n      factory: JsonpInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new ɵRuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n    }\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory = this.xhrFactory;\n    const source = xhrFactory.ɵloadImpl ? from(xhrFactory.ɵloadImpl()) : of(null);\n    return source.pipe(switchMap(() => {\n      // Everything happens on Observable subscription.\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials\n        // flag.\n        const xhr = xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n        if (req.withCredentials) {\n          xhr.withCredentials = true;\n        }\n        // Add all the requested headers.\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has('Accept')) {\n          xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has('Content-Type')) {\n          const detectedType = req.detectContentTypeHeader();\n          // Sometimes Content-Type detection fails.\n          if (detectedType !== null) {\n            xhr.setRequestHeader('Content-Type', detectedType);\n          }\n        }\n        // Set the responseType if one was requested.\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase();\n          // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        }\n        // Serialize the request body if one is present. If not, this will be set to null.\n        const reqBody = req.serializeBody();\n        // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n        let headerResponse = null;\n        // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n          const statusText = xhr.statusText || 'OK';\n          // Parse headers from XMLHttpRequest - this step is lazy.\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n          // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n          const url = getResponseUrl(xhr) || req.url;\n          // Construct the HttpHeaderResponse and memoize it.\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        };\n        // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr();\n          // The body will be read out if present.\n          let body = null;\n          if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          }\n          // Normalize another potential bug (this one comes from CORS).\n          if (status === 0) {\n            status = !!body ? HTTP_STATUS_CODE_OK : 0;\n          }\n          // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n          let ok = status >= 200 && status < 300;\n          // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the\n              // user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have\n              // been a JSON response. Restore the original body (including any XSSI prefix)\n              // to deliver a better error response.\n              body = originalBody;\n              // If this was an error request to begin with, leave it as a string, it\n              // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false;\n                // The parse error contains the text of the body that failed to parse.\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        };\n        // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        };\n        // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n        let sentHeaders = false;\n        // The download progress event handler, which is only registered if\n        // progress events are enabled.\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          }\n          // Start building the download progress event to deliver on the response\n          // event stream.\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          };\n          // Set the total number of bytes in the event if it's available.\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          }\n          // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          }\n          // Finally, fire the event.\n          observer.next(progressEvent);\n        };\n        // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          };\n          // If the total number of bytes being uploaded is available, include\n          // it.\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          }\n          // Send the event.\n          observer.next(progress);\n        };\n        // By default, register for load and error events.\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onError);\n        xhr.addEventListener('abort', onError);\n        // Progress events are only enabled if requested.\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress);\n          // Upload progress depends on whether there is a body to upload.\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        }\n        // Fire the request, and notify the event stream that it was fired.\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        // This is the return from the Observable function, which is the\n        // request cancellation handler.\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onError);\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          }\n          // Finally, abort the in-flight request.\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function HttpXhrBackend_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpXhrBackend)(i0.ɵɵinject(i1.XhrFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpXhrBackend,\n      factory: HttpXhrBackend.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], () => [{\n    type: i1.XhrFactory\n  }], null);\n})();\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n    this.lastCookieString = '';\n    this.lastToken = null;\n    /**\n     * @internal for testing\n     */\n    this.parseCount = 0;\n  }\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n  static {\n    this.ɵfac = function HttpXsrfCookieExtractor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpXsrfCookieExtractor,\n      factory: HttpXsrfCookieExtractor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [XSRF_COOKIE_NAME]\n    }]\n  }], null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  constructor(injector) {\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static {\n    this.ɵfac = function HttpXsrfInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpXsrfInterceptor,\n      factory: HttpXsrfInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n  HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"alert is-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useFactory: () => {\n      return inject(FetchBackend, {\n        optional: true\n      }) ?? inject(HttpXhrBackend);\n    }\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @developerPreview\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [FetchBackend, {\n    provide: HttpBackend,\n    useExisting: FetchBackend\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n  static {\n    this.ɵfac = function HttpClientXsrfModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpClientXsrfModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HttpClientXsrfModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n  static {\n    this.ɵfac = function HttpClientModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpClientModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HttpClientModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the dependency injector where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n  static {\n    this.ɵfac = function HttpClientJsonpModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpClientJsonpModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HttpClientJsonpModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [withJsonpSupport().ɵproviders]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```typescript\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n  const {\n    isCacheActive,\n    ...globalOptions\n  } = inject(CACHE_OPTIONS);\n  const {\n    transferCache: requestOptions,\n    method: requestMethod\n  } = req;\n  // In the following situations we do not want to cache the request\n  if (!isCacheActive || requestOptions === false ||\n  // POST requests are allowed either globally or at request level\n  requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions || requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod) ||\n  // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n  !globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req) || globalOptions.filter?.(req) === false) {\n    return next(req);\n  }\n  const transferState = inject(TransferState);\n  const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n    optional: true\n  });\n  const isServer = isPlatformServer(inject(PLATFORM_ID));\n  if (originMap && !isServer) {\n    throw new ɵRuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode && 'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' + 'present in the client side code. Please ensure that this token is only provided in the ' + 'server code of the application.');\n  }\n  const requestUrl = isServer && originMap ? mapRequestOriginUrl(req.url, originMap) : req.url;\n  const storeKey = makeCacheKey(req, requestUrl);\n  const response = transferState.get(storeKey, null);\n  let headersToInclude = globalOptions.includeHeaders;\n  if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n    // Request-specific config takes precedence over the global config.\n    headersToInclude = requestOptions.includeHeaders;\n  }\n  if (response) {\n    const {\n      [BODY]: undecodedBody,\n      [RESPONSE_TYPE]: responseType,\n      [HEADERS]: httpHeaders,\n      [STATUS]: status,\n      [STATUS_TEXT]: statusText,\n      [REQ_URL]: url\n    } = response;\n    // Request found in cache. Respond using it.\n    let body = undecodedBody;\n    switch (responseType) {\n      case 'arraybuffer':\n        body = new TextEncoder().encode(undecodedBody).buffer;\n        break;\n      case 'blob':\n        body = new Blob([undecodedBody]);\n        break;\n    }\n    // We want to warn users accessing a header provided from the cache\n    // That HttpTransferCache alters the headers\n    // The warning will be logged a single time by HttpHeaders instance\n    let headers = new HttpHeaders(httpHeaders);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Append extra logic in dev mode to produce a warning when a header\n      // that was not transferred to the client is accessed in the code via `get`\n      // and `has` calls.\n      headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n    }\n    return of(new HttpResponse({\n      body,\n      headers,\n      status,\n      statusText,\n      url\n    }));\n  }\n  // Request not found in cache. Make the request and cache it if on the server.\n  return next(req).pipe(tap(event => {\n    if (event instanceof HttpResponse && isServer) {\n      transferState.set(storeKey, {\n        [BODY]: event.body,\n        [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n        [STATUS]: event.status,\n        [STATUS_TEXT]: event.statusText,\n        [REQ_URL]: requestUrl,\n        [RESPONSE_TYPE]: req.responseType\n      });\n    }\n  }));\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n  return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n  if (!includeHeaders) {\n    return {};\n  }\n  const headersMap = {};\n  for (const key of includeHeaders) {\n    const values = headers.getAll(key);\n    if (values !== null) {\n      headersMap[key] = values;\n    }\n  }\n  return headersMap;\n}\nfunction sortAndConcatParams(params) {\n  return [...params.keys()].sort().map(k => `${k}=${params.getAll(k)}`).join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n  // make the params encoded same as a url so it's easy to identify\n  const {\n    params,\n    method,\n    responseType\n  } = request;\n  const encodedParams = sortAndConcatParams(params);\n  let serializedBody = request.serializeBody();\n  if (serializedBody instanceof URLSearchParams) {\n    serializedBody = sortAndConcatParams(serializedBody);\n  } else if (typeof serializedBody !== 'string') {\n    serializedBody = '';\n  }\n  const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n  const hash = generateHash(key);\n  return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n  let hash = 0;\n  for (const char of value) {\n    hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;\n  }\n  // Force positive number hash.\n  // ********** = equivalent of Integer.MAX_VALUE.\n  hash += ********** + 1;\n  return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n  return [{\n    provide: CACHE_OPTIONS,\n    useFactory: () => {\n      ɵperformanceMarkFeature('NgHttpTransferCache');\n      return {\n        isCacheActive: true,\n        ...cacheOptions\n      };\n    }\n  }, {\n    provide: HTTP_ROOT_INTERCEPTOR_FNS,\n    useValue: transferCacheInterceptorFn,\n    multi: true,\n    deps: [TransferState, CACHE_OPTIONS]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: () => {\n      const appRef = inject(ApplicationRef);\n      const cacheState = inject(CACHE_OPTIONS);\n      return () => {\n        ɵwhenStable(appRef).then(() => {\n          cacheState.isCacheActive = false;\n        });\n      };\n    }\n  }];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n  const warningProduced = new Set();\n  return new Proxy(headers, {\n    get(target, prop) {\n      const value = Reflect.get(target, prop);\n      const methods = new Set(['get', 'has', 'getAll']);\n      if (typeof value !== 'function' || !methods.has(prop)) {\n        return value;\n      }\n      return headerName => {\n        // We log when the key has been removed and a warning hasn't been produced for the header\n        const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n        if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n          warningProduced.add(key);\n          const truncatedUrl = ɵtruncateMiddle(url);\n          // TODO: create Error guide for this warning\n          console.warn(ɵformatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` + `was not transferred from the server to the client by the HttpTransferCache. ` + `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` + `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` + `on a request level by adding the \\`transferCache\\` parameter, or on an application ` + `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` + `\\`provideClientHydration()\\` call. `));\n        }\n        // invoking the original method\n        return value.apply(target, [headerName]);\n      };\n    }\n  });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n  const origin = new URL(url, 'resolve://').origin;\n  const mappedOrigin = originMap[origin];\n  if (!mappedOrigin) {\n    return url;\n  }\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    verifyMappedOrigin(mappedOrigin);\n  }\n  return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n  if (new URL(url, 'resolve://').pathname !== '/') {\n    throw new ɵRuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' + `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` + 'without any other segments.');\n  }\n}\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY, withHttpTransferCache as ɵwithHttpTransferCache };", "map": {"version": 3, "names": ["i0", "Injectable", "inject", "NgZone", "runInInjectionContext", "InjectionToken", "ɵPendingTasks", "PLATFORM_ID", "ɵConsole", "ɵformatRuntimeError", "Inject", "ɵRuntimeError", "makeEnvironmentProviders", "NgModule", "TransferState", "makeStateKey", "ɵperformanceMarkFeature", "APP_BOOTSTRAP_LISTENER", "ApplicationRef", "ɵwhenStable", "ɵtruncateMiddle", "of", "Observable", "from", "concatMap", "filter", "map", "finalize", "switchMap", "tap", "i1", "isPlatformServer", "DOCUMENT", "ɵparseCookieValue", "HttpHandler", "HttpBackend", "HttpHeaders", "constructor", "headers", "normalizedNames", "Map", "lazyUpdate", "lazyInit", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "key", "toLowerCase", "value", "trim", "maybeSetNormalizedName", "has", "get", "push", "set", "Headers", "values", "setHeaderEntries", "ngDevMode", "assertValidHeaders", "Object", "entries", "init", "length", "keys", "Array", "getAll", "append", "clone", "op", "delete", "lcName", "copyFrom", "update", "applyUpdate", "other", "concat", "base", "undefined", "toDelete", "existing", "headerValues", "isArray", "toString", "fn", "Error", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "valueToString", "HttpParams", "options", "updates", "cloneFrom", "encoder", "fromString", "fromObject", "res", "appendAll", "_value", "<PERSON><PERSON><PERSON>", "join", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "HttpRequest", "url", "third", "fourth", "body", "reportProgress", "withCredentials", "responseType", "toUpperCase", "context", "transferCache", "urlWithParams", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "defaultStatus", "defaultStatusText", "status", "statusText", "ok", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "HTTP_STATUS_CODE_OK", "HTTP_STATUS_CODE_NO_CONTENT", "HttpStatusCode", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "ɵfac", "HttpClient_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "factory", "ɵsetClassMetadata", "XSSI_PREFIX$1", "REQUEST_URL_HEADER", "getResponseUrl$1", "response", "xRequestUrl", "toLocaleLowerCase", "FetchBackend", "fetchImpl", "FetchFactory", "optional", "fetch", "args", "globalThis", "ngZone", "observer", "aborter", "AbortController", "doRequest", "signal", "then", "noop", "abort", "_this", "_asyncToGenerator", "createRequestInit", "fetchPromise", "runOutsideAngular", "silenceSuperfluousUnhandledPromiseRejection", "next", "<PERSON><PERSON>", "contentLength", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "partialText", "reqZone", "Zone", "current", "done", "read", "TextDecoder", "decode", "stream", "DownloadProgress", "total", "loaded", "run", "chunksAll", "concatChunks", "contentType", "parseBody", "complete", "binContent", "text", "parse", "buffer", "credentials", "detectedType", "totalLength", "Uint8Array", "position", "chunk", "FetchBackend_Factory", "promise", "interceptorChainEndFn", "finalHandlerFn", "adaptLegacyInterceptorToChain", "chainTailFn", "interceptor", "initialRequest", "intercept", "downstreamRequest", "chainedInterceptorFn", "interceptorFn", "injector", "HTTP_INTERCEPTORS", "HTTP_INTERCEPTOR_FNS", "HTTP_ROOT_INTERCEPTOR_FNS", "REQUESTS_CONTRIBUTE_TO_STABILITY", "providedIn", "legacyInterceptorFnFactory", "chain", "interceptors", "reduceRight", "pendingTasks", "contributeToStability", "taskId", "add", "remove", "fetchBackendWarningDisplayed", "resetFetchBackendWarningFlag", "HttpInterceptorHandler", "backend", "isServer", "warn", "dedupedInterceptorFns", "Set", "nextSequencedFn", "HttpInterceptorHandler_Factory", "EnvironmentInjector", "nextRequestId", "foreignDocument", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JSONP_ERR_HEADERS_NOT_SUPPORTED", "JsonpCallbackContext", "jsonpCallbackContext", "window", "JsonpClientBackend", "callbackMap", "document", "resolvedPromise", "Promise", "resolve", "nextCallback", "callback", "node", "createElement", "src", "finished", "data", "cleanup", "removeEventListener", "onLoad", "onError", "addEventListener", "append<PERSON><PERSON><PERSON>", "removeListeners", "script", "implementation", "createHTMLDocument", "adoptNode", "JsonpClientBackend_Factory", "decorators", "jsonpInterceptorFn", "JsonpInterceptor", "JsonpInterceptor_Factory", "XSSI_PREFIX", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "source", "ɵloadImpl", "build", "open", "setRequestHeader", "reqBody", "headerResponse", "partialFromXhr", "responseText", "originalBody", "sentHeaders", "onDownProgress", "progressEvent", "lengthComputable", "onUpProgress", "progress", "UploadProgress", "upload", "send", "readyState", "DONE", "HttpXhrBackend_Factory", "XhrFactory", "XSRF_ENABLED", "XSRF_DEFAULT_COOKIE_NAME", "XSRF_COOKIE_NAME", "XSRF_DEFAULT_HEADER_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "platform", "cookieName", "lastCookieString", "lastToken", "parseCount", "getToken", "cookieString", "cookie", "HttpXsrfCookieExtractor_Factory", "xsrfInterceptorFn", "lcUrl", "startsWith", "headerName", "HttpXsrfInterceptor", "HttpXsrfInterceptor_Factory", "HttpFeatureKind", "makeHttpFeature", "kind", "providers", "ɵkind", "ɵproviders", "provideHttpClient", "features", "featureKinds", "f", "NoXsrfProtection", "CustomXsrfConfiguration", "provide", "useExisting", "useFactory", "useValue", "multi", "useClass", "feature", "withInterceptors", "interceptorFns", "Interceptors", "LEGACY_INTERCEPTOR_FN", "withInterceptorsFromDi", "LegacyInterceptors", "withXsrfConfiguration", "withNoXsrfProtection", "withJsonpSupport", "JsonpSupport", "withRequestsMadeViaParent", "RequestsMadeViaParent", "handlerFromParent", "skipSelf", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>tch", "HttpClientXsrfModule", "disable", "ngModule", "withOptions", "HttpClientXsrfModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "HttpClientModule", "HttpClientModule_Factory", "HttpClientJsonpModule", "HttpClientJsonpModule_Factory", "HTTP_TRANSFER_CACHE_ORIGIN_MAP", "BODY", "HEADERS", "STATUS", "STATUS_TEXT", "REQ_URL", "RESPONSE_TYPE", "CACHE_OPTIONS", "ALLOWED_METHODS", "transferCacheInterceptorFn", "isCacheActive", "globalOptions", "requestOptions", "requestMethod", "includePostRequests", "includes", "includeRequestsWithAuthHeaders", "hasAuthHeaders", "transferState", "originMap", "requestUrl", "mapRequestOriginUrl", "storeKey", "make<PERSON><PERSON><PERSON><PERSON>", "headersToInclude", "includeHeaders", "undecodedBody", "httpHeaders", "TextEncoder", "encode", "appendMissingHeadersDetection", "getFilteredHeaders", "headersMap", "sortAndConcatParams", "sort", "k", "mappedRequestUrl", "encodedParams", "serializedBody", "hash", "generateHash", "char", "Math", "imul", "charCodeAt", "withHttpTransferCache", "cacheOptions", "deps", "appRef", "cacheState", "warningProduced", "Proxy", "target", "prop", "Reflect", "methods", "truncatedUrl", "console", "apply", "origin", "URL", "<PERSON><PERSON><PERSON><PERSON>", "verifyMappedOrigin", "pathname", "ɵHTTP_ROOT_INTERCEPTOR_FNS", "ɵHttpInterceptingHandler", "ɵHttpInterceptorHandler", "ɵREQUESTS_CONTRIBUTE_TO_STABILITY", "ɵwithHttpTransferCache"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/common/fesm2022/http.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, runInInjectionContext, InjectionToken, ɵPendingTasks, PLATFORM_ID, ɵConsole, ɵformatRuntimeError, Inject, ɵRuntimeError, makeEnvironmentProviders, NgModule, TransferState, makeStateKey, ɵperformanceMarkFeature, APP_BOOTSTRAP_LISTENER, ApplicationRef, ɵwhenStable, ɵtruncateMiddle } from '@angular/core';\nimport { of, Observable, from } from 'rxjs';\nimport { concatMap, filter, map, finalize, switchMap, tap } from 'rxjs/operators';\nimport * as i1 from '@angular/common';\nimport { isPlatformServer, DOCUMENT, ɵparseCookieValue } from '@angular/common';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        /**\n         * Internal map of lowercased header names to the normalized\n         * form of the name (the form seen first).\n         */\n        this.normalizedNames = new Map();\n        /**\n         * Queued updates to be materialized the next initialization.\n         */\n        this.lazyUpdate = null;\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach((line) => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const key = name.toLowerCase();\n                        const value = line.slice(index + 1).trim();\n                        this.maybeSetNormalizedName(name, key);\n                        if (this.headers.has(key)) {\n                            this.headers.get(key).push(value);\n                        }\n                        else {\n                            this.headers.set(key, [value]);\n                        }\n                    }\n                });\n            };\n        }\n        else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n            this.headers = new Map();\n            headers.forEach((values, name) => {\n                this.setHeaderEntries(name, values);\n            });\n        }\n        else {\n            this.lazyInit = () => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    assertValidHeaders(headers);\n                }\n                this.headers = new Map();\n                Object.entries(headers).forEach(([name, values]) => {\n                    this.setHeaderEntries(name, values);\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or override for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach((update) => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach((key) => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter((value) => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    setHeaderEntries(name, values) {\n        const headerValues = (Array.isArray(values) ? values : [values]).map((value) => value.toString());\n        const key = name.toLowerCase();\n        this.headers.set(key, headerValues);\n        this.maybeSetNormalizedName(name, key);\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys()).forEach((key) => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n    for (const [key, value] of Object.entries(headers)) {\n        if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n            throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` +\n                `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n        }\n    }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1\n                ? [codec.decodeKey(param), '']\n                : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    constructor(options = {}) {\n        this.updates = null;\n        this.cloneFrom = null;\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (!!options.fromString) {\n            if (!!options.fromObject) {\n                throw new Error(`Cannot specify both fromString and fromObject.`);\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach((key) => {\n                const value = options.fromObject[key];\n                // convert the values to strings\n                const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n                this.map.set(key, values);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach((param) => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach((_value) => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return (this.keys()\n            .map((key) => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key)\n                .map((value) => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter((param) => param !== '')\n            .join('&'));\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach((key) => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach((update) => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    constructor() {\n        this.map = new Map();\n    }\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * Checks for existence of a given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns True if the token exists, false otherwise.\n     */\n    has(token) {\n        return this.map.has(token);\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        /**\n         * The request body, or `null` if one isn't set.\n         *\n         * Bodies are not enforced to be immutable, as they can include a reference to any\n         * user-defined data type. However, interceptors should take care to preserve\n         * idempotence by treating them as such.\n         */\n        this.body = null;\n        /**\n         * Whether this request should be made in a way that exposes progress events.\n         *\n         * Progress events are expensive (change detection runs on each event) and so\n         * they should only be requested if the consumer intends to monitor them.\n         *\n         * Note: The `FetchBackend` doesn't support progress report on uploads.\n         */\n        this.reportProgress = false;\n        /**\n         * Whether this request should be sent with outgoing credentials (cookies).\n         */\n        this.withCredentials = false;\n        /**\n         * The expected response type of the server.\n         *\n         * This is used to parse the response appropriately before returning it to\n         * the requestee.\n         */\n        this.responseType = 'json';\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = third !== undefined ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n            // We do want to assign transferCache even if it's falsy (false is valid value)\n            this.transferCache = options.transferCache;\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        this.headers ??= new HttpHeaders();\n        // If no context have been passed in, construct a new HttpContext instance.\n        this.context ??= new HttpContext();\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (typeof this.body === 'string' ||\n            isArrayBuffer(this.body) ||\n            isBlob(this.body) ||\n            isFormData(this.body) ||\n            isUrlSearchParams(this.body)) {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return 'text/plain';\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return 'application/json';\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // Carefully handle the transferCache to differentiate between\n        // `false` and `undefined` in the update args.\n        const transferCache = update.transferCache ?? this.transferCache;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = update.body !== undefined ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = update.withCredentials ?? this.withCredentials;\n        const reportProgress = update.reportProgress ?? this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n            transferCache,\n        });\n    }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.ResponseHeader;\n    }\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.Response;\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    clone(update = {}) {\n        return new HttpResponse({\n            body: update.body !== undefined ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        this.name = 'HttpErrorResponse';\n        /**\n         * Errors are never okay, even when the status code is in the 2xx success range.\n         */\n        this.ok = false;\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n    HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n    HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n    HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n    HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n    HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n    HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n    HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n    HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n    HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n    HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n    HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n    HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n    HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n    HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n    HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n    HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n    HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n    HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n    HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n    HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n    HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n    HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n    HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n    HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n    HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n    HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n    HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n    HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n    HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n    HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n    HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n    HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n    HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n    HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n    HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n    HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n    HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n    HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n    HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n    HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n    HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n    HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n    HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n    HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n    HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n    HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n    HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n    HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n    HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n    HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n    HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n    HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n    HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n    HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n    HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n    HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n    HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n    HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n    HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n * TODO(adev): review\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n                transferCache: options.transferCache,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = (events$.pipe(filter((event) => event instanceof HttpResponse)));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new Error('Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new Error('Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new Error('Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClient }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpHandler }] });\n\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\nconst REQUEST_URL_HEADER = `X-Request-URL`;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n    if (response.url) {\n        return response.url;\n    }\n    // stored as lowercase in the map\n    const xRequestUrl = REQUEST_URL_HEADER.toLocaleLowerCase();\n    return response.headers.get(xRequestUrl);\n}\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n    constructor() {\n        // We use an arrow function to always reference the current global implementation of `fetch`.\n        // This is helpful for cases when the global `fetch` implementation is modified by external code,\n        // see https://github.com/angular/angular/issues/57527.\n        this.fetchImpl = inject(FetchFactory, { optional: true })?.fetch ?? ((...args) => globalThis.fetch(...args));\n        this.ngZone = inject(NgZone);\n    }\n    handle(request) {\n        return new Observable((observer) => {\n            const aborter = new AbortController();\n            this.doRequest(request, aborter.signal, observer).then(noop, (error) => observer.error(new HttpErrorResponse({ error })));\n            return () => aborter.abort();\n        });\n    }\n    async doRequest(request, signal, observer) {\n        const init = this.createRequestInit(request);\n        let response;\n        try {\n            // Run fetch outside of Angular zone.\n            // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n            // the response should eventually timeout which causes extra CD cycles every 500ms\n            const fetchPromise = this.ngZone.runOutsideAngular(() => this.fetchImpl(request.urlWithParams, { signal, ...init }));\n            // Make sure Zone.js doesn't trigger false-positive unhandled promise\n            // error in case the Promise is rejected synchronously. See function\n            // description for additional information.\n            silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n            // Send the `Sent` event before awaiting the response.\n            observer.next({ type: HttpEventType.Sent });\n            response = await fetchPromise;\n        }\n        catch (error) {\n            observer.error(new HttpErrorResponse({\n                error,\n                status: error.status ?? 0,\n                statusText: error.statusText,\n                url: request.urlWithParams,\n                headers: error.headers,\n            }));\n            return;\n        }\n        const headers = new HttpHeaders(response.headers);\n        const statusText = response.statusText;\n        const url = getResponseUrl$1(response) ?? request.urlWithParams;\n        let status = response.status;\n        let body = null;\n        if (request.reportProgress) {\n            observer.next(new HttpHeaderResponse({ headers, status, statusText, url }));\n        }\n        if (response.body) {\n            // Read Progress\n            const contentLength = response.headers.get('content-length');\n            const chunks = [];\n            const reader = response.body.getReader();\n            let receivedLength = 0;\n            let decoder;\n            let partialText;\n            // We have to check whether the Zone is defined in the global scope because this may be called\n            // when the zone is nooped.\n            const reqZone = typeof Zone !== 'undefined' && Zone.current;\n            // Perform response processing outside of Angular zone to\n            // ensure no excessive change detection runs are executed\n            // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n            await this.ngZone.runOutsideAngular(async () => {\n                while (true) {\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    chunks.push(value);\n                    receivedLength += value.length;\n                    if (request.reportProgress) {\n                        partialText =\n                            request.responseType === 'text'\n                                ? (partialText ?? '') +\n                                    (decoder ??= new TextDecoder()).decode(value, { stream: true })\n                                : undefined;\n                        const reportProgress = () => observer.next({\n                            type: HttpEventType.DownloadProgress,\n                            total: contentLength ? +contentLength : undefined,\n                            loaded: receivedLength,\n                            partialText,\n                        });\n                        reqZone ? reqZone.run(reportProgress) : reportProgress();\n                    }\n                }\n            });\n            // Combine all chunks.\n            const chunksAll = this.concatChunks(chunks, receivedLength);\n            try {\n                const contentType = response.headers.get('Content-Type') ?? '';\n                body = this.parseBody(request, chunksAll, contentType);\n            }\n            catch (error) {\n                // Body loading or parsing failed\n                observer.error(new HttpErrorResponse({\n                    error,\n                    headers: new HttpHeaders(response.headers),\n                    status: response.status,\n                    statusText: response.statusText,\n                    url: getResponseUrl$1(response) ?? request.urlWithParams,\n                }));\n                return;\n            }\n        }\n        // Same behavior as the XhrBackend\n        if (status === 0) {\n            status = body ? HTTP_STATUS_CODE_OK : 0;\n        }\n        // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n        const ok = status >= 200 && status < 300;\n        if (ok) {\n            observer.next(new HttpResponse({\n                body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n        }\n        else {\n            observer.error(new HttpErrorResponse({\n                error: body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n        }\n    }\n    parseBody(request, binContent, contentType) {\n        switch (request.responseType) {\n            case 'json':\n                // stripping the XSSI when present\n                const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n                return text === '' ? null : JSON.parse(text);\n            case 'text':\n                return new TextDecoder().decode(binContent);\n            case 'blob':\n                return new Blob([binContent], { type: contentType });\n            case 'arraybuffer':\n                return binContent.buffer;\n        }\n    }\n    createRequestInit(req) {\n        // We could share some of this logic with the XhrBackend\n        const headers = {};\n        const credentials = req.withCredentials ? 'include' : undefined;\n        // Setting all the requested headers.\n        req.headers.forEach((name, values) => (headers[name] = values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has('Accept')) {\n            headers['Accept'] = 'application/json, text/plain, */*';\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has('Content-Type')) {\n            const detectedType = req.detectContentTypeHeader();\n            // Sometimes Content-Type detection fails.\n            if (detectedType !== null) {\n                headers['Content-Type'] = detectedType;\n            }\n        }\n        return {\n            body: req.serializeBody(),\n            method: req.method,\n            headers,\n            credentials,\n        };\n    }\n    concatChunks(chunks, totalLength) {\n        const chunksAll = new Uint8Array(totalLength);\n        let position = 0;\n        for (const chunk of chunks) {\n            chunksAll.set(chunk, position);\n            position += chunk.length;\n        }\n        return chunksAll;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FetchBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FetchBackend }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: FetchBackend, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {\n}\nfunction noop() { }\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n    promise.then(noop, noop);\n}\n\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n    return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n    return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n        handle: (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn),\n    });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n    return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', { providedIn: 'root', factory: () => true });\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n    let chain = null;\n    return (req, handler) => {\n        if (chain === null) {\n            const interceptors = inject(HTTP_INTERCEPTORS, { optional: true }) ?? [];\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n        }\n        const pendingTasks = inject(ɵPendingTasks);\n        const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n        if (contributeToStability) {\n            const taskId = pendingTasks.add();\n            return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n        }\n        else {\n            return chain(req, handler);\n        }\n    };\n}\nlet fetchBackendWarningDisplayed = false;\n/** Internal function to reset the flag in tests */\nfunction resetFetchBackendWarningFlag() {\n    fetchBackendWarningDisplayed = false;\n}\nclass HttpInterceptorHandler extends HttpHandler {\n    constructor(backend, injector) {\n        super();\n        this.backend = backend;\n        this.injector = injector;\n        this.chain = null;\n        this.pendingTasks = inject(ɵPendingTasks);\n        this.contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n        // We strongly recommend using fetch backend for HTTP calls when SSR is used\n        // for an application. The logic below checks if that's the case and produces\n        // a warning otherwise.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n            const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n            if (isServer && !(this.backend instanceof FetchBackend)) {\n                fetchBackendWarningDisplayed = true;\n                injector\n                    .get(ɵConsole)\n                    .warn(ɵformatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' +\n                    \"to use `fetch` APIs. It's strongly recommended to \" +\n                    'enable `fetch` for applications that use Server-Side Rendering ' +\n                    'for better performance and compatibility. ' +\n                    'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' +\n                    'call at the root of the application.'));\n            }\n        }\n    }\n    handle(initialRequest) {\n        if (this.chain === null) {\n            const dedupedInterceptorFns = Array.from(new Set([\n                ...this.injector.get(HTTP_INTERCEPTOR_FNS),\n                ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, []),\n            ]));\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n        }\n        if (this.contributeToStability) {\n            const taskId = this.pendingTasks.add();\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n        }\n        else {\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest));\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpInterceptorHandler, deps: [{ token: HttpBackend }, { token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpInterceptorHandler }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpInterceptorHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpBackend }, { type: i0.EnvironmentInjector }] });\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n        /**\n         * A resolved promise that can be used to schedule microtasks in the event handlers.\n         */\n        this.resolvedPromise = Promise.resolve();\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Check the request headers. JSONP doesn't support headers and\n        // cannot set any that were supplied.\n        if (req.headers.keys().length > 0) {\n            throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                node.removeEventListener('load', onLoad);\n                node.removeEventListener('error', onError);\n                // Remove the <script> tag if it's still on the page.\n                node.remove();\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: HTTP_STATUS_CODE_OK,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                if (!finished) {\n                    this.removeListeners(node);\n                }\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n    removeListeners(script) {\n        // Issue #34818\n        // Changing <script>'s ownerDocument will prevent it from execution.\n        // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n        foreignDocument ??= this.document.implementation.createHTMLDocument();\n        foreignDocument.adoptNode(script);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpClientBackend }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n    if (req.method === 'JSONP') {\n        return inject(JsonpClientBackend).handle(req);\n    }\n    // Fall through for normal HTTP requests.\n    return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    constructor(injector) {\n        this.injector = injector;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param initialRequest The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader('X-Request-URL');\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new ɵRuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n        }\n        // Check whether this factory has a special function to load an XHR implementation\n        // for various non-browser environments. We currently limit it to only `ServerXhr`\n        // class, which needs to load an XHR implementation.\n        const xhrFactory = this.xhrFactory;\n        const source = xhrFactory.ɵloadImpl\n            ? from(xhrFactory.ɵloadImpl())\n            : of(null);\n        return source.pipe(switchMap(() => {\n            // Everything happens on Observable subscription.\n            return new Observable((observer) => {\n                // Start by setting up the XHR object with request method, URL, and withCredentials\n                // flag.\n                const xhr = xhrFactory.build();\n                xhr.open(req.method, req.urlWithParams);\n                if (req.withCredentials) {\n                    xhr.withCredentials = true;\n                }\n                // Add all the requested headers.\n                req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n                // Add an Accept header if one isn't present already.\n                if (!req.headers.has('Accept')) {\n                    xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n                }\n                // Auto-detect the Content-Type header if one isn't present already.\n                if (!req.headers.has('Content-Type')) {\n                    const detectedType = req.detectContentTypeHeader();\n                    // Sometimes Content-Type detection fails.\n                    if (detectedType !== null) {\n                        xhr.setRequestHeader('Content-Type', detectedType);\n                    }\n                }\n                // Set the responseType if one was requested.\n                if (req.responseType) {\n                    const responseType = req.responseType.toLowerCase();\n                    // JSON responses need to be processed as text. This is because if the server\n                    // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                    // xhr.response will be null, and xhr.responseText cannot be accessed to\n                    // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                    // is parsed by first requesting text and then applying JSON.parse.\n                    xhr.responseType = (responseType !== 'json' ? responseType : 'text');\n                }\n                // Serialize the request body if one is present. If not, this will be set to null.\n                const reqBody = req.serializeBody();\n                // If progress events are enabled, response headers will be delivered\n                // in two events - the HttpHeaderResponse event and the full HttpResponse\n                // event. However, since response headers don't change in between these\n                // two events, it doesn't make sense to parse them twice. So headerResponse\n                // caches the data extracted from the response whenever it's first parsed,\n                // to ensure parsing isn't duplicated.\n                let headerResponse = null;\n                // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n                // state, and memoizes it into headerResponse.\n                const partialFromXhr = () => {\n                    if (headerResponse !== null) {\n                        return headerResponse;\n                    }\n                    const statusText = xhr.statusText || 'OK';\n                    // Parse headers from XMLHttpRequest - this step is lazy.\n                    const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                    // Read the response URL from the XMLHttpResponse instance and fall back on the\n                    // request URL.\n                    const url = getResponseUrl(xhr) || req.url;\n                    // Construct the HttpHeaderResponse and memoize it.\n                    headerResponse = new HttpHeaderResponse({ headers, status: xhr.status, statusText, url });\n                    return headerResponse;\n                };\n                // Next, a few closures are defined for the various events which XMLHttpRequest can\n                // emit. This allows them to be unregistered as event listeners later.\n                // First up is the load event, which represents a response being fully available.\n                const onLoad = () => {\n                    // Read response state from the memoized partial data.\n                    let { headers, status, statusText, url } = partialFromXhr();\n                    // The body will be read out if present.\n                    let body = null;\n                    if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n                        // Use XMLHttpRequest.response if set, responseText otherwise.\n                        body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n                    }\n                    // Normalize another potential bug (this one comes from CORS).\n                    if (status === 0) {\n                        status = !!body ? HTTP_STATUS_CODE_OK : 0;\n                    }\n                    // ok determines whether the response will be transmitted on the event or\n                    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                    // but a successful status code can still result in an error if the user\n                    // asked for JSON data and the body cannot be parsed as such.\n                    let ok = status >= 200 && status < 300;\n                    // Check whether the body needs to be parsed as JSON (in many cases the browser\n                    // will have done that already).\n                    if (req.responseType === 'json' && typeof body === 'string') {\n                        // Save the original body, before attempting XSSI prefix stripping.\n                        const originalBody = body;\n                        body = body.replace(XSSI_PREFIX, '');\n                        try {\n                            // Attempt the parse. If it fails, a parse error should be delivered to the\n                            // user.\n                            body = body !== '' ? JSON.parse(body) : null;\n                        }\n                        catch (error) {\n                            // Since the JSON.parse failed, it's reasonable to assume this might not have\n                            // been a JSON response. Restore the original body (including any XSSI prefix)\n                            // to deliver a better error response.\n                            body = originalBody;\n                            // If this was an error request to begin with, leave it as a string, it\n                            // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n                            if (ok) {\n                                // Even though the response status was 2xx, this is still an error.\n                                ok = false;\n                                // The parse error contains the text of the body that failed to parse.\n                                body = { error, text: body };\n                            }\n                        }\n                    }\n                    if (ok) {\n                        // A successful response is delivered on the event stream.\n                        observer.next(new HttpResponse({\n                            body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                        // The full body has been received and delivered, no further events\n                        // are possible. This request is complete.\n                        observer.complete();\n                    }\n                    else {\n                        // An unsuccessful request is delivered on the error channel.\n                        observer.error(new HttpErrorResponse({\n                            // The error in this case is the response body (error from the server).\n                            error: body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                    }\n                };\n                // The onError callback is called when something goes wrong at the network level.\n                // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n                // transmitted on the error channel.\n                const onError = (error) => {\n                    const { url } = partialFromXhr();\n                    const res = new HttpErrorResponse({\n                        error,\n                        status: xhr.status || 0,\n                        statusText: xhr.statusText || 'Unknown Error',\n                        url: url || undefined,\n                    });\n                    observer.error(res);\n                };\n                // The sentHeaders flag tracks whether the HttpResponseHeaders event\n                // has been sent on the stream. This is necessary to track if progress\n                // is enabled since the event will be sent on only the first download\n                // progress event.\n                let sentHeaders = false;\n                // The download progress event handler, which is only registered if\n                // progress events are enabled.\n                const onDownProgress = (event) => {\n                    // Send the HttpResponseHeaders event if it hasn't been sent already.\n                    if (!sentHeaders) {\n                        observer.next(partialFromXhr());\n                        sentHeaders = true;\n                    }\n                    // Start building the download progress event to deliver on the response\n                    // event stream.\n                    let progressEvent = {\n                        type: HttpEventType.DownloadProgress,\n                        loaded: event.loaded,\n                    };\n                    // Set the total number of bytes in the event if it's available.\n                    if (event.lengthComputable) {\n                        progressEvent.total = event.total;\n                    }\n                    // If the request was for text content and a partial response is\n                    // available on XMLHttpRequest, include it in the progress event\n                    // to allow for streaming reads.\n                    if (req.responseType === 'text' && !!xhr.responseText) {\n                        progressEvent.partialText = xhr.responseText;\n                    }\n                    // Finally, fire the event.\n                    observer.next(progressEvent);\n                };\n                // The upload progress event handler, which is only registered if\n                // progress events are enabled.\n                const onUpProgress = (event) => {\n                    // Upload progress events are simpler. Begin building the progress\n                    // event.\n                    let progress = {\n                        type: HttpEventType.UploadProgress,\n                        loaded: event.loaded,\n                    };\n                    // If the total number of bytes being uploaded is available, include\n                    // it.\n                    if (event.lengthComputable) {\n                        progress.total = event.total;\n                    }\n                    // Send the event.\n                    observer.next(progress);\n                };\n                // By default, register for load and error events.\n                xhr.addEventListener('load', onLoad);\n                xhr.addEventListener('error', onError);\n                xhr.addEventListener('timeout', onError);\n                xhr.addEventListener('abort', onError);\n                // Progress events are only enabled if requested.\n                if (req.reportProgress) {\n                    // Download progress is always enabled if requested.\n                    xhr.addEventListener('progress', onDownProgress);\n                    // Upload progress depends on whether there is a body to upload.\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.addEventListener('progress', onUpProgress);\n                    }\n                }\n                // Fire the request, and notify the event stream that it was fired.\n                xhr.send(reqBody);\n                observer.next({ type: HttpEventType.Sent });\n                // This is the return from the Observable function, which is the\n                // request cancellation handler.\n                return () => {\n                    // On a cancellation, remove all registered event listeners.\n                    xhr.removeEventListener('error', onError);\n                    xhr.removeEventListener('abort', onError);\n                    xhr.removeEventListener('load', onLoad);\n                    xhr.removeEventListener('timeout', onError);\n                    if (req.reportProgress) {\n                        xhr.removeEventListener('progress', onDownProgress);\n                        if (reqBody !== null && xhr.upload) {\n                            xhr.upload.removeEventListener('progress', onUpProgress);\n                        }\n                    }\n                    // Finally, abort the in-flight request.\n                    if (xhr.readyState !== xhr.DONE) {\n                        xhr.abort();\n                    }\n                };\n            });\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: i1.XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXhrBackend }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i1.XhrFactory }] });\n\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_COOKIE_NAME,\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_HEADER_NAME,\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    constructor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n        this.lastCookieString = '';\n        this.lastToken = null;\n        /**\n         * @internal for testing\n         */\n        this.parseCount = 0;\n    }\n    getToken() {\n        if (this.platform === 'server') {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfCookieExtractor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }] });\nfunction xsrfInterceptorFn(req, next) {\n    const lcUrl = req.url.toLowerCase();\n    // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n    if (!inject(XSRF_ENABLED) ||\n        req.method === 'GET' ||\n        req.method === 'HEAD' ||\n        lcUrl.startsWith('http://') ||\n        lcUrl.startsWith('https://')) {\n        return next(req);\n    }\n    const token = inject(HttpXsrfTokenExtractor).getToken();\n    const headerName = inject(XSRF_HEADER_NAME);\n    // Be careful not to overwrite an existing header of the same name.\n    if (token != null && !req.headers.has(headerName)) {\n        req = req.clone({ headers: req.headers.set(headerName, token) });\n    }\n    return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    constructor(injector) {\n        this.injector = injector;\n    }\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n    HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n    HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n    HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n    HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n    HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n    return {\n        ɵkind: kind,\n        ɵproviders: providers,\n    };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"alert is-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n    if (ngDevMode) {\n        const featureKinds = new Set(features.map((f) => f.ɵkind));\n        if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) &&\n            featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n            throw new Error(ngDevMode\n                ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.`\n                : '');\n        }\n    }\n    const providers = [\n        HttpClient,\n        HttpXhrBackend,\n        HttpInterceptorHandler,\n        { provide: HttpHandler, useExisting: HttpInterceptorHandler },\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                return inject(FetchBackend, { optional: true }) ?? inject(HttpXhrBackend);\n            },\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: xsrfInterceptorFn,\n            multi: true,\n        },\n        { provide: XSRF_ENABLED, useValue: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n    ];\n    for (const feature of features) {\n        providers.push(...feature.ɵproviders);\n    }\n    return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n    return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map((interceptorFn) => {\n        return {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: interceptorFn,\n            multi: true,\n        };\n    }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n    // Note: the legacy interceptor function is provided here via an intermediate token\n    // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n    // included multiple times, all of the multi-provider entries will have the same instance of the\n    // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n    // interceptors will not run multiple times.\n    return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [\n        {\n            provide: LEGACY_INTERCEPTOR_FN,\n            useFactory: legacyInterceptorFnFactory,\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useExisting: LEGACY_INTERCEPTOR_FN,\n            multi: true,\n        },\n    ]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({ cookieName, headerName, }) {\n    const providers = [];\n    if (cookieName !== undefined) {\n        providers.push({ provide: XSRF_COOKIE_NAME, useValue: cookieName });\n    }\n    if (headerName !== undefined) {\n        providers.push({ provide: XSRF_HEADER_NAME, useValue: headerName });\n    }\n    return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n    return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [\n        {\n            provide: XSRF_ENABLED,\n            useValue: false,\n        },\n    ]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n    return makeHttpFeature(HttpFeatureKind.JsonpSupport, [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTOR_FNS, useValue: jsonpInterceptorFn, multi: true },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @developerPreview\n */\nfunction withRequestsMadeViaParent() {\n    return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                const handlerFromParent = inject(HttpHandler, { skipSelf: true, optional: true });\n                if (ngDevMode && handlerFromParent === null) {\n                    throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n                }\n                return handlerFromParent;\n            },\n        },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n    return makeHttpFeature(HttpFeatureKind.Fetch, [\n        FetchBackend,\n        { provide: HttpBackend, useExisting: FetchBackend },\n    ]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [withNoXsrfProtection().ɵproviders],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: withXsrfConfiguration(options).ɵproviders,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientXsrfModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n            HttpXsrfInterceptor,\n            { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n            { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n            withXsrfConfiguration({\n                cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                headerName: XSRF_DEFAULT_HEADER_NAME,\n            }).ɵproviders,\n            { provide: XSRF_ENABLED, useValue: true },\n        ] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        withXsrfConfiguration({\n                            cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                            headerName: XSRF_DEFAULT_HEADER_NAME,\n                        }).ɵproviders,\n                        { provide: XSRF_ENABLED, useValue: true },\n                    ],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientModule, providers: [provideHttpClient(withInterceptorsFromDi())] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Configures the dependency injector where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [provideHttpClient(withInterceptorsFromDi())],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientJsonpModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientJsonpModule, providers: [withJsonpSupport().ɵproviders] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [withJsonpSupport().ɵproviders],\n                }]\n        }] });\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```typescript\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nconst HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '');\n/**\n * Keys within cached response data structure.\n */\nconst BODY = 'b';\nconst HEADERS = 'h';\nconst STATUS = 's';\nconst STATUS_TEXT = 'st';\nconst REQ_URL = 'u';\nconst RESPONSE_TYPE = 'rt';\nconst CACHE_OPTIONS = new InjectionToken(ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '');\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\nfunction transferCacheInterceptorFn(req, next) {\n    const { isCacheActive, ...globalOptions } = inject(CACHE_OPTIONS);\n    const { transferCache: requestOptions, method: requestMethod } = req;\n    // In the following situations we do not want to cache the request\n    if (!isCacheActive ||\n        requestOptions === false ||\n        // POST requests are allowed either globally or at request level\n        (requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions) ||\n        (requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod)) ||\n        // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n        (!globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req)) ||\n        globalOptions.filter?.(req) === false) {\n        return next(req);\n    }\n    const transferState = inject(TransferState);\n    const originMap = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n        optional: true,\n    });\n    const isServer = isPlatformServer(inject(PLATFORM_ID));\n    if (originMap && !isServer) {\n        throw new ɵRuntimeError(2803 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT */, ngDevMode &&\n            'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' +\n                'present in the client side code. Please ensure that this token is only provided in the ' +\n                'server code of the application.');\n    }\n    const requestUrl = isServer && originMap ? mapRequestOriginUrl(req.url, originMap) : req.url;\n    const storeKey = makeCacheKey(req, requestUrl);\n    const response = transferState.get(storeKey, null);\n    let headersToInclude = globalOptions.includeHeaders;\n    if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n        // Request-specific config takes precedence over the global config.\n        headersToInclude = requestOptions.includeHeaders;\n    }\n    if (response) {\n        const { [BODY]: undecodedBody, [RESPONSE_TYPE]: responseType, [HEADERS]: httpHeaders, [STATUS]: status, [STATUS_TEXT]: statusText, [REQ_URL]: url, } = response;\n        // Request found in cache. Respond using it.\n        let body = undecodedBody;\n        switch (responseType) {\n            case 'arraybuffer':\n                body = new TextEncoder().encode(undecodedBody).buffer;\n                break;\n            case 'blob':\n                body = new Blob([undecodedBody]);\n                break;\n        }\n        // We want to warn users accessing a header provided from the cache\n        // That HttpTransferCache alters the headers\n        // The warning will be logged a single time by HttpHeaders instance\n        let headers = new HttpHeaders(httpHeaders);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Append extra logic in dev mode to produce a warning when a header\n            // that was not transferred to the client is accessed in the code via `get`\n            // and `has` calls.\n            headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n        }\n        return of(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url,\n        }));\n    }\n    // Request not found in cache. Make the request and cache it if on the server.\n    return next(req).pipe(tap((event) => {\n        if (event instanceof HttpResponse && isServer) {\n            transferState.set(storeKey, {\n                [BODY]: event.body,\n                [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n                [STATUS]: event.status,\n                [STATUS_TEXT]: event.statusText,\n                [REQ_URL]: requestUrl,\n                [RESPONSE_TYPE]: req.responseType,\n            });\n        }\n    }));\n}\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req) {\n    return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\nfunction getFilteredHeaders(headers, includeHeaders) {\n    if (!includeHeaders) {\n        return {};\n    }\n    const headersMap = {};\n    for (const key of includeHeaders) {\n        const values = headers.getAll(key);\n        if (values !== null) {\n            headersMap[key] = values;\n        }\n    }\n    return headersMap;\n}\nfunction sortAndConcatParams(params) {\n    return [...params.keys()]\n        .sort()\n        .map((k) => `${k}=${params.getAll(k)}`)\n        .join('&');\n}\nfunction makeCacheKey(request, mappedRequestUrl) {\n    // make the params encoded same as a url so it's easy to identify\n    const { params, method, responseType } = request;\n    const encodedParams = sortAndConcatParams(params);\n    let serializedBody = request.serializeBody();\n    if (serializedBody instanceof URLSearchParams) {\n        serializedBody = sortAndConcatParams(serializedBody);\n    }\n    else if (typeof serializedBody !== 'string') {\n        serializedBody = '';\n    }\n    const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n    const hash = generateHash(key);\n    return makeStateKey(hash);\n}\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value) {\n    let hash = 0;\n    for (const char of value) {\n        hash = (Math.imul(31, hash) + char.charCodeAt(0)) << 0;\n    }\n    // Force positive number hash.\n    // ********** = equivalent of Integer.MAX_VALUE.\n    hash += ********** + 1;\n    return hash.toString();\n}\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nfunction withHttpTransferCache(cacheOptions) {\n    return [\n        {\n            provide: CACHE_OPTIONS,\n            useFactory: () => {\n                ɵperformanceMarkFeature('NgHttpTransferCache');\n                return { isCacheActive: true, ...cacheOptions };\n            },\n        },\n        {\n            provide: HTTP_ROOT_INTERCEPTOR_FNS,\n            useValue: transferCacheInterceptorFn,\n            multi: true,\n            deps: [TransferState, CACHE_OPTIONS],\n        },\n        {\n            provide: APP_BOOTSTRAP_LISTENER,\n            multi: true,\n            useFactory: () => {\n                const appRef = inject(ApplicationRef);\n                const cacheState = inject(CACHE_OPTIONS);\n                return () => {\n                    ɵwhenStable(appRef).then(() => {\n                        cacheState.isCacheActive = false;\n                    });\n                };\n            },\n        },\n    ];\n}\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(url, headers, headersToInclude) {\n    const warningProduced = new Set();\n    return new Proxy(headers, {\n        get(target, prop) {\n            const value = Reflect.get(target, prop);\n            const methods = new Set(['get', 'has', 'getAll']);\n            if (typeof value !== 'function' || !methods.has(prop)) {\n                return value;\n            }\n            return (headerName) => {\n                // We log when the key has been removed and a warning hasn't been produced for the header\n                const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n                if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n                    warningProduced.add(key);\n                    const truncatedUrl = ɵtruncateMiddle(url);\n                    // TODO: create Error guide for this warning\n                    console.warn(ɵformatRuntimeError(2802 /* RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE */, `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` +\n                        `was not transferred from the server to the client by the HttpTransferCache. ` +\n                        `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` +\n                        `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` +\n                        `on a request level by adding the \\`transferCache\\` parameter, or on an application ` +\n                        `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` +\n                        `\\`provideClientHydration()\\` call. `));\n                }\n                // invoking the original method\n                return value.apply(target, [headerName]);\n            };\n        },\n    });\n}\nfunction mapRequestOriginUrl(url, originMap) {\n    const origin = new URL(url, 'resolve://').origin;\n    const mappedOrigin = originMap[origin];\n    if (!mappedOrigin) {\n        return url;\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        verifyMappedOrigin(mappedOrigin);\n    }\n    return url.replace(origin, mappedOrigin);\n}\nfunction verifyMappedOrigin(url) {\n    if (new URL(url, 'resolve://').pathname !== '/') {\n        throw new ɵRuntimeError(2804 /* RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH */, 'Angular detected a URL with a path segment in the value provided for the ' +\n            `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` +\n            'without any other segments.');\n    }\n}\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_TRANSFER_CACHE_ORIGIN_MAP, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS, HttpInterceptorHandler as ɵHttpInterceptingHandler, HttpInterceptorHandler as ɵHttpInterceptorHandler, REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY, withHttpTransferCache as ɵwithHttpTransferCache };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,YAAY,EAAEC,uBAAuB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,eAAe,QAAQ,eAAe;AACnV,SAASC,EAAE,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AAC3C,SAASC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACjF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACH,OAAO,EAAE;MACV,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACI,QAAQ,GAAG,MAAM;QAClB,IAAI,CAACJ,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBF,OAAO,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;UAClC,MAAMC,KAAK,GAAGD,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;UAC/B,IAAID,KAAK,GAAG,CAAC,EAAE;YACX,MAAME,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;YACjC,MAAMI,GAAG,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;YAC9B,MAAMC,KAAK,GAAGP,IAAI,CAACI,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC;YAC1C,IAAI,CAACC,sBAAsB,CAACN,IAAI,EAAEE,GAAG,CAAC;YACtC,IAAI,IAAI,CAACZ,OAAO,CAACiB,GAAG,CAACL,GAAG,CAAC,EAAE;cACvB,IAAI,CAACZ,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAACO,IAAI,CAACL,KAAK,CAAC;YACrC,CAAC,MACI;cACD,IAAI,CAACd,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAE,CAACE,KAAK,CAAC,CAAC;YAClC;UACJ;QACJ,CAAC,CAAC;MACN,CAAC;IACL,CAAC,MACI,IAAI,OAAOO,OAAO,KAAK,WAAW,IAAIrB,OAAO,YAAYqB,OAAO,EAAE;MACnE,IAAI,CAACrB,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;MACxBF,OAAO,CAACM,OAAO,CAAC,CAACgB,MAAM,EAAEZ,IAAI,KAAK;QAC9B,IAAI,CAACa,gBAAgB,CAACb,IAAI,EAAEY,MAAM,CAAC;MACvC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAClB,QAAQ,GAAG,MAAM;QAClB,IAAI,OAAOoB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/CC,kBAAkB,CAACzB,OAAO,CAAC;QAC/B;QACA,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBwB,MAAM,CAACC,OAAO,CAAC3B,OAAO,CAAC,CAACM,OAAO,CAAC,CAAC,CAACI,IAAI,EAAEY,MAAM,CAAC,KAAK;UAChD,IAAI,CAACC,gBAAgB,CAACb,IAAI,EAAEY,MAAM,CAAC;QACvC,CAAC,CAAC;MACN,CAAC;IACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,GAAGA,CAACP,IAAI,EAAE;IACN,IAAI,CAACkB,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC5B,OAAO,CAACiB,GAAG,CAACP,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,GAAGA,CAACR,IAAI,EAAE;IACN,IAAI,CAACkB,IAAI,CAAC,CAAC;IACX,MAAMN,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACkB,GAAG,CAACR,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;IACnD,OAAOS,MAAM,IAAIA,MAAM,CAACO,MAAM,GAAG,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;EACzD;EACA;AACJ;AACA;AACA;AACA;EACIQ,IAAIA,CAAA,EAAG;IACH,IAAI,CAACF,IAAI,CAAC,CAAC;IACX,OAAOG,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAACgB,eAAe,CAACqB,MAAM,CAAC,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,MAAMA,CAACtB,IAAI,EAAE;IACT,IAAI,CAACkB,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC5B,OAAO,CAACkB,GAAG,CAACR,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoB,MAAMA,CAACvB,IAAI,EAAEI,KAAK,EAAE;IAChB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAExB,IAAI;MAAEI,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,GAAGA,CAACV,IAAI,EAAEI,KAAK,EAAE;IACb,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAExB,IAAI;MAAEI,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAC1B,IAAI,EAAEI,KAAK,EAAE;IAChB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAExB,IAAI;MAAEI,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACAnB,sBAAsBA,CAACN,IAAI,EAAE2B,MAAM,EAAE;IACjC,IAAI,CAAC,IAAI,CAACpC,eAAe,CAACgB,GAAG,CAACoB,MAAM,CAAC,EAAE;MACnC,IAAI,CAACpC,eAAe,CAACmB,GAAG,CAACiB,MAAM,EAAE3B,IAAI,CAAC;IAC1C;EACJ;EACAkB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,CAAC,IAAI,CAACxB,QAAQ,EAAE;MACjB,IAAI,IAAI,CAACA,QAAQ,YAAYN,WAAW,EAAE;QACtC,IAAI,CAACwC,QAAQ,CAAC,IAAI,CAAClC,QAAQ,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC,CAAC,IAAI,CAACD,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACG,OAAO,CAAEiC,MAAM,IAAK,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC,CAAC;QAC7D,IAAI,CAACpC,UAAU,GAAG,IAAI;MAC1B;IACJ;EACJ;EACAmC,QAAQA,CAACG,KAAK,EAAE;IACZA,KAAK,CAACb,IAAI,CAAC,CAAC;IACZG,KAAK,CAAC9C,IAAI,CAACwD,KAAK,CAACzC,OAAO,CAAC8B,IAAI,CAAC,CAAC,CAAC,CAACxB,OAAO,CAAEM,GAAG,IAAK;MAC9C,IAAI,CAACZ,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAE6B,KAAK,CAACzC,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACX,eAAe,CAACmB,GAAG,CAACR,GAAG,EAAE6B,KAAK,CAACxC,eAAe,CAACiB,GAAG,CAACN,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC;EACN;EACAsB,KAAKA,CAACK,MAAM,EAAE;IACV,MAAML,KAAK,GAAG,IAAIpC,WAAW,CAAC,CAAC;IAC/BoC,KAAK,CAAC9B,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,YAAYN,WAAW,GAAG,IAAI,CAACM,QAAQ,GAAG,IAAI;IAC/F8B,KAAK,CAAC/B,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,IAAI,EAAE,EAAEuC,MAAM,CAAC,CAACH,MAAM,CAAC,CAAC;IAC3D,OAAOL,KAAK;EAChB;EACAM,WAAWA,CAACD,MAAM,EAAE;IAChB,MAAM3B,GAAG,GAAG2B,MAAM,CAAC7B,IAAI,CAACG,WAAW,CAAC,CAAC;IACrC,QAAQ0B,MAAM,CAACJ,EAAE;MACb,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAIrB,KAAK,GAAGyB,MAAM,CAACzB,KAAK;QACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnB;QACA,IAAIA,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;UACpB;QACJ;QACA,IAAI,CAACb,sBAAsB,CAACuB,MAAM,CAAC7B,IAAI,EAAEE,GAAG,CAAC;QAC7C,MAAM+B,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAE,KAAK,GAAG,GAAG,IAAI,CAACnC,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,GAAGgC,SAAS,KAAK,EAAE;QAC1ED,IAAI,CAACxB,IAAI,CAAC,GAAGL,KAAK,CAAC;QACnB,IAAI,CAACd,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAE+B,IAAI,CAAC;QAC3B;MACJ,KAAK,GAAG;QACJ,MAAME,QAAQ,GAAGN,MAAM,CAACzB,KAAK;QAC7B,IAAI,CAAC+B,QAAQ,EAAE;UACX,IAAI,CAAC7C,OAAO,CAACoC,MAAM,CAACxB,GAAG,CAAC;UACxB,IAAI,CAACX,eAAe,CAACmC,MAAM,CAACxB,GAAG,CAAC;QACpC,CAAC,MACI;UACD,IAAIkC,QAAQ,GAAG,IAAI,CAAC9C,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC;UACpC,IAAI,CAACkC,QAAQ,EAAE;YACX;UACJ;UACAA,QAAQ,GAAGA,QAAQ,CAAC3D,MAAM,CAAE2B,KAAK,IAAK+B,QAAQ,CAACpC,OAAO,CAACK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;UACrE,IAAIgC,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC7B,OAAO,CAACoC,MAAM,CAACxB,GAAG,CAAC;YACxB,IAAI,CAACX,eAAe,CAACmC,MAAM,CAACxB,GAAG,CAAC;UACpC,CAAC,MACI;YACD,IAAI,CAACZ,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAEkC,QAAQ,CAAC;UACnC;QACJ;QACA;IACR;EACJ;EACAvB,gBAAgBA,CAACb,IAAI,EAAEY,MAAM,EAAE;IAC3B,MAAMyB,YAAY,GAAG,CAAChB,KAAK,CAACiB,OAAO,CAAC1B,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC,EAAElC,GAAG,CAAE0B,KAAK,IAAKA,KAAK,CAACmC,QAAQ,CAAC,CAAC,CAAC;IACjG,MAAMrC,GAAG,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACb,OAAO,CAACoB,GAAG,CAACR,GAAG,EAAEmC,YAAY,CAAC;IACnC,IAAI,CAAC/B,sBAAsB,CAACN,IAAI,EAAEE,GAAG,CAAC;EAC1C;EACA;AACJ;AACA;EACIN,OAAOA,CAAC4C,EAAE,EAAE;IACR,IAAI,CAACtB,IAAI,CAAC,CAAC;IACXG,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAACgB,eAAe,CAAC6B,IAAI,CAAC,CAAC,CAAC,CAACxB,OAAO,CAAEM,GAAG,IAAKsC,EAAE,CAAC,IAAI,CAACjD,eAAe,CAACiB,GAAG,CAACN,GAAG,CAAC,EAAE,IAAI,CAACZ,OAAO,CAACkB,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC;EACtH;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,kBAAkBA,CAACzB,OAAO,EAAE;EACjC,KAAK,MAAM,CAACY,GAAG,EAAEE,KAAK,CAAC,IAAIY,MAAM,CAACC,OAAO,CAAC3B,OAAO,CAAC,EAAE;IAChD,IAAI,EAAE,OAAOc,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC,IAAI,CAACiB,KAAK,CAACiB,OAAO,CAAClC,KAAK,CAAC,EAAE;MACpF,MAAM,IAAIqC,KAAK,CAAC,6BAA6BvC,GAAG,sBAAsB,GAClE,+DAA+DE,KAAK,KAAK,CAAC;IAClF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACzC,GAAG,EAAE;IACX,OAAO0C,gBAAgB,CAAC1C,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI2C,WAAWA,CAACzC,KAAK,EAAE;IACf,OAAOwC,gBAAgB,CAACxC,KAAK,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI0C,SAASA,CAAC5C,GAAG,EAAE;IACX,OAAO6C,kBAAkB,CAAC7C,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI8C,WAAWA,CAAC5C,KAAK,EAAE;IACf,OAAO2C,kBAAkB,CAAC3C,KAAK,CAAC;EACpC;AACJ;AACA,SAAS6C,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACnC,MAAMzE,GAAG,GAAG,IAAIc,GAAG,CAAC,CAAC;EACrB,IAAI0D,SAAS,CAAC/B,MAAM,GAAG,CAAC,EAAE;IACtB;IACA;IACA;IACA,MAAMiC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC1D,KAAK,CAAC,GAAG,CAAC;IACtDyD,MAAM,CAACxD,OAAO,CAAE0D,KAAK,IAAK;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAACvD,OAAO,CAAC,GAAG,CAAC;MAChC,MAAM,CAACG,GAAG,EAAEsD,GAAG,CAAC,GAAGD,KAAK,IAAI,CAAC,CAAC,GACxB,CAACJ,KAAK,CAACL,SAAS,CAACQ,KAAK,CAAC,EAAE,EAAE,CAAC,GAC5B,CAACH,KAAK,CAACL,SAAS,CAACQ,KAAK,CAACrD,KAAK,CAAC,CAAC,EAAEsD,KAAK,CAAC,CAAC,EAAEJ,KAAK,CAACH,WAAW,CAACM,KAAK,CAACrD,KAAK,CAACsD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;MACzF,MAAME,IAAI,GAAG/E,GAAG,CAAC8B,GAAG,CAACN,GAAG,CAAC,IAAI,EAAE;MAC/BuD,IAAI,CAAChD,IAAI,CAAC+C,GAAG,CAAC;MACd9E,GAAG,CAACgC,GAAG,CAACR,GAAG,EAAEuD,IAAI,CAAC;IACtB,CAAC,CAAC;EACN;EACA,OAAO/E,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMgF,uBAAuB,GAAG,iBAAiB;AACjD,MAAMC,8BAA8B,GAAG;EACnC,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE;AACV,CAAC;AACD,SAASf,gBAAgBA,CAACgB,CAAC,EAAE;EACzB,OAAOC,kBAAkB,CAACD,CAAC,CAAC,CAACP,OAAO,CAACK,uBAAuB,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKJ,8BAA8B,CAACI,CAAC,CAAC,IAAID,CAAC,CAAC;AACnH;AACA,SAASE,aAAaA,CAAC5D,KAAK,EAAE;EAC1B,OAAO,GAAGA,KAAK,EAAE;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6D,UAAU,CAAC;EACb5E,WAAWA,CAAC6E,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,IAAI3B,oBAAoB,CAAC,CAAC;IAC5D,IAAI,CAAC,CAACwB,OAAO,CAACI,UAAU,EAAE;MACtB,IAAI,CAAC,CAACJ,OAAO,CAACK,UAAU,EAAE;QACtB,MAAM,IAAI9B,KAAK,CAAC,gDAAgD,CAAC;MACrE;MACA,IAAI,CAAC/D,GAAG,GAAGuE,WAAW,CAACiB,OAAO,CAACI,UAAU,EAAE,IAAI,CAACD,OAAO,CAAC;IAC5D,CAAC,MACI,IAAI,CAAC,CAACH,OAAO,CAACK,UAAU,EAAE;MAC3B,IAAI,CAAC7F,GAAG,GAAG,IAAIc,GAAG,CAAC,CAAC;MACpBwB,MAAM,CAACI,IAAI,CAAC8C,OAAO,CAACK,UAAU,CAAC,CAAC3E,OAAO,CAAEM,GAAG,IAAK;QAC7C,MAAME,KAAK,GAAG8D,OAAO,CAACK,UAAU,CAACrE,GAAG,CAAC;QACrC;QACA,MAAMU,MAAM,GAAGS,KAAK,CAACiB,OAAO,CAAClC,KAAK,CAAC,GAAGA,KAAK,CAAC1B,GAAG,CAACsF,aAAa,CAAC,GAAG,CAACA,aAAa,CAAC5D,KAAK,CAAC,CAAC;QACvF,IAAI,CAAC1B,GAAG,CAACgC,GAAG,CAACR,GAAG,EAAEU,MAAM,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAClC,GAAG,GAAG,IAAI;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6B,GAAGA,CAAC+C,KAAK,EAAE;IACP,IAAI,CAACpC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACxC,GAAG,CAAC6B,GAAG,CAAC+C,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9C,GAAGA,CAAC8C,KAAK,EAAE;IACP,IAAI,CAACpC,IAAI,CAAC,CAAC;IACX,MAAMsD,GAAG,GAAG,IAAI,CAAC9F,GAAG,CAAC8B,GAAG,CAAC8C,KAAK,CAAC;IAC/B,OAAO,CAAC,CAACkB,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIlD,MAAMA,CAACgC,KAAK,EAAE;IACV,IAAI,CAACpC,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACxC,GAAG,CAAC8B,GAAG,CAAC8C,KAAK,CAAC,IAAI,IAAI;EACtC;EACA;AACJ;AACA;AACA;EACIlC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACF,IAAI,CAAC,CAAC;IACX,OAAOG,KAAK,CAAC9C,IAAI,CAAC,IAAI,CAACG,GAAG,CAAC0C,IAAI,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAAC+B,KAAK,EAAElD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAE8B,KAAK;MAAElD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIgD,SAASA,CAACrB,MAAM,EAAE;IACd,MAAMe,OAAO,GAAG,EAAE;IAClBnD,MAAM,CAACI,IAAI,CAACgC,MAAM,CAAC,CAACxD,OAAO,CAAE0D,KAAK,IAAK;MACnC,MAAMlD,KAAK,GAAGgD,MAAM,CAACE,KAAK,CAAC;MAC3B,IAAIjC,KAAK,CAACiB,OAAO,CAAClC,KAAK,CAAC,EAAE;QACtBA,KAAK,CAACR,OAAO,CAAE8E,MAAM,IAAK;UACtBP,OAAO,CAAC1D,IAAI,CAAC;YAAE6C,KAAK;YAAElD,KAAK,EAAEsE,MAAM;YAAEjD,EAAE,EAAE;UAAI,CAAC,CAAC;QACnD,CAAC,CAAC;MACN,CAAC,MACI;QACD0C,OAAO,CAAC1D,IAAI,CAAC;UAAE6C,KAAK;UAAElD,KAAK,EAAEA,KAAK;UAAEqB,EAAE,EAAE;QAAI,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACD,KAAK,CAAC2C,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzD,GAAGA,CAAC4C,KAAK,EAAElD,KAAK,EAAE;IACd,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAE8B,KAAK;MAAElD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAC4B,KAAK,EAAElD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACoB,KAAK,CAAC;MAAE8B,KAAK;MAAElD,KAAK;MAAEqB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIc,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrB,IAAI,CAAC,CAAC;IACX,OAAQ,IAAI,CAACE,IAAI,CAAC,CAAC,CACd1C,GAAG,CAAEwB,GAAG,IAAK;MACd,MAAMyE,IAAI,GAAG,IAAI,CAACN,OAAO,CAAC1B,SAAS,CAACzC,GAAG,CAAC;MACxC;MACA;MACA;MACA,OAAO,IAAI,CAACxB,GAAG,CAAC8B,GAAG,CAACN,GAAG,CAAC,CACnBxB,GAAG,CAAE0B,KAAK,IAAKuE,IAAI,GAAG,GAAG,GAAG,IAAI,CAACN,OAAO,CAACxB,WAAW,CAACzC,KAAK,CAAC,CAAC,CAC5DwE,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACG;IACA;IAAA,CACCnG,MAAM,CAAE6E,KAAK,IAAKA,KAAK,KAAK,EAAE,CAAC,CAC/BsB,IAAI,CAAC,GAAG,CAAC;EAClB;EACApD,KAAKA,CAACK,MAAM,EAAE;IACV,MAAML,KAAK,GAAG,IAAIyC,UAAU,CAAC;MAAEI,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IACvD7C,KAAK,CAAC4C,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI;IACxC5C,KAAK,CAAC2C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,IAAI,EAAE,EAAEnC,MAAM,CAACH,MAAM,CAAC;IACnD,OAAOL,KAAK;EAChB;EACAN,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACxC,GAAG,KAAK,IAAI,EAAE;MACnB,IAAI,CAACA,GAAG,GAAG,IAAIc,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAAC4E,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACA,SAAS,CAAClD,IAAI,CAAC,CAAC;MACrB,IAAI,CAACkD,SAAS,CAAChD,IAAI,CAAC,CAAC,CAACxB,OAAO,CAAEM,GAAG,IAAK,IAAI,CAACxB,GAAG,CAACgC,GAAG,CAACR,GAAG,EAAE,IAAI,CAACkE,SAAS,CAAC1F,GAAG,CAAC8B,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC;MACtF,IAAI,CAACiE,OAAO,CAACvE,OAAO,CAAEiC,MAAM,IAAK;QAC7B,QAAQA,MAAM,CAACJ,EAAE;UACb,KAAK,GAAG;UACR,KAAK,GAAG;YACJ,MAAMQ,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC/C,GAAG,CAAC8B,GAAG,CAACqB,MAAM,CAACyB,KAAK,CAAC,GAAGpB,SAAS,KAAK,EAAE;YAC/ED,IAAI,CAACxB,IAAI,CAACuD,aAAa,CAACnC,MAAM,CAACzB,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC1B,GAAG,CAACgC,GAAG,CAACmB,MAAM,CAACyB,KAAK,EAAErB,IAAI,CAAC;YAChC;UACJ,KAAK,GAAG;YACJ,IAAIJ,MAAM,CAACzB,KAAK,KAAK8B,SAAS,EAAE;cAC5B,IAAID,IAAI,GAAG,IAAI,CAACvD,GAAG,CAAC8B,GAAG,CAACqB,MAAM,CAACyB,KAAK,CAAC,IAAI,EAAE;cAC3C,MAAMuB,GAAG,GAAG5C,IAAI,CAAClC,OAAO,CAACiE,aAAa,CAACnC,MAAM,CAACzB,KAAK,CAAC,CAAC;cACrD,IAAIyE,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ5C,IAAI,CAAC6C,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;cACvB;cACA,IAAI5C,IAAI,CAACd,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAACzC,GAAG,CAACgC,GAAG,CAACmB,MAAM,CAACyB,KAAK,EAAErB,IAAI,CAAC;cACpC,CAAC,MACI;gBACD,IAAI,CAACvD,GAAG,CAACgD,MAAM,CAACG,MAAM,CAACyB,KAAK,CAAC;cACjC;YACJ,CAAC,MACI;cACD,IAAI,CAAC5E,GAAG,CAACgD,MAAM,CAACG,MAAM,CAACyB,KAAK,CAAC;cAC7B;YACJ;QACR;MACJ,CAAC,CAAC;MACF,IAAI,CAACc,SAAS,GAAG,IAAI,CAACD,OAAO,GAAG,IAAI;IACxC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,CAAC;EACnB1F,WAAWA,CAAC2F,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd5F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACX,GAAG,GAAG,IAAIc,GAAG,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,GAAGA,CAACwE,KAAK,EAAE9E,KAAK,EAAE;IACd,IAAI,CAAC1B,GAAG,CAACgC,GAAG,CAACwE,KAAK,EAAE9E,KAAK,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,GAAGA,CAAC0E,KAAK,EAAE;IACP,IAAI,CAAC,IAAI,CAACxG,GAAG,CAAC6B,GAAG,CAAC2E,KAAK,CAAC,EAAE;MACtB,IAAI,CAACxG,GAAG,CAACgC,GAAG,CAACwE,KAAK,EAAEA,KAAK,CAACF,YAAY,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAI,CAACtG,GAAG,CAAC8B,GAAG,CAAC0E,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIxD,MAAMA,CAACwD,KAAK,EAAE;IACV,IAAI,CAACxG,GAAG,CAACgD,MAAM,CAACwD,KAAK,CAAC;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3E,GAAGA,CAAC2E,KAAK,EAAE;IACP,OAAO,IAAI,CAACxG,GAAG,CAAC6B,GAAG,CAAC2E,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;EACI9D,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAC1C,GAAG,CAAC0C,IAAI,CAAC,CAAC;EAC1B;AACJ;;AAEA;AACA;AACA;AACA,SAAS+D,aAAaA,CAACC,MAAM,EAAE;EAC3B,QAAQA,MAAM;IACV,KAAK,QAAQ;IACb,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAO,IAAI;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACjF,KAAK,EAAE;EAC1B,OAAO,OAAOkF,WAAW,KAAK,WAAW,IAAIlF,KAAK,YAAYkF,WAAW;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACnF,KAAK,EAAE;EACnB,OAAO,OAAOoF,IAAI,KAAK,WAAW,IAAIpF,KAAK,YAAYoF,IAAI;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACrF,KAAK,EAAE;EACvB,OAAO,OAAOsF,QAAQ,KAAK,WAAW,IAAItF,KAAK,YAAYsF,QAAQ;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACvF,KAAK,EAAE;EAC9B,OAAO,OAAOwF,eAAe,KAAK,WAAW,IAAIxF,KAAK,YAAYwF,eAAe;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdxG,WAAWA,CAAC+F,MAAM,EAAEU,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,IAAI,GAAG,IAAI;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,MAAM;IAC1B,IAAI,CAAChB,MAAM,GAAGA,MAAM,CAACiB,WAAW,CAAC,CAAC;IAClC;IACA;IACA,IAAInC,OAAO;IACX;IACA;IACA,IAAIiB,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,CAACY,MAAM,EAAE;MACxC;MACA,IAAI,CAACC,IAAI,GAAGF,KAAK,KAAK7D,SAAS,GAAG6D,KAAK,GAAG,IAAI;MAC9C7B,OAAO,GAAG8B,MAAM;IACpB,CAAC,MACI;MACD;MACA9B,OAAO,GAAG6B,KAAK;IACnB;IACA;IACA,IAAI7B,OAAO,EAAE;MACT;MACA,IAAI,CAACgC,cAAc,GAAG,CAAC,CAAChC,OAAO,CAACgC,cAAc;MAC9C,IAAI,CAACC,eAAe,GAAG,CAAC,CAACjC,OAAO,CAACiC,eAAe;MAChD;MACA,IAAI,CAAC,CAACjC,OAAO,CAACkC,YAAY,EAAE;QACxB,IAAI,CAACA,YAAY,GAAGlC,OAAO,CAACkC,YAAY;MAC5C;MACA;MACA,IAAI,CAAC,CAAClC,OAAO,CAAC5E,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAG4E,OAAO,CAAC5E,OAAO;MAClC;MACA,IAAI,CAAC,CAAC4E,OAAO,CAACoC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGpC,OAAO,CAACoC,OAAO;MAClC;MACA,IAAI,CAAC,CAACpC,OAAO,CAACd,MAAM,EAAE;QAClB,IAAI,CAACA,MAAM,GAAGc,OAAO,CAACd,MAAM;MAChC;MACA;MACA,IAAI,CAACmD,aAAa,GAAGrC,OAAO,CAACqC,aAAa;IAC9C;IACA;IACA,IAAI,CAACjH,OAAO,KAAK,IAAIF,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,CAACkH,OAAO,KAAK,IAAIrB,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,CAAC,IAAI,CAAC7B,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAIa,UAAU,CAAC,CAAC;MAC9B,IAAI,CAACuC,aAAa,GAAGV,GAAG;IAC5B,CAAC,MACI;MACD;MACA,MAAM1C,MAAM,GAAG,IAAI,CAACA,MAAM,CAACb,QAAQ,CAAC,CAAC;MACrC,IAAIa,MAAM,CAACjC,MAAM,KAAK,CAAC,EAAE;QACrB;QACA,IAAI,CAACqF,aAAa,GAAGV,GAAG;MAC5B,CAAC,MACI;QACD;QACA,MAAMW,IAAI,GAAGX,GAAG,CAAC/F,OAAO,CAAC,GAAG,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM2G,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,GAAGX,GAAG,CAAC3E,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAChE,IAAI,CAACqF,aAAa,GAAGV,GAAG,GAAGY,GAAG,GAAGtD,MAAM;MAC3C;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIuD,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACV,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7BZ,aAAa,CAAC,IAAI,CAACY,IAAI,CAAC,IACxBV,MAAM,CAAC,IAAI,CAACU,IAAI,CAAC,IACjBR,UAAU,CAAC,IAAI,CAACQ,IAAI,CAAC,IACrBN,iBAAiB,CAAC,IAAI,CAACM,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYhC,UAAU,EAAE;MACjC,OAAO,IAAI,CAACgC,IAAI,CAAC1D,QAAQ,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,OAAO,IAAI,CAAC0D,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,IAC9B5E,KAAK,CAACiB,OAAO,CAAC,IAAI,CAAC2D,IAAI,CAAC,EAAE;MAC1B,OAAOW,IAAI,CAACC,SAAS,CAAC,IAAI,CAACZ,IAAI,CAAC;IACpC;IACA;IACA,OAAO,IAAI,CAACA,IAAI,CAAC1D,QAAQ,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuE,uBAAuBA,CAAA,EAAG;IACtB;IACA,IAAI,IAAI,CAACb,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA,IAAIR,UAAU,CAAC,IAAI,CAACQ,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAIV,MAAM,CAAC,IAAI,CAACU,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI,CAACA,IAAI,CAACc,IAAI,IAAI,IAAI;IACjC;IACA;IACA,IAAI1B,aAAa,CAAC,IAAI,CAACY,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;MAC/B,OAAO,YAAY;IACvB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYhC,UAAU,EAAE;MACjC,OAAO,iDAAiD;IAC5D;IACA;IACA,IAAI,OAAO,IAAI,CAACgC,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,EAAE;MAChC,OAAO,kBAAkB;IAC7B;IACA;IACA,OAAO,IAAI;EACf;EACAzE,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,MAAMuD,MAAM,GAAGvD,MAAM,CAACuD,MAAM,IAAI,IAAI,CAACA,MAAM;IAC3C,MAAMU,GAAG,GAAGjE,MAAM,CAACiE,GAAG,IAAI,IAAI,CAACA,GAAG;IAClC,MAAMM,YAAY,GAAGvE,MAAM,CAACuE,YAAY,IAAI,IAAI,CAACA,YAAY;IAC7D;IACA;IACA,MAAMG,aAAa,GAAG1E,MAAM,CAAC0E,aAAa,IAAI,IAAI,CAACA,aAAa;IAChE;IACA;IACA;IACA;IACA,MAAMN,IAAI,GAAGpE,MAAM,CAACoE,IAAI,KAAK/D,SAAS,GAAGL,MAAM,CAACoE,IAAI,GAAG,IAAI,CAACA,IAAI;IAChE;IACA;IACA,MAAME,eAAe,GAAGtE,MAAM,CAACsE,eAAe,IAAI,IAAI,CAACA,eAAe;IACtE,MAAMD,cAAc,GAAGrE,MAAM,CAACqE,cAAc,IAAI,IAAI,CAACA,cAAc;IACnE;IACA;IACA,IAAI5G,OAAO,GAAGuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;IAC5C,IAAI8D,MAAM,GAAGvB,MAAM,CAACuB,MAAM,IAAI,IAAI,CAACA,MAAM;IACzC;IACA,MAAMkD,OAAO,GAAGzE,MAAM,CAACyE,OAAO,IAAI,IAAI,CAACA,OAAO;IAC9C;IACA,IAAIzE,MAAM,CAACmF,UAAU,KAAK9E,SAAS,EAAE;MACjC;MACA5C,OAAO,GAAG0B,MAAM,CAACI,IAAI,CAACS,MAAM,CAACmF,UAAU,CAAC,CAACC,MAAM,CAAC,CAAC3H,OAAO,EAAEU,IAAI,KAAKV,OAAO,CAACoB,GAAG,CAACV,IAAI,EAAE6B,MAAM,CAACmF,UAAU,CAAChH,IAAI,CAAC,CAAC,EAAEV,OAAO,CAAC;IAC3H;IACA;IACA,IAAIuC,MAAM,CAACqF,SAAS,EAAE;MAClB;MACA9D,MAAM,GAAGpC,MAAM,CAACI,IAAI,CAACS,MAAM,CAACqF,SAAS,CAAC,CAACD,MAAM,CAAC,CAAC7D,MAAM,EAAEE,KAAK,KAAKF,MAAM,CAAC1C,GAAG,CAAC4C,KAAK,EAAEzB,MAAM,CAACqF,SAAS,CAAC5D,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;IACxH;IACA;IACA,OAAO,IAAIyC,WAAW,CAACT,MAAM,EAAEU,GAAG,EAAEG,IAAI,EAAE;MACtC7C,MAAM;MACN9D,OAAO;MACPgH,OAAO;MACPJ,cAAc;MACdE,YAAY;MACZD,eAAe;MACfI;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIY,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjD;AACJ;AACA;AACA;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACzE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACrD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;AACA;EACI/H,WAAWA,CAAC6B,IAAI,EAAEmG,aAAa,GAAG,GAAG,EAAEC,iBAAiB,GAAG,IAAI,EAAE;IAC7D;IACA;IACA,IAAI,CAAChI,OAAO,GAAG4B,IAAI,CAAC5B,OAAO,IAAI,IAAIF,WAAW,CAAC,CAAC;IAChD,IAAI,CAACmI,MAAM,GAAGrG,IAAI,CAACqG,MAAM,KAAKrF,SAAS,GAAGhB,IAAI,CAACqG,MAAM,GAAGF,aAAa;IACrE,IAAI,CAACG,UAAU,GAAGtG,IAAI,CAACsG,UAAU,IAAIF,iBAAiB;IACtD,IAAI,CAACxB,GAAG,GAAG5E,IAAI,CAAC4E,GAAG,IAAI,IAAI;IAC3B;IACA,IAAI,CAAC2B,EAAE,GAAG,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;EACrD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,kBAAkB,SAASN,gBAAgB,CAAC;EAC9C;AACJ;AACA;EACI/H,WAAWA,CAAC6B,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAAC6F,IAAI,GAAGI,aAAa,CAACQ,cAAc;EAC5C;EACA;AACJ;AACA;AACA;EACInG,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,OAAO,IAAI6F,kBAAkB,CAAC;MAC1BpI,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCiI,MAAM,EAAE1F,MAAM,CAAC0F,MAAM,KAAKrF,SAAS,GAAGL,MAAM,CAAC0F,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAE3F,MAAM,CAAC2F,UAAU,IAAI,IAAI,CAACA,UAAU;MAChD1B,GAAG,EAAEjE,MAAM,CAACiE,GAAG,IAAI,IAAI,CAACA,GAAG,IAAI5D;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0F,YAAY,SAASR,gBAAgB,CAAC;EACxC;AACJ;AACA;EACI/H,WAAWA,CAAC6B,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAAC6F,IAAI,GAAGI,aAAa,CAACU,QAAQ;IAClC,IAAI,CAAC5B,IAAI,GAAG/E,IAAI,CAAC+E,IAAI,KAAK/D,SAAS,GAAGhB,IAAI,CAAC+E,IAAI,GAAG,IAAI;EAC1D;EACAzE,KAAKA,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;IACf,OAAO,IAAI+F,YAAY,CAAC;MACpB3B,IAAI,EAAEpE,MAAM,CAACoE,IAAI,KAAK/D,SAAS,GAAGL,MAAM,CAACoE,IAAI,GAAG,IAAI,CAACA,IAAI;MACzD3G,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCiI,MAAM,EAAE1F,MAAM,CAAC0F,MAAM,KAAKrF,SAAS,GAAGL,MAAM,CAAC0F,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAE3F,MAAM,CAAC2F,UAAU,IAAI,IAAI,CAACA,UAAU;MAChD1B,GAAG,EAAEjE,MAAM,CAACiE,GAAG,IAAI,IAAI,CAACA,GAAG,IAAI5D;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4F,iBAAiB,SAASV,gBAAgB,CAAC;EAC7C/H,WAAWA,CAAC6B,IAAI,EAAE;IACd;IACA,KAAK,CAACA,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC;IAC/B,IAAI,CAAClB,IAAI,GAAG,mBAAmB;IAC/B;AACR;AACA;IACQ,IAAI,CAACyH,EAAE,GAAG,KAAK;IACf;IACA;IACA;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,EAAE;MACzC,IAAI,CAACQ,OAAO,GAAG,mCAAmC7G,IAAI,CAAC4E,GAAG,IAAI,eAAe,EAAE;IACnF,CAAC,MACI;MACD,IAAI,CAACiC,OAAO,GAAG,6BAA6B7G,IAAI,CAAC4E,GAAG,IAAI,eAAe,KAAK5E,IAAI,CAACqG,MAAM,IAAIrG,IAAI,CAACsG,UAAU,EAAE;IAChH;IACA,IAAI,CAACQ,KAAK,GAAG9G,IAAI,CAAC8G,KAAK,IAAI,IAAI;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,2BAA2B,GAAG,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;EACjDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS;EAC3DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO;EACvDA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,GAAG,eAAe;EACvEA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM;EACrDA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,4BAA4B,CAAC,GAAG,GAAG,CAAC,GAAG,4BAA4B;EACjGA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,GAAG,yBAAyB;EAC3FA,cAAc,CAACA,cAAc,CAAC,uBAAuB,CAAC,GAAG,GAAG,CAAC,GAAG,uBAAuB;EACvFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,+BAA+B,CAAC,GAAG,GAAG,CAAC,GAAG,+BAA+B;AAC3G,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAClE,OAAO,EAAE+B,IAAI,EAAE;EAC5B,OAAO;IACHA,IAAI;IACJ3G,OAAO,EAAE4E,OAAO,CAAC5E,OAAO;IACxBgH,OAAO,EAAEpC,OAAO,CAACoC,OAAO;IACxB+B,OAAO,EAAEnE,OAAO,CAACmE,OAAO;IACxBjF,MAAM,EAAEc,OAAO,CAACd,MAAM;IACtB8C,cAAc,EAAEhC,OAAO,CAACgC,cAAc;IACtCE,YAAY,EAAElC,OAAO,CAACkC,YAAY;IAClCD,eAAe,EAAEjC,OAAO,CAACiC,eAAe;IACxCI,aAAa,EAAErC,OAAO,CAACqC;EAC3B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+B,UAAU,CAAC;EACbjJ,WAAWA,CAACkJ,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,KAAK,EAAE3C,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIwE,GAAG;IACP;IACA,IAAID,KAAK,YAAY5C,WAAW,EAAE;MAC9B;MACA;MACA6C,GAAG,GAAGD,KAAK;IACf,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,IAAInJ,OAAO,GAAG4C,SAAS;MACvB,IAAIgC,OAAO,CAAC5E,OAAO,YAAYF,WAAW,EAAE;QACxCE,OAAO,GAAG4E,OAAO,CAAC5E,OAAO;MAC7B,CAAC,MACI;QACDA,OAAO,GAAG,IAAIF,WAAW,CAAC8E,OAAO,CAAC5E,OAAO,CAAC;MAC9C;MACA;MACA,IAAI8D,MAAM,GAAGlB,SAAS;MACtB,IAAI,CAAC,CAACgC,OAAO,CAACd,MAAM,EAAE;QAClB,IAAIc,OAAO,CAACd,MAAM,YAAYa,UAAU,EAAE;UACtCb,MAAM,GAAGc,OAAO,CAACd,MAAM;QAC3B,CAAC,MACI;UACDA,MAAM,GAAG,IAAIa,UAAU,CAAC;YAAEM,UAAU,EAAEL,OAAO,CAACd;UAAO,CAAC,CAAC;QAC3D;MACJ;MACA;MACAsF,GAAG,GAAG,IAAI7C,WAAW,CAAC4C,KAAK,EAAE3C,GAAG,EAAE5B,OAAO,CAAC+B,IAAI,KAAK/D,SAAS,GAAGgC,OAAO,CAAC+B,IAAI,GAAG,IAAI,EAAE;QAChF3G,OAAO;QACPgH,OAAO,EAAEpC,OAAO,CAACoC,OAAO;QACxBlD,MAAM;QACN8C,cAAc,EAAEhC,OAAO,CAACgC,cAAc;QACtC;QACAE,YAAY,EAAElC,OAAO,CAACkC,YAAY,IAAI,MAAM;QAC5CD,eAAe,EAAEjC,OAAO,CAACiC,eAAe;QACxCI,aAAa,EAAErC,OAAO,CAACqC;MAC3B,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA,MAAMoC,OAAO,GAAGtK,EAAE,CAACqK,GAAG,CAAC,CAACE,IAAI,CAACpK,SAAS,CAAEkK,GAAG,IAAK,IAAI,CAACH,OAAO,CAACM,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA;IACA;IACA,IAAID,KAAK,YAAY5C,WAAW,IAAI3B,OAAO,CAACmE,OAAO,KAAK,QAAQ,EAAE;MAC9D,OAAOM,OAAO;IAClB;IACA;IACA;IACA;IACA,MAAMG,IAAI,GAAIH,OAAO,CAACC,IAAI,CAACnK,MAAM,CAAEsK,KAAK,IAAKA,KAAK,YAAYnB,YAAY,CAAC,CAAE;IAC7E;IACA,QAAQ1D,OAAO,CAACmE,OAAO,IAAI,MAAM;MAC7B,KAAK,MAAM;QACP;QACA;QACA;QACA;QACA;QACA,QAAQK,GAAG,CAACtC,YAAY;UACpB,KAAK,aAAa;YACd,OAAO0C,IAAI,CAACF,IAAI,CAAClK,GAAG,CAAE8F,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,EAAEzB,GAAG,CAACyB,IAAI,YAAYX,WAAW,CAAC,EAAE;gBACzD,MAAM,IAAI7C,KAAK,CAAC,iCAAiC,CAAC;cACtD;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAO6C,IAAI,CAACF,IAAI,CAAClK,GAAG,CAAE8F,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,EAAEzB,GAAG,CAACyB,IAAI,YAAYT,IAAI,CAAC,EAAE;gBAClD,MAAM,IAAI/C,KAAK,CAAC,yBAAyB,CAAC;cAC9C;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAO6C,IAAI,CAACF,IAAI,CAAClK,GAAG,CAAE8F,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAACyB,IAAI,KAAK,IAAI,IAAI,OAAOzB,GAAG,CAACyB,IAAI,KAAK,QAAQ,EAAE;gBACnD,MAAM,IAAIxD,KAAK,CAAC,2BAA2B,CAAC;cAChD;cACA,OAAO+B,GAAG,CAACyB,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;UACX;YACI;YACA,OAAO6C,IAAI,CAACF,IAAI,CAAClK,GAAG,CAAE8F,GAAG,IAAKA,GAAG,CAACyB,IAAI,CAAC,CAAC;QAChD;MACJ,KAAK,UAAU;QACX;QACA,OAAO6C,IAAI;MACf;QACI;QACA,MAAM,IAAIrG,KAAK,CAAC,uCAAuCyB,OAAO,CAACmE,OAAO,GAAG,CAAC;IAClF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3G,MAAMA,CAACoE,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAACsE,OAAO,CAAC,QAAQ,EAAE1C,GAAG,EAAE5B,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;EACI1D,GAAGA,CAACsF,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACsE,OAAO,CAAC,KAAK,EAAE1C,GAAG,EAAE5B,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8E,IAAIA,CAAClD,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO,IAAI,CAACsE,OAAO,CAAC,MAAM,EAAE1C,GAAG,EAAE5B,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+E,KAAKA,CAACnD,GAAG,EAAEoD,aAAa,EAAE;IACtB,OAAO,IAAI,CAACV,OAAO,CAAC,OAAO,EAAE1C,GAAG,EAAE;MAC9B1C,MAAM,EAAE,IAAIa,UAAU,CAAC,CAAC,CAAC1C,MAAM,CAAC2H,aAAa,EAAE,gBAAgB,CAAC;MAChEb,OAAO,EAAE,MAAM;MACfjC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlC,OAAOA,CAAC4B,GAAG,EAAE5B,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACsE,OAAO,CAAC,SAAS,EAAE1C,GAAG,EAAE5B,OAAO,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIiF,KAAKA,CAACrD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACsE,OAAO,CAAC,OAAO,EAAE1C,GAAG,EAAEsC,OAAO,CAAClE,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;EACImD,IAAIA,CAACtD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAI,CAACsE,OAAO,CAAC,MAAM,EAAE1C,GAAG,EAAEsC,OAAO,CAAClE,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoD,GAAGA,CAACvD,GAAG,EAAEG,IAAI,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACsE,OAAO,CAAC,KAAK,EAAE1C,GAAG,EAAEsC,OAAO,CAAClE,OAAO,EAAE+B,IAAI,CAAC,CAAC;EAC3D;EACA;IAAS,IAAI,CAACqD,IAAI,YAAAC,mBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyFlB,UAAU,EAApBtL,EAAE,CAAAyM,QAAA,CAAoCvK,WAAW;IAAA,CAA6C;EAAE;EACjM;IAAS,IAAI,CAACwK,KAAK,kBAD8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EACYoD,UAAU;MAAAsB,OAAA,EAAVtB,UAAU,CAAAgB;IAAA,EAAG;EAAE;AAClI;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KAHqG9D,EAAE,CAAA6M,iBAAA,CAGXvB,UAAU,EAAc,CAAC;IACzGvB,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE7H;EAAY,CAAC,CAAC;AAAA;AAEzD,MAAM4K,aAAa,GAAG,cAAc;AACpC,MAAMC,kBAAkB,GAAG,eAAe;AAC1C;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAChC,IAAIA,QAAQ,CAACnE,GAAG,EAAE;IACd,OAAOmE,QAAQ,CAACnE,GAAG;EACvB;EACA;EACA,MAAMoE,WAAW,GAAGH,kBAAkB,CAACI,iBAAiB,CAAC,CAAC;EAC1D,OAAOF,QAAQ,CAAC3K,OAAO,CAACkB,GAAG,CAAC0J,WAAW,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,YAAY,CAAC;EACf/K,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA,IAAI,CAACgL,SAAS,GAAGnN,MAAM,CAACoN,YAAY,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,EAAEC,KAAK,KAAK,CAAC,GAAGC,IAAI,KAAKC,UAAU,CAACF,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;IAC5G,IAAI,CAACE,MAAM,GAAGzN,MAAM,CAACC,MAAM,CAAC;EAChC;EACA0L,MAAMA,CAACL,OAAO,EAAE;IACZ,OAAO,IAAIlK,UAAU,CAAEsM,QAAQ,IAAK;MAChC,MAAMC,OAAO,GAAG,IAAIC,eAAe,CAAC,CAAC;MACrC,IAAI,CAACC,SAAS,CAACvC,OAAO,EAAEqC,OAAO,CAACG,MAAM,EAAEJ,QAAQ,CAAC,CAACK,IAAI,CAACC,IAAI,EAAGlD,KAAK,IAAK4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;QAAEE;MAAM,CAAC,CAAC,CAAC,CAAC;MACzH,OAAO,MAAM6C,OAAO,CAACM,KAAK,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACMJ,SAASA,CAACvC,OAAO,EAAEwC,MAAM,EAAEJ,QAAQ,EAAE;IAAA,IAAAQ,KAAA;IAAA,OAAAC,iBAAA;MACvC,MAAMnK,IAAI,GAAGkK,KAAI,CAACE,iBAAiB,CAAC9C,OAAO,CAAC;MAC5C,IAAIyB,QAAQ;MACZ,IAAI;QACA;QACA;QACA;QACA,MAAMsB,YAAY,GAAGH,KAAI,CAACT,MAAM,CAACa,iBAAiB,CAAC,MAAMJ,KAAI,CAACf,SAAS,CAAC7B,OAAO,CAAChC,aAAa,EAAE;UAAEwE,MAAM;UAAE,GAAG9J;QAAK,CAAC,CAAC,CAAC;QACpH;QACA;QACA;QACAuK,2CAA2C,CAACF,YAAY,CAAC;QACzD;QACAX,QAAQ,CAACc,IAAI,CAAC;UAAE3E,IAAI,EAAEI,aAAa,CAACwE;QAAK,CAAC,CAAC;QAC3C1B,QAAQ,SAASsB,YAAY;MACjC,CAAC,CACD,OAAOvD,KAAK,EAAE;QACV4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLT,MAAM,EAAES,KAAK,CAACT,MAAM,IAAI,CAAC;UACzBC,UAAU,EAAEQ,KAAK,CAACR,UAAU;UAC5B1B,GAAG,EAAE0C,OAAO,CAAChC,aAAa;UAC1BlH,OAAO,EAAE0I,KAAK,CAAC1I;QACnB,CAAC,CAAC,CAAC;QACH;MACJ;MACA,MAAMA,OAAO,GAAG,IAAIF,WAAW,CAAC6K,QAAQ,CAAC3K,OAAO,CAAC;MACjD,MAAMkI,UAAU,GAAGyC,QAAQ,CAACzC,UAAU;MACtC,MAAM1B,GAAG,GAAGkE,gBAAgB,CAACC,QAAQ,CAAC,IAAIzB,OAAO,CAAChC,aAAa;MAC/D,IAAIe,MAAM,GAAG0C,QAAQ,CAAC1C,MAAM;MAC5B,IAAItB,IAAI,GAAG,IAAI;MACf,IAAIuC,OAAO,CAACtC,cAAc,EAAE;QACxB0E,QAAQ,CAACc,IAAI,CAAC,IAAIhE,kBAAkB,CAAC;UAAEpI,OAAO;UAAEiI,MAAM;UAAEC,UAAU;UAAE1B;QAAI,CAAC,CAAC,CAAC;MAC/E;MACA,IAAImE,QAAQ,CAAChE,IAAI,EAAE;QACf;QACA,MAAM2F,aAAa,GAAG3B,QAAQ,CAAC3K,OAAO,CAACkB,GAAG,CAAC,gBAAgB,CAAC;QAC5D,MAAMqL,MAAM,GAAG,EAAE;QACjB,MAAMC,MAAM,GAAG7B,QAAQ,CAAChE,IAAI,CAAC8F,SAAS,CAAC,CAAC;QACxC,IAAIC,cAAc,GAAG,CAAC;QACtB,IAAIC,OAAO;QACX,IAAIC,WAAW;QACf;QACA;QACA,MAAMC,OAAO,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,OAAO;QAC3D;QACA;QACA;QACA,MAAMjB,KAAI,CAACT,MAAM,CAACa,iBAAiB,cAAAH,iBAAA,CAAC,aAAY;UAC5C,OAAO,IAAI,EAAE;YACT,MAAM;cAAEiB,IAAI;cAAElM;YAAM,CAAC,SAAS0L,MAAM,CAACS,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACN;YACJ;YACAT,MAAM,CAACpL,IAAI,CAACL,KAAK,CAAC;YAClB4L,cAAc,IAAI5L,KAAK,CAACe,MAAM;YAC9B,IAAIqH,OAAO,CAACtC,cAAc,EAAE;cACxBgG,WAAW,GACP1D,OAAO,CAACpC,YAAY,KAAK,MAAM,GACzB,CAAC8F,WAAW,IAAI,EAAE,IAChB,CAACD,OAAO,KAAK,IAAIO,WAAW,CAAC,CAAC,EAAEC,MAAM,CAACrM,KAAK,EAAE;gBAAEsM,MAAM,EAAE;cAAK,CAAC,CAAC,GACjExK,SAAS;cACnB,MAAMgE,cAAc,GAAGA,CAAA,KAAM0E,QAAQ,CAACc,IAAI,CAAC;gBACvC3E,IAAI,EAAEI,aAAa,CAACwF,gBAAgB;gBACpCC,KAAK,EAAEhB,aAAa,GAAG,CAACA,aAAa,GAAG1J,SAAS;gBACjD2K,MAAM,EAAEb,cAAc;gBACtBE;cACJ,CAAC,CAAC;cACFC,OAAO,GAAGA,OAAO,CAACW,GAAG,CAAC5G,cAAc,CAAC,GAAGA,cAAc,CAAC,CAAC;YAC5D;UACJ;QACJ,CAAC,EAAC;QACF;QACA,MAAM6G,SAAS,GAAG3B,KAAI,CAAC4B,YAAY,CAACnB,MAAM,EAAEG,cAAc,CAAC;QAC3D,IAAI;UACA,MAAMiB,WAAW,GAAGhD,QAAQ,CAAC3K,OAAO,CAACkB,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;UAC9DyF,IAAI,GAAGmF,KAAI,CAAC8B,SAAS,CAAC1E,OAAO,EAAEuE,SAAS,EAAEE,WAAW,CAAC;QAC1D,CAAC,CACD,OAAOjF,KAAK,EAAE;UACV;UACA4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;YACjCE,KAAK;YACL1I,OAAO,EAAE,IAAIF,WAAW,CAAC6K,QAAQ,CAAC3K,OAAO,CAAC;YAC1CiI,MAAM,EAAE0C,QAAQ,CAAC1C,MAAM;YACvBC,UAAU,EAAEyC,QAAQ,CAACzC,UAAU;YAC/B1B,GAAG,EAAEkE,gBAAgB,CAACC,QAAQ,CAAC,IAAIzB,OAAO,CAAChC;UAC/C,CAAC,CAAC,CAAC;UACH;QACJ;MACJ;MACA;MACA,IAAIe,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGtB,IAAI,GAAGgC,mBAAmB,GAAG,CAAC;MAC3C;MACA;MACA;MACA;MACA;MACA,MAAMR,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;MACxC,IAAIE,EAAE,EAAE;QACJmD,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;UAC3B3B,IAAI;UACJ3G,OAAO;UACPiI,MAAM;UACNC,UAAU;UACV1B;QACJ,CAAC,CAAC,CAAC;QACH;QACA;QACA8E,QAAQ,CAACuC,QAAQ,CAAC,CAAC;MACvB,CAAC,MACI;QACDvC,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK,EAAE/B,IAAI;UACX3G,OAAO;UACPiI,MAAM;UACNC,UAAU;UACV1B;QACJ,CAAC,CAAC,CAAC;MACP;IAAC;EACL;EACAoH,SAASA,CAAC1E,OAAO,EAAE4E,UAAU,EAAEH,WAAW,EAAE;IACxC,QAAQzE,OAAO,CAACpC,YAAY;MACxB,KAAK,MAAM;QACP;QACA,MAAMiH,IAAI,GAAG,IAAIb,WAAW,CAAC,CAAC,CAACC,MAAM,CAACW,UAAU,CAAC,CAAC/J,OAAO,CAACyG,aAAa,EAAE,EAAE,CAAC;QAC5E,OAAOuD,IAAI,KAAK,EAAE,GAAG,IAAI,GAAGzG,IAAI,CAAC0G,KAAK,CAACD,IAAI,CAAC;MAChD,KAAK,MAAM;QACP,OAAO,IAAIb,WAAW,CAAC,CAAC,CAACC,MAAM,CAACW,UAAU,CAAC;MAC/C,KAAK,MAAM;QACP,OAAO,IAAI5H,IAAI,CAAC,CAAC4H,UAAU,CAAC,EAAE;UAAErG,IAAI,EAAEkG;QAAY,CAAC,CAAC;MACxD,KAAK,aAAa;QACd,OAAOG,UAAU,CAACG,MAAM;IAChC;EACJ;EACAjC,iBAAiBA,CAAC5C,GAAG,EAAE;IACnB;IACA,MAAMpJ,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMkO,WAAW,GAAG9E,GAAG,CAACvC,eAAe,GAAG,SAAS,GAAGjE,SAAS;IAC/D;IACAwG,GAAG,CAACpJ,OAAO,CAACM,OAAO,CAAC,CAACI,IAAI,EAAEY,MAAM,KAAMtB,OAAO,CAACU,IAAI,CAAC,GAAGY,MAAM,CAACgE,IAAI,CAAC,GAAG,CAAE,CAAC;IACzE;IACA,IAAI,CAAC8D,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC,EAAE;MAC5BjB,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC;IAC3D;IACA;IACA,IAAI,CAACoJ,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC,EAAE;MAClC,MAAMkN,YAAY,GAAG/E,GAAG,CAAC5B,uBAAuB,CAAC,CAAC;MAClD;MACA,IAAI2G,YAAY,KAAK,IAAI,EAAE;QACvBnO,OAAO,CAAC,cAAc,CAAC,GAAGmO,YAAY;MAC1C;IACJ;IACA,OAAO;MACHxH,IAAI,EAAEyC,GAAG,CAAC/B,aAAa,CAAC,CAAC;MACzBvB,MAAM,EAAEsD,GAAG,CAACtD,MAAM;MAClB9F,OAAO;MACPkO;IACJ,CAAC;EACL;EACAR,YAAYA,CAACnB,MAAM,EAAE6B,WAAW,EAAE;IAC9B,MAAMX,SAAS,GAAG,IAAIY,UAAU,CAACD,WAAW,CAAC;IAC7C,IAAIE,QAAQ,GAAG,CAAC;IAChB,KAAK,MAAMC,KAAK,IAAIhC,MAAM,EAAE;MACxBkB,SAAS,CAACrM,GAAG,CAACmN,KAAK,EAAED,QAAQ,CAAC;MAC9BA,QAAQ,IAAIC,KAAK,CAAC1M,MAAM;IAC5B;IACA,OAAO4L,SAAS;EACpB;EACA;IAAS,IAAI,CAACzD,IAAI,YAAAwE,qBAAAtE,iBAAA;MAAA,YAAAA,iBAAA,IAAyFY,YAAY;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACV,KAAK,kBAzN8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAyNYkF,YAAY;MAAAR,OAAA,EAAZQ,YAAY,CAAAd;IAAA,EAAG;EAAE;AACpI;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KA3NqG9D,EAAE,CAAA6M,iBAAA,CA2NXO,YAAY,EAAc,CAAC;IAC3GrD,IAAI,EAAE9J;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMqN,YAAY,CAAC;AAEnB,SAASY,IAAIA,CAAA,EAAG,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,2CAA2CA,CAACsC,OAAO,EAAE;EAC1DA,OAAO,CAAC9C,IAAI,CAACC,IAAI,EAAEA,IAAI,CAAC;AAC5B;AAEA,SAAS8C,qBAAqBA,CAACtF,GAAG,EAAEuF,cAAc,EAAE;EAChD,OAAOA,cAAc,CAACvF,GAAG,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASwF,6BAA6BA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC7D,OAAO,CAACC,cAAc,EAAEJ,cAAc,KAAKG,WAAW,CAACE,SAAS,CAACD,cAAc,EAAE;IAC7ExF,MAAM,EAAG0F,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc;EAChF,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAACL,WAAW,EAAEM,aAAa,EAAEC,QAAQ,EAAE;EAChE,OAAO,CAACL,cAAc,EAAEJ,cAAc,KAAK7Q,qBAAqB,CAACsR,QAAQ,EAAE,MAAMD,aAAa,CAACJ,cAAc,EAAGE,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc,CAAC,CAAC,CAAC;AAC1L;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,iBAAiB,GAAG,IAAItR,cAAc,CAACyD,SAAS,GAAG,mBAAmB,GAAG,EAAE,CAAC;AAClF;AACA;AACA;AACA,MAAM8N,oBAAoB,GAAG,IAAIvR,cAAc,CAACyD,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA,MAAM+N,yBAAyB,GAAG,IAAIxR,cAAc,CAACyD,SAAS,GAAG,2BAA2B,GAAG,EAAE,CAAC;AAClG;AACA;AACA;AACA,MAAMgO,gCAAgC,GAAG,IAAIzR,cAAc,CAACyD,SAAS,GAAG,kCAAkC,GAAG,EAAE,EAAE;EAAEiO,UAAU,EAAE,MAAM;EAAEnF,OAAO,EAAEA,CAAA,KAAM;AAAK,CAAC,CAAC;AAC7J;AACA;AACA;AACA;AACA,SAASoF,0BAA0BA,CAAA,EAAG;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,CAACvG,GAAG,EAAEH,OAAO,KAAK;IACrB,IAAI0G,KAAK,KAAK,IAAI,EAAE;MAChB,MAAMC,YAAY,GAAGhS,MAAM,CAACyR,iBAAiB,EAAE;QAAEpE,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI,EAAE;MACxE;MACA;MACA;MACA;MACA0E,KAAK,GAAGC,YAAY,CAACC,WAAW,CAACjB,6BAA6B,EAAEF,qBAAqB,CAAC;IAC1F;IACA,MAAMoB,YAAY,GAAGlS,MAAM,CAACI,aAAa,CAAC;IAC1C,MAAM+R,qBAAqB,GAAGnS,MAAM,CAAC4R,gCAAgC,CAAC;IACtE,IAAIO,qBAAqB,EAAE;MACvB,MAAMC,MAAM,GAAGF,YAAY,CAACG,GAAG,CAAC,CAAC;MACjC,OAAON,KAAK,CAACvG,GAAG,EAAEH,OAAO,CAAC,CAACK,IAAI,CAACjK,QAAQ,CAAC,MAAMyQ,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC,MACI;MACD,OAAOL,KAAK,CAACvG,GAAG,EAAEH,OAAO,CAAC;IAC9B;EACJ,CAAC;AACL;AACA,IAAIkH,4BAA4B,GAAG,KAAK;AACxC;AACA,SAASC,4BAA4BA,CAAA,EAAG;EACpCD,4BAA4B,GAAG,KAAK;AACxC;AACA,MAAME,sBAAsB,SAASzQ,WAAW,CAAC;EAC7CG,WAAWA,CAACuQ,OAAO,EAAElB,QAAQ,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACkB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAClB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACO,KAAK,GAAG,IAAI;IACjB,IAAI,CAACG,YAAY,GAAGlS,MAAM,CAACI,aAAa,CAAC;IACzC,IAAI,CAAC+R,qBAAqB,GAAGnS,MAAM,CAAC4R,gCAAgC,CAAC;IACrE;IACA;IACA;IACA,IAAI,CAAC,OAAOhO,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC2O,4BAA4B,EAAE;MAClF,MAAMI,QAAQ,GAAG9Q,gBAAgB,CAAC2P,QAAQ,CAAClO,GAAG,CAACjD,WAAW,CAAC,CAAC;MAC5D,IAAIsS,QAAQ,IAAI,EAAE,IAAI,CAACD,OAAO,YAAYxF,YAAY,CAAC,EAAE;QACrDqF,4BAA4B,GAAG,IAAI;QACnCf,QAAQ,CACHlO,GAAG,CAAChD,QAAQ,CAAC,CACbsS,IAAI,CAACrS,mBAAmB,CAAC,IAAI,CAAC,uDAAuD,uDAAuD,GAC7I,oDAAoD,GACpD,iEAAiE,GACjE,4CAA4C,GAC5C,wEAAwE,GACxE,sCAAsC,CAAC,CAAC;MAChD;IACJ;EACJ;EACAoL,MAAMA,CAACwF,cAAc,EAAE;IACnB,IAAI,IAAI,CAACY,KAAK,KAAK,IAAI,EAAE;MACrB,MAAMc,qBAAqB,GAAG1O,KAAK,CAAC9C,IAAI,CAAC,IAAIyR,GAAG,CAAC,CAC7C,GAAG,IAAI,CAACtB,QAAQ,CAAClO,GAAG,CAACoO,oBAAoB,CAAC,EAC1C,GAAG,IAAI,CAACF,QAAQ,CAAClO,GAAG,CAACqO,yBAAyB,EAAE,EAAE,CAAC,CACtD,CAAC,CAAC;MACH;MACA;MACA;MACA;MACA,IAAI,CAACI,KAAK,GAAGc,qBAAqB,CAACZ,WAAW,CAAC,CAACc,eAAe,EAAExB,aAAa,KAAKD,oBAAoB,CAACyB,eAAe,EAAExB,aAAa,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAEV,qBAAqB,CAAC;IAClL;IACA,IAAI,IAAI,CAACqB,qBAAqB,EAAE;MAC5B,MAAMC,MAAM,GAAG,IAAI,CAACF,YAAY,CAACG,GAAG,CAAC,CAAC;MACtC,OAAO,IAAI,CAACN,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACqB,OAAO,CAAC/G,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC3F,IAAI,CAACjK,QAAQ,CAAC,MAAM,IAAI,CAACyQ,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAC3J,CAAC,MACI;MACD,OAAO,IAAI,CAACL,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACqB,OAAO,CAAC/G,MAAM,CAAC0F,iBAAiB,CAAC,CAAC;IACpG;EACJ;EACA;IAAS,IAAI,CAACjF,IAAI,YAAA4G,+BAAA1G,iBAAA;MAAA,YAAAA,iBAAA,IAAyFmG,sBAAsB,EAjWhC3S,EAAE,CAAAyM,QAAA,CAiWgDtK,WAAW,GAjW7DnC,EAAE,CAAAyM,QAAA,CAiWwEzM,EAAE,CAACmT,mBAAmB;IAAA,CAA6C;EAAE;EAChP;IAAS,IAAI,CAACzG,KAAK,kBAlW8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAkWYyK,sBAAsB;MAAA/F,OAAA,EAAtB+F,sBAAsB,CAAArG;IAAA,EAAG;EAAE;AAC9I;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KApWqG9D,EAAE,CAAA6M,iBAAA,CAoWX8F,sBAAsB,EAAc,CAAC;IACrH5I,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE5H;EAAY,CAAC,EAAE;IAAE4H,IAAI,EAAE/J,EAAE,CAACmT;EAAoB,CAAC,CAAC;AAAA;;AAE3F;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB;AACA;AACA,MAAMC,qBAAqB,GAAG,gDAAgD;AAC9E;AACA;AACA,MAAMC,sBAAsB,GAAG,+CAA+C;AAC9E,MAAMC,6BAA6B,GAAG,6CAA6C;AACnF;AACA;AACA,MAAMC,+BAA+B,GAAG,wCAAwC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOA,MAAM;EACjB;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBxR,WAAWA,CAACyR,WAAW,EAAEC,QAAQ,EAAE;IAC/B,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,OAAO,qBAAqBf,aAAa,EAAE,EAAE;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvH,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAACtD,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI3C,KAAK,CAAC8N,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAI7H,GAAG,CAACtC,YAAY,KAAK,MAAM,EAAE;MAClC,MAAM,IAAI3D,KAAK,CAAC+N,6BAA6B,CAAC;IAClD;IACA;IACA;IACA,IAAI9H,GAAG,CAACpJ,OAAO,CAAC8B,IAAI,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIsB,KAAK,CAACgO,+BAA+B,CAAC;IACpD;IACA;IACA,OAAO,IAAInS,UAAU,CAAEsM,QAAQ,IAAK;MAChC;MACA;MACA;MACA,MAAMwG,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACpC,MAAMrL,GAAG,GAAG4C,GAAG,CAAClC,aAAa,CAACnD,OAAO,CAAC,sBAAsB,EAAE,IAAI+N,QAAQ,IAAI,CAAC;MAC/E;MACA,MAAMC,IAAI,GAAG,IAAI,CAACN,QAAQ,CAACO,aAAa,CAAC,QAAQ,CAAC;MAClDD,IAAI,CAACE,GAAG,GAAGzL,GAAG;MACd;MACA;MACA;MACA,IAAIG,IAAI,GAAG,IAAI;MACf;MACA,IAAIuL,QAAQ,GAAG,KAAK;MACpB;MACA;MACA;MACA,IAAI,CAACV,WAAW,CAACM,QAAQ,CAAC,GAAIK,IAAI,IAAK;QACnC;QACA,OAAO,IAAI,CAACX,WAAW,CAACM,QAAQ,CAAC;QACjC;QACAnL,IAAI,GAAGwL,IAAI;QACXD,QAAQ,GAAG,IAAI;MACnB,CAAC;MACD;MACA;MACA;MACA,MAAME,OAAO,GAAGA,CAAA,KAAM;QAClBL,IAAI,CAACM,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;QACxCP,IAAI,CAACM,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;QAC1C;QACAR,IAAI,CAAC7B,MAAM,CAAC,CAAC;QACb;QACA;QACA,OAAO,IAAI,CAACsB,WAAW,CAACM,QAAQ,CAAC;MACrC,CAAC;MACD;MACA;MACA;MACA;MACA,MAAMQ,MAAM,GAAI7I,KAAK,IAAK;QACtB;QACA;QACA;QACA,IAAI,CAACiI,eAAe,CAAC/F,IAAI,CAAC,MAAM;UAC5B;UACAyG,OAAO,CAAC,CAAC;UACT;UACA,IAAI,CAACF,QAAQ,EAAE;YACX;YACA;YACA5G,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjChC,GAAG;cACHyB,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,aAAa;cACzBQ,KAAK,EAAE,IAAIvF,KAAK,CAAC6N,qBAAqB;YAC1C,CAAC,CAAC,CAAC;YACH;UACJ;UACA;UACA;UACA1F,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;YAC3B3B,IAAI;YACJsB,MAAM,EAAEU,mBAAmB;YAC3BT,UAAU,EAAE,IAAI;YAChB1B;UACJ,CAAC,CAAC,CAAC;UACH;UACA8E,QAAQ,CAACuC,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC;MACN,CAAC;MACD;MACA;MACA;MACA,MAAM0E,OAAO,GAAI7J,KAAK,IAAK;QACvB0J,OAAO,CAAC,CAAC;QACT;QACA9G,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLT,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,aAAa;UACzB1B;QACJ,CAAC,CAAC,CAAC;MACP,CAAC;MACD;MACA;MACAuL,IAAI,CAACS,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;MACrCP,IAAI,CAACS,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;MACvC,IAAI,CAACd,QAAQ,CAAC9K,IAAI,CAAC8L,WAAW,CAACV,IAAI,CAAC;MACpC;MACAzG,QAAQ,CAACc,IAAI,CAAC;QAAE3E,IAAI,EAAEI,aAAa,CAACwE;MAAK,CAAC,CAAC;MAC3C;MACA,OAAO,MAAM;QACT,IAAI,CAAC6F,QAAQ,EAAE;UACX,IAAI,CAACQ,eAAe,CAACX,IAAI,CAAC;QAC9B;QACA;QACAK,OAAO,CAAC,CAAC;MACb,CAAC;IACL,CAAC,CAAC;EACN;EACAM,eAAeA,CAACC,MAAM,EAAE;IACpB;IACA;IACA;IACA5B,eAAe,KAAK,IAAI,CAACU,QAAQ,CAACmB,cAAc,CAACC,kBAAkB,CAAC,CAAC;IACrE9B,eAAe,CAAC+B,SAAS,CAACH,MAAM,CAAC;EACrC;EACA;IAAS,IAAI,CAAC3I,IAAI,YAAA+I,2BAAA7I,iBAAA;MAAA,YAAAA,iBAAA,IAAyFqH,kBAAkB,EA5iB5B7T,EAAE,CAAAyM,QAAA,CA4iB4CiH,oBAAoB,GA5iBlE1T,EAAE,CAAAyM,QAAA,CA4iB6EzK,QAAQ;IAAA,CAA6C;EAAE;EACvO;IAAS,IAAI,CAAC0K,KAAK,kBA7iB8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EA6iBY2L,kBAAkB;MAAAjH,OAAA,EAAlBiH,kBAAkB,CAAAvH;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KA/iBqG9D,EAAE,CAAA6M,iBAAA,CA+iBXgH,kBAAkB,EAAc,CAAC;IACjH9J,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE2J;EAAqB,CAAC,EAAE;IAAE3J,IAAI,EAAE7E,SAAS;IAAEoQ,UAAU,EAAE,CAAC;MAC/EvL,IAAI,EAAErJ,MAAM;MACZ+M,IAAI,EAAE,CAACzL,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,SAASuT,kBAAkBA,CAAC7J,GAAG,EAAEgD,IAAI,EAAE;EACnC,IAAIhD,GAAG,CAACtD,MAAM,KAAK,OAAO,EAAE;IACxB,OAAOlI,MAAM,CAAC2T,kBAAkB,CAAC,CAAChI,MAAM,CAACH,GAAG,CAAC;EACjD;EACA;EACA,OAAOgD,IAAI,CAAChD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8J,gBAAgB,CAAC;EACnBnT,WAAWA,CAACqP,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,SAASA,CAACD,cAAc,EAAE3C,IAAI,EAAE;IAC5B,OAAOtO,qBAAqB,CAAC,IAAI,CAACsR,QAAQ,EAAE,MAAM6D,kBAAkB,CAAClE,cAAc,EAAGE,iBAAiB,IAAK7C,IAAI,CAAC7C,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC;EAChJ;EACA;IAAS,IAAI,CAACjF,IAAI,YAAAmJ,yBAAAjJ,iBAAA;MAAA,YAAAA,iBAAA,IAAyFgJ,gBAAgB,EArlB1BxV,EAAE,CAAAyM,QAAA,CAqlB0CzM,EAAE,CAACmT,mBAAmB;IAAA,CAA6C;EAAE;EAClN;IAAS,IAAI,CAACzG,KAAK,kBAtlB8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAslBYsN,gBAAgB;MAAA5I,OAAA,EAAhB4I,gBAAgB,CAAAlJ;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KAxlBqG9D,EAAE,CAAA6M,iBAAA,CAwlBX2I,gBAAgB,EAAc,CAAC;IAC/GzL,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE/J,EAAE,CAACmT;EAAoB,CAAC,CAAC;AAAA;AAEpE,MAAMuC,WAAW,GAAG,cAAc;AAClC;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAE;EACzB,IAAI,aAAa,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE;IACzC,OAAOD,GAAG,CAACC,WAAW;EAC1B;EACA,IAAI,kBAAkB,CAACC,IAAI,CAACF,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACtD,OAAOH,GAAG,CAACI,iBAAiB,CAAC,eAAe,CAAC;EACjD;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB5T,WAAWA,CAAC6T,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIrK,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAACtD,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAIzH,aAAa,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,OAAOmD,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,sNAAsN,CAAC;IAC/N;IACA;IACA;IACA;IACA,MAAMoS,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMC,MAAM,GAAGD,UAAU,CAACE,SAAS,GAC7B7U,IAAI,CAAC2U,UAAU,CAACE,SAAS,CAAC,CAAC,CAAC,GAC5B/U,EAAE,CAAC,IAAI,CAAC;IACd,OAAO8U,MAAM,CAACvK,IAAI,CAAChK,SAAS,CAAC,MAAM;MAC/B;MACA,OAAO,IAAIN,UAAU,CAAEsM,QAAQ,IAAK;QAChC;QACA;QACA,MAAMgI,GAAG,GAAGM,UAAU,CAACG,KAAK,CAAC,CAAC;QAC9BT,GAAG,CAACU,IAAI,CAAC5K,GAAG,CAACtD,MAAM,EAAEsD,GAAG,CAAClC,aAAa,CAAC;QACvC,IAAIkC,GAAG,CAACvC,eAAe,EAAE;UACrByM,GAAG,CAACzM,eAAe,GAAG,IAAI;QAC9B;QACA;QACAuC,GAAG,CAACpJ,OAAO,CAACM,OAAO,CAAC,CAACI,IAAI,EAAEY,MAAM,KAAKgS,GAAG,CAACW,gBAAgB,CAACvT,IAAI,EAAEY,MAAM,CAACgE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACnF;QACA,IAAI,CAAC8D,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,QAAQ,CAAC,EAAE;UAC5BqS,GAAG,CAACW,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC;QACvE;QACA;QACA,IAAI,CAAC7K,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC,EAAE;UAClC,MAAMkN,YAAY,GAAG/E,GAAG,CAAC5B,uBAAuB,CAAC,CAAC;UAClD;UACA,IAAI2G,YAAY,KAAK,IAAI,EAAE;YACvBmF,GAAG,CAACW,gBAAgB,CAAC,cAAc,EAAE9F,YAAY,CAAC;UACtD;QACJ;QACA;QACA,IAAI/E,GAAG,CAACtC,YAAY,EAAE;UAClB,MAAMA,YAAY,GAAGsC,GAAG,CAACtC,YAAY,CAACjG,WAAW,CAAC,CAAC;UACnD;UACA;UACA;UACA;UACA;UACAyS,GAAG,CAACxM,YAAY,GAAIA,YAAY,KAAK,MAAM,GAAGA,YAAY,GAAG,MAAO;QACxE;QACA;QACA,MAAMoN,OAAO,GAAG9K,GAAG,CAAC/B,aAAa,CAAC,CAAC;QACnC;QACA;QACA;QACA;QACA;QACA;QACA,IAAI8M,cAAc,GAAG,IAAI;QACzB;QACA;QACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAID,cAAc,KAAK,IAAI,EAAE;YACzB,OAAOA,cAAc;UACzB;UACA,MAAMjM,UAAU,GAAGoL,GAAG,CAACpL,UAAU,IAAI,IAAI;UACzC;UACA,MAAMlI,OAAO,GAAG,IAAIF,WAAW,CAACwT,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC;UAC5D;UACA;UACA,MAAMjN,GAAG,GAAG6M,cAAc,CAACC,GAAG,CAAC,IAAIlK,GAAG,CAAC5C,GAAG;UAC1C;UACA2N,cAAc,GAAG,IAAI/L,kBAAkB,CAAC;YAAEpI,OAAO;YAAEiI,MAAM,EAAEqL,GAAG,CAACrL,MAAM;YAAEC,UAAU;YAAE1B;UAAI,CAAC,CAAC;UACzF,OAAO2N,cAAc;QACzB,CAAC;QACD;QACA;QACA;QACA,MAAM7B,MAAM,GAAGA,CAAA,KAAM;UACjB;UACA,IAAI;YAAEtS,OAAO;YAAEiI,MAAM;YAAEC,UAAU;YAAE1B;UAAI,CAAC,GAAG4N,cAAc,CAAC,CAAC;UAC3D;UACA,IAAIzN,IAAI,GAAG,IAAI;UACf,IAAIsB,MAAM,KAAKW,2BAA2B,EAAE;YACxC;YACAjC,IAAI,GAAG,OAAO2M,GAAG,CAAC3I,QAAQ,KAAK,WAAW,GAAG2I,GAAG,CAACe,YAAY,GAAGf,GAAG,CAAC3I,QAAQ;UAChF;UACA;UACA,IAAI1C,MAAM,KAAK,CAAC,EAAE;YACdA,MAAM,GAAG,CAAC,CAACtB,IAAI,GAAGgC,mBAAmB,GAAG,CAAC;UAC7C;UACA;UACA;UACA;UACA;UACA,IAAIR,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;UACtC;UACA;UACA,IAAImB,GAAG,CAACtC,YAAY,KAAK,MAAM,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;YACzD;YACA,MAAM2N,YAAY,GAAG3N,IAAI;YACzBA,IAAI,GAAGA,IAAI,CAAC5C,OAAO,CAACqP,WAAW,EAAE,EAAE,CAAC;YACpC,IAAI;cACA;cACA;cACAzM,IAAI,GAAGA,IAAI,KAAK,EAAE,GAAGW,IAAI,CAAC0G,KAAK,CAACrH,IAAI,CAAC,GAAG,IAAI;YAChD,CAAC,CACD,OAAO+B,KAAK,EAAE;cACV;cACA;cACA;cACA/B,IAAI,GAAG2N,YAAY;cACnB;cACA;cACA,IAAInM,EAAE,EAAE;gBACJ;gBACAA,EAAE,GAAG,KAAK;gBACV;gBACAxB,IAAI,GAAG;kBAAE+B,KAAK;kBAAEqF,IAAI,EAAEpH;gBAAK,CAAC;cAChC;YACJ;UACJ;UACA,IAAIwB,EAAE,EAAE;YACJ;YACAmD,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;cAC3B3B,IAAI;cACJ3G,OAAO;cACPiI,MAAM;cACNC,UAAU;cACV1B,GAAG,EAAEA,GAAG,IAAI5D;YAChB,CAAC,CAAC,CAAC;YACH;YACA;YACA0I,QAAQ,CAACuC,QAAQ,CAAC,CAAC;UACvB,CAAC,MACI;YACD;YACAvC,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjC;cACAE,KAAK,EAAE/B,IAAI;cACX3G,OAAO;cACPiI,MAAM;cACNC,UAAU;cACV1B,GAAG,EAAEA,GAAG,IAAI5D;YAChB,CAAC,CAAC,CAAC;UACP;QACJ,CAAC;QACD;QACA;QACA;QACA,MAAM2P,OAAO,GAAI7J,KAAK,IAAK;UACvB,MAAM;YAAElC;UAAI,CAAC,GAAG4N,cAAc,CAAC,CAAC;UAChC,MAAMlP,GAAG,GAAG,IAAIsD,iBAAiB,CAAC;YAC9BE,KAAK;YACLT,MAAM,EAAEqL,GAAG,CAACrL,MAAM,IAAI,CAAC;YACvBC,UAAU,EAAEoL,GAAG,CAACpL,UAAU,IAAI,eAAe;YAC7C1B,GAAG,EAAEA,GAAG,IAAI5D;UAChB,CAAC,CAAC;UACF0I,QAAQ,CAAC5C,KAAK,CAACxD,GAAG,CAAC;QACvB,CAAC;QACD;QACA;QACA;QACA;QACA,IAAIqP,WAAW,GAAG,KAAK;QACvB;QACA;QACA,MAAMC,cAAc,GAAI/K,KAAK,IAAK;UAC9B;UACA,IAAI,CAAC8K,WAAW,EAAE;YACdjJ,QAAQ,CAACc,IAAI,CAACgI,cAAc,CAAC,CAAC,CAAC;YAC/BG,WAAW,GAAG,IAAI;UACtB;UACA;UACA;UACA,IAAIE,aAAa,GAAG;YAChBhN,IAAI,EAAEI,aAAa,CAACwF,gBAAgB;YACpCE,MAAM,EAAE9D,KAAK,CAAC8D;UAClB,CAAC;UACD;UACA,IAAI9D,KAAK,CAACiL,gBAAgB,EAAE;YACxBD,aAAa,CAACnH,KAAK,GAAG7D,KAAK,CAAC6D,KAAK;UACrC;UACA;UACA;UACA;UACA,IAAIlE,GAAG,CAACtC,YAAY,KAAK,MAAM,IAAI,CAAC,CAACwM,GAAG,CAACe,YAAY,EAAE;YACnDI,aAAa,CAAC7H,WAAW,GAAG0G,GAAG,CAACe,YAAY;UAChD;UACA;UACA/I,QAAQ,CAACc,IAAI,CAACqI,aAAa,CAAC;QAChC,CAAC;QACD;QACA;QACA,MAAME,YAAY,GAAIlL,KAAK,IAAK;UAC5B;UACA;UACA,IAAImL,QAAQ,GAAG;YACXnN,IAAI,EAAEI,aAAa,CAACgN,cAAc;YAClCtH,MAAM,EAAE9D,KAAK,CAAC8D;UAClB,CAAC;UACD;UACA;UACA,IAAI9D,KAAK,CAACiL,gBAAgB,EAAE;YACxBE,QAAQ,CAACtH,KAAK,GAAG7D,KAAK,CAAC6D,KAAK;UAChC;UACA;UACAhC,QAAQ,CAACc,IAAI,CAACwI,QAAQ,CAAC;QAC3B,CAAC;QACD;QACAtB,GAAG,CAACd,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;QACpCgB,GAAG,CAACd,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;QACtCe,GAAG,CAACd,gBAAgB,CAAC,SAAS,EAAED,OAAO,CAAC;QACxCe,GAAG,CAACd,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;QACtC;QACA,IAAInJ,GAAG,CAACxC,cAAc,EAAE;UACpB;UACA0M,GAAG,CAACd,gBAAgB,CAAC,UAAU,EAAEgC,cAAc,CAAC;UAChD;UACA,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;YAChCxB,GAAG,CAACwB,MAAM,CAACtC,gBAAgB,CAAC,UAAU,EAAEmC,YAAY,CAAC;UACzD;QACJ;QACA;QACArB,GAAG,CAACyB,IAAI,CAACb,OAAO,CAAC;QACjB5I,QAAQ,CAACc,IAAI,CAAC;UAAE3E,IAAI,EAAEI,aAAa,CAACwE;QAAK,CAAC,CAAC;QAC3C;QACA;QACA,OAAO,MAAM;UACT;UACAiH,GAAG,CAACjB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCe,GAAG,CAACjB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCe,GAAG,CAACjB,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;UACvCgB,GAAG,CAACjB,mBAAmB,CAAC,SAAS,EAAEE,OAAO,CAAC;UAC3C,IAAInJ,GAAG,CAACxC,cAAc,EAAE;YACpB0M,GAAG,CAACjB,mBAAmB,CAAC,UAAU,EAAEmC,cAAc,CAAC;YACnD,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;cAChCxB,GAAG,CAACwB,MAAM,CAACzC,mBAAmB,CAAC,UAAU,EAAEsC,YAAY,CAAC;YAC5D;UACJ;UACA;UACA,IAAIrB,GAAG,CAAC0B,UAAU,KAAK1B,GAAG,CAAC2B,IAAI,EAAE;YAC7B3B,GAAG,CAACzH,KAAK,CAAC,CAAC;UACf;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAAkL,uBAAAhL,iBAAA;MAAA,YAAAA,iBAAA,IAAyFyJ,cAAc,EAh3BxBjW,EAAE,CAAAyM,QAAA,CAg3BwC3K,EAAE,CAAC2V,UAAU;IAAA,CAA6C;EAAE;EACvM;IAAS,IAAI,CAAC/K,KAAK,kBAj3B8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAi3BY+N,cAAc;MAAArJ,OAAA,EAAdqJ,cAAc,CAAA3J;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KAn3BqG9D,EAAE,CAAA6M,iBAAA,CAm3BXoJ,cAAc,EAAc,CAAC;IAC7GlM,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAEjI,EAAE,CAAC2V;EAAW,CAAC,CAAC;AAAA;AAE3D,MAAMC,YAAY,GAAG,IAAIrX,cAAc,CAACyD,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC;AACxE,MAAM6T,wBAAwB,GAAG,YAAY;AAC7C,MAAMC,gBAAgB,GAAG,IAAIvX,cAAc,CAACyD,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EiO,UAAU,EAAE,MAAM;EAClBnF,OAAO,EAAEA,CAAA,KAAM+K;AACnB,CAAC,CAAC;AACF,MAAME,wBAAwB,GAAG,cAAc;AAC/C,MAAMC,gBAAgB,GAAG,IAAIzX,cAAc,CAACyD,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EiO,UAAU,EAAE,MAAM;EAClBnF,OAAO,EAAEA,CAAA,KAAMiL;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAME,sBAAsB,CAAC;AAE7B;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1B3V,WAAWA,CAAC4V,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACnC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CAAC;EACvB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,QAAQ,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,MAAMM,YAAY,GAAG,IAAI,CAACP,GAAG,CAACQ,MAAM,IAAI,EAAE;IAC1C,IAAID,YAAY,KAAK,IAAI,CAACJ,gBAAgB,EAAE;MACxC,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACD,SAAS,GAAGpW,iBAAiB,CAACuW,YAAY,EAAE,IAAI,CAACL,UAAU,CAAC;MACjE,IAAI,CAACC,gBAAgB,GAAGI,YAAY;IACxC;IACA,OAAO,IAAI,CAACH,SAAS;EACzB;EACA;IAAS,IAAI,CAAC/L,IAAI,YAAAoM,gCAAAlM,iBAAA;MAAA,YAAAA,iBAAA,IAAyFwL,uBAAuB,EAp6BjChY,EAAE,CAAAyM,QAAA,CAo6BiDzK,QAAQ,GAp6B3DhC,EAAE,CAAAyM,QAAA,CAo6BsElM,WAAW,GAp6BnFP,EAAE,CAAAyM,QAAA,CAo6B8FmL,gBAAgB;IAAA,CAA6C;EAAE;EAChQ;IAAS,IAAI,CAAClL,KAAK,kBAr6B8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAq6BY8P,uBAAuB;MAAApL,OAAA,EAAvBoL,uBAAuB,CAAA1L;IAAA,EAAG;EAAE;AAC/I;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KAv6BqG9D,EAAE,CAAA6M,iBAAA,CAu6BXmL,uBAAuB,EAAc,CAAC;IACtHjO,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE7E,SAAS;IAAEoQ,UAAU,EAAE,CAAC;MAC/CvL,IAAI,EAAErJ,MAAM;MACZ+M,IAAI,EAAE,CAACzL,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE+H,IAAI,EAAE7E,SAAS;IAAEoQ,UAAU,EAAE,CAAC;MAClCvL,IAAI,EAAErJ,MAAM;MACZ+M,IAAI,EAAE,CAAClN,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEwJ,IAAI,EAAE7E,SAAS;IAAEoQ,UAAU,EAAE,CAAC;MAClCvL,IAAI,EAAErJ,MAAM;MACZ+M,IAAI,EAAE,CAACmK,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAASe,iBAAiBA,CAACjN,GAAG,EAAEgD,IAAI,EAAE;EAClC,MAAMkK,KAAK,GAAGlN,GAAG,CAAC5C,GAAG,CAAC3F,WAAW,CAAC,CAAC;EACnC;EACA;EACA;EACA;EACA,IAAI,CAACjD,MAAM,CAACwX,YAAY,CAAC,IACrBhM,GAAG,CAACtD,MAAM,KAAK,KAAK,IACpBsD,GAAG,CAACtD,MAAM,KAAK,MAAM,IACrBwQ,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAC3BD,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC9B,OAAOnK,IAAI,CAAChD,GAAG,CAAC;EACpB;EACA,MAAMxD,KAAK,GAAGhI,MAAM,CAAC6X,sBAAsB,CAAC,CAACQ,QAAQ,CAAC,CAAC;EACvD,MAAMO,UAAU,GAAG5Y,MAAM,CAAC4X,gBAAgB,CAAC;EAC3C;EACA,IAAI5P,KAAK,IAAI,IAAI,IAAI,CAACwD,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAACuV,UAAU,CAAC,EAAE;IAC/CpN,GAAG,GAAGA,GAAG,CAAClH,KAAK,CAAC;MAAElC,OAAO,EAAEoJ,GAAG,CAACpJ,OAAO,CAACoB,GAAG,CAACoV,UAAU,EAAE5Q,KAAK;IAAE,CAAC,CAAC;EACpE;EACA,OAAOwG,IAAI,CAAChD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMqN,mBAAmB,CAAC;EACtB1W,WAAWA,CAACqP,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAJ,SAASA,CAACD,cAAc,EAAE3C,IAAI,EAAE;IAC5B,OAAOtO,qBAAqB,CAAC,IAAI,CAACsR,QAAQ,EAAE,MAAMiH,iBAAiB,CAACtH,cAAc,EAAGE,iBAAiB,IAAK7C,IAAI,CAAC7C,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC;EAC/I;EACA;IAAS,IAAI,CAACjF,IAAI,YAAA0M,4BAAAxM,iBAAA;MAAA,YAAAA,iBAAA,IAAyFuM,mBAAmB,EAl9B7B/Y,EAAE,CAAAyM,QAAA,CAk9B6CzM,EAAE,CAACmT,mBAAmB;IAAA,CAA6C;EAAE;EACrN;IAAS,IAAI,CAACzG,KAAK,kBAn9B8E1M,EAAE,CAAA2M,kBAAA;MAAAzE,KAAA,EAm9BY6Q,mBAAmB;MAAAnM,OAAA,EAAnBmM,mBAAmB,CAAAzM;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAxI,SAAA,oBAAAA,SAAA,KAr9BqG9D,EAAE,CAAA6M,iBAAA,CAq9BXkM,mBAAmB,EAAc,CAAC;IAClHhP,IAAI,EAAE9J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8J,IAAI,EAAE/J,EAAE,CAACmT;EAAoB,CAAC,CAAC;AAAA;;AAEpE;AACA;AACA;AACA;AACA;AACA,IAAI8F,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACjFA,eAAe,CAACA,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB;EAC3FA,eAAe,CAACA,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EAC7EA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB;EACvFA,eAAe,CAACA,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC3D,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACtC,OAAO;IACHC,KAAK,EAAEF,IAAI;IACXG,UAAU,EAAEF;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAC,GAAGC,QAAQ,EAAE;EACpC,IAAI1V,SAAS,EAAE;IACX,MAAM2V,YAAY,GAAG,IAAIzG,GAAG,CAACwG,QAAQ,CAAC9X,GAAG,CAAEgY,CAAC,IAAKA,CAAC,CAACL,KAAK,CAAC,CAAC;IAC1D,IAAII,YAAY,CAAClW,GAAG,CAAC0V,eAAe,CAACU,gBAAgB,CAAC,IAClDF,YAAY,CAAClW,GAAG,CAAC0V,eAAe,CAACW,uBAAuB,CAAC,EAAE;MAC3D,MAAM,IAAInU,KAAK,CAAC3B,SAAS,GACnB,uJAAuJ,GACvJ,EAAE,CAAC;IACb;EACJ;EACA,MAAMsV,SAAS,GAAG,CACd9N,UAAU,EACV2K,cAAc,EACdtD,sBAAsB,EACtB;IAAEkH,OAAO,EAAE3X,WAAW;IAAE4X,WAAW,EAAEnH;EAAuB,CAAC,EAC7D;IACIkH,OAAO,EAAE1X,WAAW;IACpB4X,UAAU,EAAEA,CAAA,KAAM;MACd,OAAO7Z,MAAM,CAACkN,YAAY,EAAE;QAAEG,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAIrN,MAAM,CAAC+V,cAAc,CAAC;IAC7E;EACJ,CAAC,EACD;IACI4D,OAAO,EAAEjI,oBAAoB;IAC7BoI,QAAQ,EAAErB,iBAAiB;IAC3BsB,KAAK,EAAE;EACX,CAAC,EACD;IAAEJ,OAAO,EAAEnC,YAAY;IAAEsC,QAAQ,EAAE;EAAK,CAAC,EACzC;IAAEH,OAAO,EAAE9B,sBAAsB;IAAEmC,QAAQ,EAAElC;EAAwB,CAAC,CACzE;EACD,KAAK,MAAMmC,OAAO,IAAIX,QAAQ,EAAE;IAC5BJ,SAAS,CAAC3V,IAAI,CAAC,GAAG0W,OAAO,CAACb,UAAU,CAAC;EACzC;EACA,OAAO1Y,wBAAwB,CAACwY,SAAS,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOnB,eAAe,CAACD,eAAe,CAACqB,YAAY,EAAED,cAAc,CAAC3Y,GAAG,CAAE+P,aAAa,IAAK;IACvF,OAAO;MACHoI,OAAO,EAAEjI,oBAAoB;MAC7BoI,QAAQ,EAAEvI,aAAa;MACvBwI,KAAK,EAAE;IACX,CAAC;EACL,CAAC,CAAC,CAAC;AACP;AACA,MAAMM,qBAAqB,GAAG,IAAIla,cAAc,CAACyD,SAAS,GAAG,uBAAuB,GAAG,EAAE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0W,sBAAsBA,CAAA,EAAG;EAC9B;EACA;EACA;EACA;EACA;EACA,OAAOtB,eAAe,CAACD,eAAe,CAACwB,kBAAkB,EAAE,CACvD;IACIZ,OAAO,EAAEU,qBAAqB;IAC9BR,UAAU,EAAE/H;EAChB,CAAC,EACD;IACI6H,OAAO,EAAEjI,oBAAoB;IAC7BkI,WAAW,EAAES,qBAAqB;IAClCN,KAAK,EAAE;EACX,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqBA,CAAC;EAAEvC,UAAU;EAAEW;AAAY,CAAC,EAAE;EACxD,MAAMM,SAAS,GAAG,EAAE;EACpB,IAAIjB,UAAU,KAAKjT,SAAS,EAAE;IAC1BkU,SAAS,CAAC3V,IAAI,CAAC;MAAEoW,OAAO,EAAEjC,gBAAgB;MAAEoC,QAAQ,EAAE7B;IAAW,CAAC,CAAC;EACvE;EACA,IAAIW,UAAU,KAAK5T,SAAS,EAAE;IAC1BkU,SAAS,CAAC3V,IAAI,CAAC;MAAEoW,OAAO,EAAE/B,gBAAgB;MAAEkC,QAAQ,EAAElB;IAAW,CAAC,CAAC;EACvE;EACA,OAAOI,eAAe,CAACD,eAAe,CAACW,uBAAuB,EAAER,SAAS,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,oBAAoBA,CAAA,EAAG;EAC5B,OAAOzB,eAAe,CAACD,eAAe,CAACU,gBAAgB,EAAE,CACrD;IACIE,OAAO,EAAEnC,YAAY;IACrBsC,QAAQ,EAAE;EACd,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,gBAAgBA,CAAA,EAAG;EACxB,OAAO1B,eAAe,CAACD,eAAe,CAAC4B,YAAY,EAAE,CACjDhH,kBAAkB,EAClB;IAAEgG,OAAO,EAAEnG,oBAAoB;IAAEqG,UAAU,EAAEpG;EAAqB,CAAC,EACnE;IAAEkG,OAAO,EAAEjI,oBAAoB;IAAEoI,QAAQ,EAAEzE,kBAAkB;IAAE0E,KAAK,EAAE;EAAK,CAAC,CAC/E,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,yBAAyBA,CAAA,EAAG;EACjC,OAAO5B,eAAe,CAACD,eAAe,CAAC8B,qBAAqB,EAAE,CAC1D;IACIlB,OAAO,EAAE1X,WAAW;IACpB4X,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMiB,iBAAiB,GAAG9a,MAAM,CAACgC,WAAW,EAAE;QAAE+Y,QAAQ,EAAE,IAAI;QAAE1N,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjF,IAAIzJ,SAAS,IAAIkX,iBAAiB,KAAK,IAAI,EAAE;QACzC,MAAM,IAAIvV,KAAK,CAAC,kGAAkG,CAAC;MACvH;MACA,OAAOuV,iBAAiB;IAC5B;EACJ,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAAA,EAAG;EACjB,OAAOhC,eAAe,CAACD,eAAe,CAACkC,KAAK,EAAE,CAC1C/N,YAAY,EACZ;IAAEyM,OAAO,EAAE1X,WAAW;IAAE2X,WAAW,EAAE1M;EAAa,CAAC,CACtD,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgO,oBAAoB,CAAC;EACvB;AACJ;AACA;EACI,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,oBAAoB;MAC9BhC,SAAS,EAAE,CAACuB,oBAAoB,CAAC,CAAC,CAACrB,UAAU;IACjD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOiC,WAAWA,CAACrU,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO;MACHoU,QAAQ,EAAEF,oBAAoB;MAC9BhC,SAAS,EAAEsB,qBAAqB,CAACxT,OAAO,CAAC,CAACoS;IAC9C,CAAC;EACL;EACA;IAAS,IAAI,CAAChN,IAAI,YAAAkP,6BAAAhP,iBAAA;MAAA,YAAAA,iBAAA,IAAyF4O,oBAAoB;IAAA,CAAkD;EAAE;EACnL;IAAS,IAAI,CAACK,IAAI,kBA/tC+Ezb,EAAE,CAAA0b,gBAAA;MAAA3R,IAAA,EA+tCSqR;IAAoB,EAAG;EAAE;EACrI;IAAS,IAAI,CAACO,IAAI,kBAhuC+E3b,EAAE,CAAA4b,gBAAA;MAAAxC,SAAA,EAguC0C,CACrIL,mBAAmB,EACnB;QAAEc,OAAO,EAAElI,iBAAiB;QAAEmI,WAAW,EAAEf,mBAAmB;QAAEkB,KAAK,EAAE;MAAK,CAAC,EAC7E;QAAEJ,OAAO,EAAE9B,sBAAsB;QAAEmC,QAAQ,EAAElC;MAAwB,CAAC,EACtE0C,qBAAqB,CAAC;QAClBvC,UAAU,EAAER,wBAAwB;QACpCmB,UAAU,EAAEjB;MAChB,CAAC,CAAC,CAACyB,UAAU,EACb;QAAEO,OAAO,EAAEnC,YAAY;QAAEsC,QAAQ,EAAE;MAAK,CAAC;IAC5C,EAAG;EAAE;AACd;AACA;EAAA,QAAAlW,SAAA,oBAAAA,SAAA,KA3uCqG9D,EAAE,CAAA6M,iBAAA,CA2uCXuO,oBAAoB,EAAc,CAAC;IACnHrR,IAAI,EAAElJ,QAAQ;IACd4M,IAAI,EAAE,CAAC;MACC2L,SAAS,EAAE,CACPL,mBAAmB,EACnB;QAAEc,OAAO,EAAElI,iBAAiB;QAAEmI,WAAW,EAAEf,mBAAmB;QAAEkB,KAAK,EAAE;MAAK,CAAC,EAC7E;QAAEJ,OAAO,EAAE9B,sBAAsB;QAAEmC,QAAQ,EAAElC;MAAwB,CAAC,EACtE0C,qBAAqB,CAAC;QAClBvC,UAAU,EAAER,wBAAwB;QACpCmB,UAAU,EAAEjB;MAChB,CAAC,CAAC,CAACyB,UAAU,EACb;QAAEO,OAAO,EAAEnC,YAAY;QAAEsC,QAAQ,EAAE;MAAK,CAAC;IAEjD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACvP,IAAI,YAAAwP,yBAAAtP,iBAAA;MAAA,YAAAA,iBAAA,IAAyFqP,gBAAgB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACJ,IAAI,kBAtwC+Ezb,EAAE,CAAA0b,gBAAA;MAAA3R,IAAA,EAswCS8R;IAAgB,EAAG;EAAE;EACjI;IAAS,IAAI,CAACF,IAAI,kBAvwC+E3b,EAAE,CAAA4b,gBAAA;MAAAxC,SAAA,EAuwCsC,CAACG,iBAAiB,CAACiB,sBAAsB,CAAC,CAAC,CAAC;IAAC,EAAG;EAAE;AAC/L;AACA;EAAA,QAAA1W,SAAA,oBAAAA,SAAA,KAzwCqG9D,EAAE,CAAA6M,iBAAA,CAywCXgP,gBAAgB,EAAc,CAAC;IAC/G9R,IAAI,EAAElJ,QAAQ;IACd4M,IAAI,EAAE,CAAC;MACC;AACpB;AACA;AACA;MACoB2L,SAAS,EAAE,CAACG,iBAAiB,CAACiB,sBAAsB,CAAC,CAAC,CAAC;IAC3D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,qBAAqB,CAAC;EACxB;IAAS,IAAI,CAACzP,IAAI,YAAA0P,8BAAAxP,iBAAA;MAAA,YAAAA,iBAAA,IAAyFuP,qBAAqB;IAAA,CAAkD;EAAE;EACpL;IAAS,IAAI,CAACN,IAAI,kBA9xC+Ezb,EAAE,CAAA0b,gBAAA;MAAA3R,IAAA,EA8xCSgS;IAAqB,EAAG;EAAE;EACtI;IAAS,IAAI,CAACJ,IAAI,kBA/xC+E3b,EAAE,CAAA4b,gBAAA;MAAAxC,SAAA,EA+xC2C,CAACwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;IAAC,EAAG;EAAE;AACtL;AACA;EAAA,QAAAxV,SAAA,oBAAAA,SAAA,KAjyCqG9D,EAAE,CAAA6M,iBAAA,CAiyCXkP,qBAAqB,EAAc,CAAC;IACpHhS,IAAI,EAAElJ,QAAQ;IACd4M,IAAI,EAAE,CAAC;MACC2L,SAAS,EAAE,CAACwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,8BAA8B,GAAG,IAAI5b,cAAc,CAACyD,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AAC5G;AACA;AACA;AACA,MAAMoY,IAAI,GAAG,GAAG;AAChB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,MAAM,GAAG,GAAG;AAClB,MAAMC,WAAW,GAAG,IAAI;AACxB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,aAAa,GAAG,IAAI;AAC1B,MAAMC,aAAa,GAAG,IAAInc,cAAc,CAACyD,SAAS,GAAG,mCAAmC,GAAG,EAAE,CAAC;AAC9F;AACA;AACA;AACA,MAAM2Y,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AACvC,SAASC,0BAA0BA,CAAChR,GAAG,EAAEgD,IAAI,EAAE;EAC3C,MAAM;IAAEiO,aAAa;IAAE,GAAGC;EAAc,CAAC,GAAG1c,MAAM,CAACsc,aAAa,CAAC;EACjE,MAAM;IAAEjT,aAAa,EAAEsT,cAAc;IAAEzU,MAAM,EAAE0U;EAAc,CAAC,GAAGpR,GAAG;EACpE;EACA,IAAI,CAACiR,aAAa,IACdE,cAAc,KAAK,KAAK;EACxB;EACCC,aAAa,KAAK,MAAM,IAAI,CAACF,aAAa,CAACG,mBAAmB,IAAI,CAACF,cAAe,IAClFC,aAAa,KAAK,MAAM,IAAI,CAACL,eAAe,CAACO,QAAQ,CAACF,aAAa,CAAE;EACtE;EACC,CAACF,aAAa,CAACK,8BAA8B,IAAIC,cAAc,CAACxR,GAAG,CAAE,IACtEkR,aAAa,CAACnb,MAAM,GAAGiK,GAAG,CAAC,KAAK,KAAK,EAAE;IACvC,OAAOgD,IAAI,CAAChD,GAAG,CAAC;EACpB;EACA,MAAMyR,aAAa,GAAGjd,MAAM,CAACY,aAAa,CAAC;EAC3C,MAAMsc,SAAS,GAAGld,MAAM,CAAC+b,8BAA8B,EAAE;IACrD1O,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,MAAMsF,QAAQ,GAAG9Q,gBAAgB,CAAC7B,MAAM,CAACK,WAAW,CAAC,CAAC;EACtD,IAAI6c,SAAS,IAAI,CAACvK,QAAQ,EAAE;IACxB,MAAM,IAAIlS,aAAa,CAAC,IAAI,CAAC,uDAAuDmD,SAAS,IACzF,qFAAqF,GACjF,yFAAyF,GACzF,iCAAiC,CAAC;EAC9C;EACA,MAAMuZ,UAAU,GAAGxK,QAAQ,IAAIuK,SAAS,GAAGE,mBAAmB,CAAC5R,GAAG,CAAC5C,GAAG,EAAEsU,SAAS,CAAC,GAAG1R,GAAG,CAAC5C,GAAG;EAC5F,MAAMyU,QAAQ,GAAGC,YAAY,CAAC9R,GAAG,EAAE2R,UAAU,CAAC;EAC9C,MAAMpQ,QAAQ,GAAGkQ,aAAa,CAAC3Z,GAAG,CAAC+Z,QAAQ,EAAE,IAAI,CAAC;EAClD,IAAIE,gBAAgB,GAAGb,aAAa,CAACc,cAAc;EACnD,IAAI,OAAOb,cAAc,KAAK,QAAQ,IAAIA,cAAc,CAACa,cAAc,EAAE;IACrE;IACAD,gBAAgB,GAAGZ,cAAc,CAACa,cAAc;EACpD;EACA,IAAIzQ,QAAQ,EAAE;IACV,MAAM;MAAE,CAACiP,IAAI,GAAGyB,aAAa;MAAE,CAACpB,aAAa,GAAGnT,YAAY;MAAE,CAAC+S,OAAO,GAAGyB,WAAW;MAAE,CAACxB,MAAM,GAAG7R,MAAM;MAAE,CAAC8R,WAAW,GAAG7R,UAAU;MAAE,CAAC8R,OAAO,GAAGxT;IAAK,CAAC,GAAGmE,QAAQ;IAC/J;IACA,IAAIhE,IAAI,GAAG0U,aAAa;IACxB,QAAQvU,YAAY;MAChB,KAAK,aAAa;QACdH,IAAI,GAAG,IAAI4U,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,aAAa,CAAC,CAACpN,MAAM;QACrD;MACJ,KAAK,MAAM;QACPtH,IAAI,GAAG,IAAIT,IAAI,CAAC,CAACmV,aAAa,CAAC,CAAC;QAChC;IACR;IACA;IACA;IACA;IACA,IAAIrb,OAAO,GAAG,IAAIF,WAAW,CAACwb,WAAW,CAAC;IAC1C,IAAI,OAAO9Z,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C;MACA;MACA;MACAxB,OAAO,GAAGyb,6BAA6B,CAACrS,GAAG,CAAC5C,GAAG,EAAExG,OAAO,EAAEmb,gBAAgB,IAAI,EAAE,CAAC;IACrF;IACA,OAAOpc,EAAE,CAAC,IAAIuJ,YAAY,CAAC;MACvB3B,IAAI;MACJ3G,OAAO;MACPiI,MAAM;MACNC,UAAU;MACV1B;IACJ,CAAC,CAAC,CAAC;EACP;EACA;EACA,OAAO4F,IAAI,CAAChD,GAAG,CAAC,CAACE,IAAI,CAAC/J,GAAG,CAAEkK,KAAK,IAAK;IACjC,IAAIA,KAAK,YAAYnB,YAAY,IAAIiI,QAAQ,EAAE;MAC3CsK,aAAa,CAACzZ,GAAG,CAAC6Z,QAAQ,EAAE;QACxB,CAACrB,IAAI,GAAGnQ,KAAK,CAAC9C,IAAI;QAClB,CAACkT,OAAO,GAAG6B,kBAAkB,CAACjS,KAAK,CAACzJ,OAAO,EAAEmb,gBAAgB,CAAC;QAC9D,CAACrB,MAAM,GAAGrQ,KAAK,CAACxB,MAAM;QACtB,CAAC8R,WAAW,GAAGtQ,KAAK,CAACvB,UAAU;QAC/B,CAAC8R,OAAO,GAAGe,UAAU;QACrB,CAACd,aAAa,GAAG7Q,GAAG,CAACtC;MACzB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC,CAAC;AACP;AACA;AACA,SAAS8T,cAAcA,CAACxR,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,eAAe,CAAC,IAAImI,GAAG,CAACpJ,OAAO,CAACiB,GAAG,CAAC,qBAAqB,CAAC;AACrF;AACA,SAASya,kBAAkBA,CAAC1b,OAAO,EAAEob,cAAc,EAAE;EACjD,IAAI,CAACA,cAAc,EAAE;IACjB,OAAO,CAAC,CAAC;EACb;EACA,MAAMO,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAM/a,GAAG,IAAIwa,cAAc,EAAE;IAC9B,MAAM9Z,MAAM,GAAGtB,OAAO,CAACgC,MAAM,CAACpB,GAAG,CAAC;IAClC,IAAIU,MAAM,KAAK,IAAI,EAAE;MACjBqa,UAAU,CAAC/a,GAAG,CAAC,GAAGU,MAAM;IAC5B;EACJ;EACA,OAAOqa,UAAU;AACrB;AACA,SAASC,mBAAmBA,CAAC9X,MAAM,EAAE;EACjC,OAAO,CAAC,GAAGA,MAAM,CAAChC,IAAI,CAAC,CAAC,CAAC,CACpB+Z,IAAI,CAAC,CAAC,CACNzc,GAAG,CAAE0c,CAAC,IAAK,GAAGA,CAAC,IAAIhY,MAAM,CAAC9B,MAAM,CAAC8Z,CAAC,CAAC,EAAE,CAAC,CACtCxW,IAAI,CAAC,GAAG,CAAC;AAClB;AACA,SAAS4V,YAAYA,CAAChS,OAAO,EAAE6S,gBAAgB,EAAE;EAC7C;EACA,MAAM;IAAEjY,MAAM;IAAEgC,MAAM;IAAEgB;EAAa,CAAC,GAAGoC,OAAO;EAChD,MAAM8S,aAAa,GAAGJ,mBAAmB,CAAC9X,MAAM,CAAC;EACjD,IAAImY,cAAc,GAAG/S,OAAO,CAAC7B,aAAa,CAAC,CAAC;EAC5C,IAAI4U,cAAc,YAAY3V,eAAe,EAAE;IAC3C2V,cAAc,GAAGL,mBAAmB,CAACK,cAAc,CAAC;EACxD,CAAC,MACI,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;IACzCA,cAAc,GAAG,EAAE;EACvB;EACA,MAAMrb,GAAG,GAAG,CAACkF,MAAM,EAAEgB,YAAY,EAAEiV,gBAAgB,EAAEE,cAAc,EAAED,aAAa,CAAC,CAAC1W,IAAI,CAAC,GAAG,CAAC;EAC7F,MAAM4W,IAAI,GAAGC,YAAY,CAACvb,GAAG,CAAC;EAC9B,OAAOnC,YAAY,CAACyd,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACrb,KAAK,EAAE;EACzB,IAAIob,IAAI,GAAG,CAAC;EACZ,KAAK,MAAME,IAAI,IAAItb,KAAK,EAAE;IACtBob,IAAI,GAAIG,IAAI,CAACC,IAAI,CAAC,EAAE,EAAEJ,IAAI,CAAC,GAAGE,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAK,CAAC;EAC1D;EACA;EACA;EACAL,IAAI,IAAI,UAAU,GAAG,CAAC;EACtB,OAAOA,IAAI,CAACjZ,QAAQ,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuZ,qBAAqBA,CAACC,YAAY,EAAE;EACzC,OAAO,CACH;IACIlF,OAAO,EAAE2C,aAAa;IACtBzC,UAAU,EAAEA,CAAA,KAAM;MACd/Y,uBAAuB,CAAC,qBAAqB,CAAC;MAC9C,OAAO;QAAE2b,aAAa,EAAE,IAAI;QAAE,GAAGoC;MAAa,CAAC;IACnD;EACJ,CAAC,EACD;IACIlF,OAAO,EAAEhI,yBAAyB;IAClCmI,QAAQ,EAAE0C,0BAA0B;IACpCzC,KAAK,EAAE,IAAI;IACX+E,IAAI,EAAE,CAACle,aAAa,EAAE0b,aAAa;EACvC,CAAC,EACD;IACI3C,OAAO,EAAE5Y,sBAAsB;IAC/BgZ,KAAK,EAAE,IAAI;IACXF,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMkF,MAAM,GAAG/e,MAAM,CAACgB,cAAc,CAAC;MACrC,MAAMge,UAAU,GAAGhf,MAAM,CAACsc,aAAa,CAAC;MACxC,OAAO,MAAM;QACTrb,WAAW,CAAC8d,MAAM,CAAC,CAAChR,IAAI,CAAC,MAAM;UAC3BiR,UAAU,CAACvC,aAAa,GAAG,KAAK;QACpC,CAAC,CAAC;MACN,CAAC;IACL;EACJ,CAAC,CACJ;AACL;AACA;AACA;AACA;AACA;AACA,SAASoB,6BAA6BA,CAACjV,GAAG,EAAExG,OAAO,EAAEmb,gBAAgB,EAAE;EACnE,MAAM0B,eAAe,GAAG,IAAInM,GAAG,CAAC,CAAC;EACjC,OAAO,IAAIoM,KAAK,CAAC9c,OAAO,EAAE;IACtBkB,GAAGA,CAAC6b,MAAM,EAAEC,IAAI,EAAE;MACd,MAAMlc,KAAK,GAAGmc,OAAO,CAAC/b,GAAG,CAAC6b,MAAM,EAAEC,IAAI,CAAC;MACvC,MAAME,OAAO,GAAG,IAAIxM,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;MACjD,IAAI,OAAO5P,KAAK,KAAK,UAAU,IAAI,CAACoc,OAAO,CAACjc,GAAG,CAAC+b,IAAI,CAAC,EAAE;QACnD,OAAOlc,KAAK;MAChB;MACA,OAAQ0V,UAAU,IAAK;QACnB;QACA,MAAM5V,GAAG,GAAG,CAACoc,IAAI,GAAG,GAAG,GAAGxG,UAAU,EAAE3V,WAAW,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,CAACsa,gBAAgB,CAACT,QAAQ,CAAClE,UAAU,CAAC,IAAI,CAACqG,eAAe,CAAC5b,GAAG,CAACL,GAAG,CAAC,EAAE;UACrEic,eAAe,CAAC5M,GAAG,CAACrP,GAAG,CAAC;UACxB,MAAMuc,YAAY,GAAGre,eAAe,CAAC0H,GAAG,CAAC;UACzC;UACA4W,OAAO,CAAC5M,IAAI,CAACrS,mBAAmB,CAAC,IAAI,CAAC,0DAA0D,+BAA+BqY,UAAU,qDAAqD,GAC1L,8EAA8E,GAC9E,iCAAiCA,UAAU,uBAAuB2G,YAAY,cAAc,GAC5F,gFAAgF,GAChF,qFAAqF,GACrF,2EAA2E,GAC3E,qCAAqC,CAAC,CAAC;QAC/C;QACA;QACA,OAAOrc,KAAK,CAACuc,KAAK,CAACN,MAAM,EAAE,CAACvG,UAAU,CAAC,CAAC;MAC5C,CAAC;IACL;EACJ,CAAC,CAAC;AACN;AACA,SAASwE,mBAAmBA,CAACxU,GAAG,EAAEsU,SAAS,EAAE;EACzC,MAAMwC,MAAM,GAAG,IAAIC,GAAG,CAAC/W,GAAG,EAAE,YAAY,CAAC,CAAC8W,MAAM;EAChD,MAAME,YAAY,GAAG1C,SAAS,CAACwC,MAAM,CAAC;EACtC,IAAI,CAACE,YAAY,EAAE;IACf,OAAOhX,GAAG;EACd;EACA,IAAI,OAAOhF,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/Cic,kBAAkB,CAACD,YAAY,CAAC;EACpC;EACA,OAAOhX,GAAG,CAACzC,OAAO,CAACuZ,MAAM,EAAEE,YAAY,CAAC;AAC5C;AACA,SAASC,kBAAkBA,CAACjX,GAAG,EAAE;EAC7B,IAAI,IAAI+W,GAAG,CAAC/W,GAAG,EAAE,YAAY,CAAC,CAACkX,QAAQ,KAAK,GAAG,EAAE;IAC7C,MAAM,IAAIrf,aAAa,CAAC,IAAI,CAAC,sDAAsD,2EAA2E,GAC1J,6CAA6CmI,GAAG,wCAAwC,GACxF,6BAA6B,CAAC;EACtC;AACJ;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASsE,YAAY,EAAEuE,iBAAiB,EAAEsK,8BAA8B,EAAE9Z,WAAW,EAAEmJ,UAAU,EAAEyQ,qBAAqB,EAAEF,gBAAgB,EAAET,oBAAoB,EAAEnT,WAAW,EAAEF,gBAAgB,EAAE+C,iBAAiB,EAAEX,aAAa,EAAE8O,eAAe,EAAE/W,WAAW,EAAEwI,kBAAkB,EAAEtI,WAAW,EAAE6E,UAAU,EAAE4B,WAAW,EAAE+B,YAAY,EAAER,gBAAgB,EAAEe,cAAc,EAAEzF,oBAAoB,EAAEuQ,cAAc,EAAE8B,sBAAsB,EAAElE,kBAAkB,EAAE2B,gBAAgB,EAAE+D,iBAAiB,EAAE2B,SAAS,EAAEd,gBAAgB,EAAEI,sBAAsB,EAAEI,gBAAgB,EAAED,oBAAoB,EAAEG,yBAAyB,EAAEJ,qBAAqB,EAAE7I,yBAAyB,IAAIoO,0BAA0B,EAAEtN,sBAAsB,IAAIuN,wBAAwB,EAAEvN,sBAAsB,IAAIwN,uBAAuB,EAAErO,gCAAgC,IAAIsO,iCAAiC,EAAEtB,qBAAqB,IAAIuB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}