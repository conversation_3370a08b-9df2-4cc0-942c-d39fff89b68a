{"ast": null, "code": "import { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nclass FocusKeyManager extends ListKeyManager {\n  _origin = 'program';\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\nexport { FocusKeyManager as F };", "map": {"version": 3, "names": ["L", "ListKeyManager", "FocusKeyManager", "_origin", "setFocusOrigin", "origin", "setActiveItem", "item", "activeItem", "focus", "F"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/focus-key-manager-CPmlyB_c.mjs"], "sourcesContent": ["import { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\n\nclass FocusKeyManager extends ListKeyManager {\n    _origin = 'program';\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\nexport { FocusKeyManager as F };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AAErE,MAAMC,eAAe,SAASD,cAAc,CAAC;EACzCE,OAAO,GAAG,SAAS;EACnB;AACJ;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAACF,OAAO,GAAGE,MAAM;IACrB,OAAO,IAAI;EACf;EACAC,aAAaA,CAACC,IAAI,EAAE;IAChB,KAAK,CAACD,aAAa,CAACC,IAAI,CAAC;IACzB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACC,KAAK,CAAC,IAAI,CAACN,OAAO,CAAC;IACvC;EACJ;AACJ;AAEA,SAASD,eAAe,IAAIQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}