@use 'sass:map';
@use '../../token-utils';
@use '../../../style/sass-utils';
@use '../../../theming/inspection';
@use '../../../theming/theming';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, outlined-button);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    keep-touch-target: false,

    outline-width: 1px,
    container-shape: 4px,

    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    hover-state-layer-opacity: null,
    focus-state-layer-opacity: null,
    pressed-state-layer-opacity: null,

    focus-state-layer-color: null,
    hover-state-layer-color: null,
    pressed-state-layer-color: null,

    hover-label-text-color: null,
    focus-label-text-color: null,
    pressed-label-text-color: null,

    hover-outline-color: null,
    focus-outline-color: null,
    pressed-outline-color: null,

    focus-ring-color: null,
    focus-ring-offset: null,

    with-icon-icon-size: null,
    with-icon-icon-color: null,
    with-icon-hover-icon-color: null,
    with-icon-focus-icon-color: null,
    with-icon-pressed-icon-color: null,
    with-icon-disabled-icon-color: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $outline: if(token-utils.$private-is-internal-build,
    rgba(if($is-dark, #fff, #000), 0.12),
    inspection.get-theme-color($theme, foreground, divider)
  );

  @return (
    disabled-outline-color: $outline,
    disabled-label-text-color: inspection.get-theme-color($theme, foreground, disabled-button,
      if($is-dark, 0.5, 0.38)),
    label-text-color: inspection.get-theme-color($theme, foreground, text, 1),
    outline-color: $outline,
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  @return (
    label-text-color: inspection.get-theme-color($theme, $palette-name, default),

    // TODO: we shouldn't have to set this since it's the same as the non-palette version, however
    // there are a bunch of tests internally that depend on it. We should remove this and clean
    // up the screenshots separately.
    outline-color: map.get(get-color-tokens($theme), outline-color),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    label-text-font: inspection.get-theme-typography($theme, button, font-family),
    label-text-size: inspection.get-theme-typography($theme, button, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, button, letter-spacing),
    label-text-weight: inspection.get-theme-typography($theme, button, font-weight),
    label-text-transform: none,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    container-height:
      map.get(
        (
          0: 36px,
          -1: 32px,
          -2: 28px,
          -3: 24px,
        ),
        $scale
      )
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(token-utils.$placeholder-color-config),
    get-typography-tokens(token-utils.$placeholder-typography-config),
    get-density-tokens(token-utils.$placeholder-density-config)
  );
}
