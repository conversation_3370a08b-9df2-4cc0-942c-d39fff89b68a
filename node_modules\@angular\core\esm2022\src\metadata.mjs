/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * This indirection is needed to free up Component, etc symbols in the public API
 * to be used by the decorator versions of these annotations.
 */
export { Attribute } from './di/metadata_attr';
export { ContentChild, ContentChildren, Query, ViewChild, ViewChildren, } from './metadata/di';
export { Component, Directive, HostBinding, HostListener, Input, Output, Pipe, } from './metadata/directives';
export { NgModule } from './metadata/ng_module';
export { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from './metadata/schema';
export { ViewEncapsulation } from './metadata/view';
//# sourceMappingURL=data:application/json;base64,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