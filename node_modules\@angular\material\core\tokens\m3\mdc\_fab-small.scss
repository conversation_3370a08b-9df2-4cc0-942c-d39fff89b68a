@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, fab-small);

/// Generates the tokens for MDC fab-small
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC fab-small
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-utils.get-mdc-tokens('fab-primary-small', $systems, $exclude-hardcoded);
  $variant-tokens: (
    // Color variants
    primary: (), // Default, no overrides needed.
    secondary: token-utils.get-mdc-tokens('fab-secondary-small', $systems, $exclude-hardcoded),
    tertiary: token-utils.get-mdc-tokens('fab-tertiary-small', $systems, $exclude-hardcoded)
  );

  @return token-utils.namespace-tokens($prefix, ($mdc-tokens, $variant-tokens), $token-slots);
}
