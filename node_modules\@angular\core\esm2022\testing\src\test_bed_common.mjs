/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { InjectionToken, ɵDeferBlockBehavior as DeferBlockBehavior, } from '@angular/core';
/** Whether test modules should be torn down by default. */
export const TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;
/** Whether unknown elements in templates should throw by default. */
export const THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;
/** Whether unknown properties in templates should throw by default. */
export const THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;
/** Whether defer blocks should use manual triggering or play through normally. */
export const DEFER_BLOCK_DEFAULT_BEHAVIOR = DeferBlockBehavior.Playthrough;
/**
 * An abstract class for inserting the root test component element in a platform independent way.
 *
 * @publicApi
 */
export class TestComponentRenderer {
    insertRootElement(rootElementId) { }
    removeAllRootElements() { }
}
/**
 * @publicApi
 */
export const ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');
/**
 * @publicApi
 */
export const ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');
//# sourceMappingURL=data:application/json;base64,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