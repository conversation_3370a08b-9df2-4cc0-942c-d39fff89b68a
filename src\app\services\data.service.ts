import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface TestData {
  ID: number;
  TestID: string;
  BatchNo: string;
  CreatedOn: string;
  TestCode: string;
  TestName: string;
  Minimum: number;
  LSL: number;
  USL: number;
  SD: string;
  ED: string;
  SubTestName: string;
  PpL: number;
  PpU: number;
  CpL: number;
  CpU: number;
  Pp: number;
  PpK: number;
  Cp: number;
  CpK: number;
  UOM: string;
  CPV_FLAG: string;
}

export interface MatrixData {
  batchNumbers: string[];
  testNames: string[];
  subTestsByTest: { [testName: string]: string[] };
  dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } };
  unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } };
  ppPpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Pp: number | null, PpK: number | null } } };
  cpCpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Cp: number | null, CpK: number | null } } };
}

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor(private http: HttpClient) { }

  getTestData(): Observable<TestData[]> {
    console.log('Fetching data from /assets/samplejson.json');
    return this.http.get<TestData[]>('/assets/samplejson.json');
  }

  getMatrixData(): Observable<MatrixData> {
    return this.getTestData().pipe(
      map(data => {
        console.log('Raw data from JSON:', data);
        const transformedData = this.transformToMatrix(data);
        console.log('Transformed matrix data:', transformedData);
        return transformedData;
      })
    );
  }

  private transformToMatrix(data: TestData[]): MatrixData {
    const batchNumbers = [...new Set(data.map(item => item.BatchNo))].sort();
    const testNames = [...new Set(data.map(item => item.TestName))].sort();

    const subTestsByTest: { [testName: string]: string[] } = {};
    const dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } } = {};
    const unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } } = {};
    const ppPpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Pp: number | null, PpK: number | null } } } = {};
    const cpCpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Cp: number | null, CpK: number | null } } } = {};

    // Initialize structures
    testNames.forEach(testName => {
      subTestsByTest[testName] = [...new Set(data.filter(item => item.TestName === testName).map(item => item.SubTestName))].sort();
      unitsByTestAndSubTest[testName] = {};
      ppPpKByTestAndSubTest[testName] = {};
      cpCpKByTestAndSubTest[testName] = {};
    });

    batchNumbers.forEach(batchNo => {
      dataMatrix[batchNo] = {};
      testNames.forEach(testName => {
        dataMatrix[batchNo][testName] = {};
      });
    });

    // Populate data matrix, units, and statistical values
    data.forEach(item => {
      if (!dataMatrix[item.BatchNo][item.TestName][item.SubTestName]) {
        dataMatrix[item.BatchNo][item.TestName][item.SubTestName] = item;
        unitsByTestAndSubTest[item.TestName][item.SubTestName] = item.UOM;

        // Store Pp/PpK values (use first valid occurrence for each TestName/SubTestName combination)
        if (!ppPpKByTestAndSubTest[item.TestName][item.SubTestName]) {
          ppPpKByTestAndSubTest[item.TestName][item.SubTestName] = {
            Pp: item.Pp || null,
            PpK: item.PpK || null
          };
        }

        // Store Cp/CpK values (use first valid occurrence for each TestName/SubTestName combination)
        if (!cpCpKByTestAndSubTest[item.TestName][item.SubTestName]) {
          cpCpKByTestAndSubTest[item.TestName][item.SubTestName] = {
            Cp: item.Cp || null,
            CpK: item.CpK || null
          };
        }
      }
    });

    return {
      batchNumbers,
      testNames,
      subTestsByTest,
      dataMatrix,
      unitsByTestAndSubTest,
      ppPpKByTestAndSubTest,
      cpCpKByTestAndSubTest
    };
  }
}
