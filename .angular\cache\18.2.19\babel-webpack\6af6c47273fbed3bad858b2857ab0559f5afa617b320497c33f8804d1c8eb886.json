{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/data.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/progress-spinner\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/list\";\nfunction PulsedesignComponent_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Batch Numbers: \", ctx_r0.matrixData.batchNumbers == null ? null : ctx_r0.matrixData.batchNumbers.length, \"\");\n  }\n}\nfunction PulsedesignComponent_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Test Names: \", ctx_r0.matrixData.testNames == null ? null : ctx_r0.matrixData.testNames.length, \"\");\n  }\n}\nfunction PulsedesignComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, PulsedesignComponent_div_3_p_5_Template, 2, 1, \"p\", 7)(6, PulsedesignComponent_div_3_p_6_Template, 2, 1, \"p\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Loading: \", ctx_r0.loading, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Matrix Data Available: \", !!ctx_r0.matrixData, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.matrixData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.matrixData);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"colspan\", ctx_r0.matrixData.subTestsByTest[testName_r2].length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", testName_r2, \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", subTestName_r3, \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template, 2, 1, \"th\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r4]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r5 = ctx.$implicit;\n    const testName_r6 = i0.ɵɵnextContext().$implicit;\n    const batchNo_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, ctx_r0.getMinimumValue(batchNo_r7, testName_r6, subTestName_r5), \"1.2-3\"), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template, 3, 4, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r6]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const batchNo_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(batchNo_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r8 = ctx.$implicit;\n    const testName_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getUnit(testName_r9, subTestName_r8), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template, 2, 1, \"td\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r9]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r10 = ctx.$implicit;\n    const testName_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getPpPpKValue(testName_r11, subTestName_r10), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template, 2, 1, \"td\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r11 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r11]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r12 = ctx.$implicit;\n    const testName_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getCpCpKValue(testName_r13, subTestName_r12), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template, 2, 1, \"td\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r13 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r13]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_32_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 31)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_mat_card_4_ng_container_32_td_1_Template_button_click_1_listener($event) {\n      const subTestName_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const testName_r16 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openActionModal($event, testName_r16, subTestName_r15));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_32_td_1_Template, 4, 0, \"td\", 30);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r16 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r16]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 8)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Test Data Matrix\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 9)(6, \"table\", 10)(7, \"thead\")(8, \"tr\", 11)(9, \"th\", 12);\n    i0.ɵɵtext(10, \"BatchNo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, PulsedesignComponent_mat_card_4_ng_container_11_Template, 3, 2, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"tr\", 14);\n    i0.ɵɵtemplate(13, PulsedesignComponent_mat_card_4_ng_container_13_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, PulsedesignComponent_mat_card_4_tr_15_Template, 4, 2, \"tr\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"tfoot\")(17, \"tr\", 16)(18, \"td\", 17);\n    i0.ɵɵtext(19, \"Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, PulsedesignComponent_mat_card_4_ng_container_20_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"tr\", 18)(22, \"td\", 17);\n    i0.ɵɵtext(23, \"Pp | PpK\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, PulsedesignComponent_mat_card_4_ng_container_24_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tr\", 19)(26, \"td\", 17);\n    i0.ɵɵtext(27, \"Cp | CpK\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, PulsedesignComponent_mat_card_4_ng_container_28_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"tr\", 20)(30, \"td\", 17);\n    i0.ɵɵtext(31, \"Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, PulsedesignComponent_mat_card_4_ng_container_32_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.batchNumbers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n  }\n}\nfunction PulsedesignComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PulsedesignComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"p\");\n    i0.ɵɵtext(2, \"No data available or error loading data.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PulsedesignComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeActionModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 37)(3, \"h4\");\n    i0.ɵɵtext(4, \"Select Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.closeActionModal());\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-list\", 39)(9, \"mat-list-item\", 40);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_mat_list_item_click_9_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPCAClick(ctx_r0.selectedTestName, ctx_r0.selectedSubTestName));\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\", 41);\n    i0.ɵɵtext(11, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 42);\n    i0.ɵɵtext(13, \"PCA\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-list-item\", 40);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_mat_list_item_click_14_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onNelsonClick(ctx_r0.selectedTestName, ctx_r0.selectedSubTestName));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\", 41);\n    i0.ɵɵtext(16, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 42);\n    i0.ɵɵtext(18, \"Nelson\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-list-item\", 40);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_mat_list_item_click_19_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSQCClick(ctx_r0.selectedTestName, ctx_r0.selectedSubTestName));\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\", 41);\n    i0.ɵɵtext(21, \"assessment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 42);\n    i0.ɵɵtext(23, \"SQC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"mat-list-item\", 40);\n    i0.ɵɵlistener(\"click\", function PulsedesignComponent_div_7_Template_mat_list_item_click_24_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDownloadClick(ctx_r0.selectedTestName, ctx_r0.selectedSubTestName));\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\", 41);\n    i0.ɵɵtext(26, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 42);\n    i0.ɵɵtext(28, \"Download\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r0.modalPosition.top)(\"left\", ctx_r0.modalPosition.left);\n  }\n}\nexport class PulsedesignComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.matrixData = null;\n    this.loading = true;\n    this.displayedColumns = [];\n    this.showActionModal = false;\n    this.selectedTestName = '';\n    this.selectedSubTestName = '';\n    this.modalPosition = {\n      top: '0px',\n      left: '0px'\n    };\n  }\n  ngOnInit() {\n    console.log('Component initialized, loading data...');\n    this.dataService.getMatrixData().subscribe({\n      next: data => {\n        console.log('Data received:', data);\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n        console.log('Loading complete, matrixData:', this.matrixData);\n      },\n      error: error => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  setupDisplayedColumns() {\n    if (!this.matrixData) return;\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n  getMinimumValue(batchNo, testName, subTestName) {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n  getPpPpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const ppPpKData = this.matrixData.ppPpKByTestAndSubTest[testName]?.[subTestName];\n    if (ppPpKData && ppPpKData.Pp !== null && ppPpKData.PpK !== null) {\n      return `${ppPpKData.Pp.toFixed(3)} | ${ppPpKData.PpK.toFixed(3)}`;\n    }\n    return '';\n  }\n  getCpCpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const cpCpKData = this.matrixData.cpCpKByTestAndSubTest[testName]?.[subTestName];\n    if (cpCpKData && cpCpKData.Cp !== null && cpCpKData.CpK !== null) {\n      return `${cpCpKData.Cp.toFixed(3)} | ${cpCpKData.CpK.toFixed(3)}`;\n    }\n    return '';\n  }\n  getUnit(testName, subTestName) {\n    if (!this.matrixData) return '';\n    // First try the pre-computed units\n    const unit = this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName];\n    if (unit) return unit;\n    // Fallback: find any batch that has data for this testName and subTestName combination\n    for (const batchNo of this.matrixData.batchNumbers) {\n      const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n      if (data) {\n        return data.UOM;\n      }\n    }\n    return '';\n  }\n  // Action methods\n  onActionClick(action, testName, subTestName) {\n    const unit = this.getUnit(testName, subTestName);\n    const ppPpK = this.getPpPpKValue(testName, subTestName);\n    const cpCpK = this.getCpCpKValue(testName, subTestName);\n    const message = `Action: ${action}\nTestName: ${testName}\nSubTestName: ${subTestName}\nUnit: ${unit}\nPp | PpK: ${ppPpK || 'N/A'}\nCp | CpK: ${cpCpK || 'N/A'}`;\n    alert(message);\n  }\n  onPCAClick(testName, subTestName) {\n    this.onActionClick('PCA', testName, subTestName);\n    this.closeActionModal();\n  }\n  onNelsonClick(testName, subTestName) {\n    this.onActionClick('Nelson', testName, subTestName);\n    this.closeActionModal();\n  }\n  onSQCClick(testName, subTestName) {\n    this.onActionClick('SQC', testName, subTestName);\n    this.closeActionModal();\n  }\n  onDownloadClick(testName, subTestName) {\n    this.onActionClick('Download', testName, subTestName);\n    this.closeActionModal();\n  }\n  // Modal management methods\n  openActionModal(event, testName, subTestName) {\n    event.stopPropagation();\n    this.selectedTestName = testName;\n    this.selectedSubTestName = subTestName;\n    // Calculate modal position based on click position\n    const rect = event.target.getBoundingClientRect();\n    this.modalPosition = {\n      top: rect.bottom + window.scrollY + 5 + 'px',\n      left: rect.left + window.scrollX - 70 + 'px'\n    };\n    this.showActionModal = true;\n  }\n  closeActionModal() {\n    this.showActionModal = false;\n    this.selectedTestName = '';\n    this.selectedSubTestName = '';\n  }\n  static {\n    this.ɵfac = function PulsedesignComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PulsedesignComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PulsedesignComponent,\n      selectors: [[\"app-pulsedesign\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 5,\n      consts: [[1, \"pulse-container\"], [\"class\", \"debug-info\", 4, \"ngIf\"], [\"class\", \"matrix-card\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"modal-overlay\", 3, \"click\", 4, \"ngIf\"], [1, \"debug-info\"], [4, \"ngIf\"], [1, \"matrix-card\"], [1, \"table-container\"], [1, \"matrix-table\"], [1, \"test-name-header\"], [\"rowspan\", \"2\", 1, \"batch-header\"], [4, \"ngFor\", \"ngForOf\"], [1, \"sub-test-header\"], [\"class\", \"data-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-row\", \"unit-row\"], [1, \"footer-label\"], [1, \"footer-row\", \"pp-row\"], [1, \"footer-row\", \"cp-row\"], [1, \"footer-row\", \"actions-row\"], [1, \"test-name-cell\"], [\"class\", \"sub-test-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"sub-test-cell\"], [1, \"data-row\"], [1, \"batch-cell\"], [\"class\", \"value-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"value-cell\"], [\"class\", \"footer-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-cell\"], [\"class\", \"action-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-cell\"], [\"mat-icon-button\", \"\", 1, \"action-button\", 3, \"click\"], [1, \"loading-container\"], [1, \"error-container\"], [1, \"modal-overlay\", 3, \"click\"], [1, \"action-modal\", 3, \"click\"], [1, \"modal-header\"], [\"mat-icon-button\", \"\", 1, \"close-button\", 3, \"click\"], [1, \"action-list\"], [1, \"action-item\", 3, \"click\"], [\"matListItemIcon\", \"\"], [\"matListItemTitle\", \"\"]],\n      template: function PulsedesignComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Welcome to pulse latest design\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, PulsedesignComponent_div_3_Template, 7, 4, \"div\", 1)(4, PulsedesignComponent_mat_card_4_Template, 33, 7, \"mat-card\", 2)(5, PulsedesignComponent_div_5_Template, 4, 0, \"div\", 3)(6, PulsedesignComponent_div_6_Template, 3, 0, \"div\", 4)(7, PulsedesignComponent_div_7_Template, 29, 4, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.matrixData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.matrixData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showActionModal);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, MatTableModule, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatProgressSpinnerModule, i4.MatProgressSpinner, MatIconModule, i5.MatIcon, MatButtonModule, i6.MatIconButton, MatDialogModule, MatListModule, i7.MatList, i7.MatListItem, i7.MatListItemIcon, i7.MatListItemTitle],\n      styles: [\".pulse-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2.5rem;\\n  text-align: center;\\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\\n  margin: 0 0 30px 0;\\n}\\n\\n.matrix-card[_ngcontent-%COMP%] {\\n  margin: 20px auto;\\n  max-width: 95%;\\n  box-shadow: 0 8px 16px rgba(0,0,0,0.2);\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-width: 100%;\\n}\\n\\n.matrix-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-size: 12px;\\n}\\n\\n.matrix-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n.matrix-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 8px;\\n  text-align: center;\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.test-name-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #3f51b5;\\n  color: white;\\n  font-weight: bold;\\n  font-size: 14px;\\n}\\n\\n.sub-test-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #5c6bc0;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 12px;\\n}\\n\\n.batch-header[_ngcontent-%COMP%] {\\n  background-color: #3f51b5 !important;\\n  color: white !important;\\n  font-weight: bold !important;\\n  writing-mode: vertical-rl;\\n  text-orientation: mixed;\\n  min-width: 80px;\\n}\\n\\n\\n\\n.data-row[_ngcontent-%COMP%]:nth-child(even) {\\n  background-color: #f5f5f5;\\n}\\n\\n.data-row[_ngcontent-%COMP%]:nth-child(odd) {\\n  background-color: white;\\n}\\n\\n.batch-cell[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  font-weight: 600;\\n  color: #3f51b5;\\n}\\n\\n.value-cell[_ngcontent-%COMP%] {\\n  font-family: 'Courier New', monospace;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.footer-row[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  font-weight: 600;\\n}\\n\\n.footer-label[_ngcontent-%COMP%] {\\n  background-color: #9e9e9e;\\n  color: white;\\n  font-weight: bold;\\n  text-align: center;\\n}\\n\\n.unit-row[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n\\n.pp-row[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n}\\n\\n.cp-row[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n}\\n\\n.actions-row[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n}\\n\\n.footer-cell[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-family: 'Courier New', monospace;\\n}\\n\\n.action-cell[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 8px;\\n  position: relative;\\n}\\n\\n.action-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  transition: all 0.3s ease;\\n}\\n\\n.action-button[_ngcontent-%COMP%]:hover {\\n  color: #2e7d32;\\n  background-color: rgba(76, 175, 80, 0.1);\\n}\\n\\n\\n\\n.hover-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: white;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 1000;\\n  min-width: 140px;\\n  overflow: hidden;\\n  margin-top: 4px;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  font-size: 14px;\\n  color: #333;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(76, 175, 80, 0.1);\\n  color: #2e7d32;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  color: #666;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.debug-info[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  padding: 10px;\\n  margin: 10px 0;\\n  border-radius: 5px;\\n  color: #333;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 0, 0, 0.1);\\n  color: white;\\n  padding: 20px;\\n  text-align: center;\\n  border-radius: 5px;\\n  margin: 20px 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .matrix-table[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n\\n  .matrix-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .matrix-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 4px;\\n  }\\n\\n  h1[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTableModule", "MatCardModule", "MatProgressSpinnerModule", "MatIconModule", "MatButtonModule", "MatDialogModule", "MatListModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "matrixData", "batchNumbers", "length", "testNames", "ɵɵtemplate", "PulsedesignComponent_div_3_p_5_Template", "PulsedesignComponent_div_3_p_6_Template", "loading", "ɵɵproperty", "ɵɵelementContainerStart", "testName_r2", "subTestName_r3", "PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template", "subTestsByTest", "testName_r4", "ɵɵpipeBind2", "getMinimumValue", "batchNo_r7", "testName_r6", "subTestName_r5", "PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template", "PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template", "ɵɵtextInterpolate", "getUnit", "testName_r9", "subTestName_r8", "PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template", "getPpPpKValue", "testName_r11", "subTestName_r10", "PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template", "getCpCpKValue", "testName_r13", "subTestName_r12", "PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template", "ɵɵlistener", "PulsedesignComponent_mat_card_4_ng_container_32_td_1_Template_button_click_1_listener", "$event", "subTestName_r15", "ɵɵrestoreView", "_r14", "$implicit", "testName_r16", "ɵɵnextContext", "ɵɵresetView", "openActionModal", "PulsedesignComponent_mat_card_4_ng_container_32_td_1_Template", "PulsedesignComponent_mat_card_4_ng_container_11_Template", "PulsedesignComponent_mat_card_4_ng_container_13_Template", "PulsedesignComponent_mat_card_4_tr_15_Template", "PulsedesignComponent_mat_card_4_ng_container_20_Template", "PulsedesignComponent_mat_card_4_ng_container_24_Template", "PulsedesignComponent_mat_card_4_ng_container_28_Template", "PulsedesignComponent_mat_card_4_ng_container_32_Template", "ɵɵelement", "PulsedesignComponent_div_7_Template_div_click_0_listener", "_r17", "closeActionModal", "PulsedesignComponent_div_7_Template_div_click_1_listener", "stopPropagation", "PulsedesignComponent_div_7_Template_button_click_5_listener", "PulsedesignComponent_div_7_Template_mat_list_item_click_9_listener", "onPCAClick", "selectedTestName", "selectedSubTestName", "PulsedesignComponent_div_7_Template_mat_list_item_click_14_listener", "onNelsonClick", "PulsedesignComponent_div_7_Template_mat_list_item_click_19_listener", "onSQCClick", "PulsedesignComponent_div_7_Template_mat_list_item_click_24_listener", "onDownloadClick", "ɵɵstyleProp", "modalPosition", "top", "left", "PulsedesignComponent", "constructor", "dataService", "displayedColumns", "showActionModal", "ngOnInit", "console", "log", "getMatrixData", "subscribe", "next", "data", "setupDisplayedColumns", "error", "for<PERSON>ach", "testName", "subTestName", "push", "batchNo", "dataMatrix", "Minimum", "ppPpKData", "ppPpKByTestAndSubTest", "Pp", "PpK", "toFixed", "cpCpKData", "cpCpKByTestAndSubTest", "Cp", "CpK", "unit", "unitsByTestAndSubTest", "UOM", "onActionClick", "action", "ppPpK", "cpCpK", "message", "alert", "event", "rect", "target", "getBoundingClientRect", "bottom", "window", "scrollY", "scrollX", "ɵɵdirectiveInject", "i1", "DataService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PulsedesignComponent_Template", "rf", "ctx", "PulsedesignComponent_div_3_Template", "PulsedesignComponent_mat_card_4_Template", "PulsedesignComponent_div_5_Template", "PulsedesignComponent_div_6_Template", "PulsedesignComponent_div_7_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatProgressSpinner", "i5", "MatIcon", "i6", "MatIconButton", "i7", "MatList", "MatListItem", "MatListItemIcon", "MatListItemTitle", "styles"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.ts", "C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatListModule } from '@angular/material/list';\nimport { DataService, MatrixData } from '../services/data.service';\n\n@Component({\n  selector: 'app-pulsedesign',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTableModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    MatIconModule,\n    MatButtonModule,\n    MatDialogModule,\n    MatListModule\n  ],\n  templateUrl: './pulsedesign.component.html',\n  styleUrls: ['./pulsedesign.component.css']\n})\nexport class PulsedesignComponent implements OnInit {\n  matrixData: MatrixData | null = null;\n  loading = true;\n  displayedColumns: string[] = [];\n  showActionModal = false;\n  selectedTestName = '';\n  selectedSubTestName = '';\n  modalPosition = { top: '0px', left: '0px' };\n\n  constructor(private dataService: DataService) {}\n\n  ngOnInit() {\n    console.log('Component initialized, loading data...');\n    this.dataService.getMatrixData().subscribe({\n      next: (data) => {\n        console.log('Data received:', data);\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n        console.log('Loading complete, matrixData:', this.matrixData);\n      },\n      error: (error) => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private setupDisplayedColumns() {\n    if (!this.matrixData) return;\n\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData!.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n\n  getMinimumValue(batchNo: string, testName: string, subTestName: string): number | null {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n\n  getPpPpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n\n    const ppPpKData = this.matrixData.ppPpKByTestAndSubTest[testName]?.[subTestName];\n    if (ppPpKData && ppPpKData.Pp !== null && ppPpKData.PpK !== null) {\n      return `${ppPpKData.Pp.toFixed(3)} | ${ppPpKData.PpK.toFixed(3)}`;\n    }\n\n    return '';\n  }\n\n  getCpCpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n\n    const cpCpKData = this.matrixData.cpCpKByTestAndSubTest[testName]?.[subTestName];\n    if (cpCpKData && cpCpKData.Cp !== null && cpCpKData.CpK !== null) {\n      return `${cpCpKData.Cp.toFixed(3)} | ${cpCpKData.CpK.toFixed(3)}`;\n    }\n\n    return '';\n  }\n\n  getUnit(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n    // First try the pre-computed units\n    const unit = this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName];\n    if (unit) return unit;\n\n    // Fallback: find any batch that has data for this testName and subTestName combination\n    for (const batchNo of this.matrixData.batchNumbers) {\n      const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n      if (data) {\n        return data.UOM;\n      }\n    }\n    return '';\n  }\n\n  // Action methods\n  onActionClick(action: string, testName: string, subTestName: string): void {\n    const unit = this.getUnit(testName, subTestName);\n    const ppPpK = this.getPpPpKValue(testName, subTestName);\n    const cpCpK = this.getCpCpKValue(testName, subTestName);\n\n    const message = `Action: ${action}\nTestName: ${testName}\nSubTestName: ${subTestName}\nUnit: ${unit}\nPp | PpK: ${ppPpK || 'N/A'}\nCp | CpK: ${cpCpK || 'N/A'}`;\n\n    alert(message);\n  }\n\n  onPCAClick(testName: string, subTestName: string): void {\n    this.onActionClick('PCA', testName, subTestName);\n    this.closeActionModal();\n  }\n\n  onNelsonClick(testName: string, subTestName: string): void {\n    this.onActionClick('Nelson', testName, subTestName);\n    this.closeActionModal();\n  }\n\n  onSQCClick(testName: string, subTestName: string): void {\n    this.onActionClick('SQC', testName, subTestName);\n    this.closeActionModal();\n  }\n\n  onDownloadClick(testName: string, subTestName: string): void {\n    this.onActionClick('Download', testName, subTestName);\n    this.closeActionModal();\n  }\n\n  // Modal management methods\n  openActionModal(event: MouseEvent, testName: string, subTestName: string): void {\n    event.stopPropagation();\n    this.selectedTestName = testName;\n    this.selectedSubTestName = subTestName;\n\n    // Calculate modal position based on click position\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    this.modalPosition = {\n      top: (rect.bottom + window.scrollY + 5) + 'px',\n      left: (rect.left + window.scrollX - 70) + 'px'\n    };\n\n    this.showActionModal = true;\n  }\n\n  closeActionModal(): void {\n    this.showActionModal = false;\n    this.selectedTestName = '';\n    this.selectedSubTestName = '';\n  }\n}\n", "<div class=\"pulse-container\">\n  <h1>Welcome to pulse latest design</h1>\n\n  <!-- Debug Information -->\n  <div *ngIf=\"!loading\" class=\"debug-info\">\n    <p>Loading: {{ loading }}</p>\n    <p>Matrix Data Available: {{ !!matrixData }}</p>\n    <p *ngIf=\"matrixData\">Batch Numbers: {{ matrixData.batchNumbers?.length }}</p>\n    <p *ngIf=\"matrixData\">Test Names: {{ matrixData.testNames?.length }}</p>\n  </div>\n\n  <mat-card class=\"matrix-card\" *ngIf=\"!loading && matrixData\">\n    <mat-card-header>\n      <mat-card-title>Test Data Matrix</mat-card-title>\n    </mat-card-header>\n    <mat-card-content>\n      <div class=\"table-container\">\n        <!-- Simple table structure first -->\n        <table class=\"matrix-table\">\n          <!-- Header Row with TestName and SubTestName -->\n          <thead>\n            <!-- Main TestName Header Row -->\n            <tr class=\"test-name-header\">\n              <th class=\"batch-header\" rowspan=\"2\">BatchNo</th>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <th class=\"test-name-cell\" [attr.colspan]=\"matrixData.subTestsByTest[testName].length\">\n                  {{ testName }}\n                </th>\n              </ng-container>\n            </tr>\n            <!-- SubTestName Header Row -->\n            <tr class=\"sub-test-header\">\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <th class=\"sub-test-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ subTestName }}\n                </th>\n              </ng-container>\n            </tr>\n          </thead>\n\n          <!-- Data Rows -->\n          <tbody>\n            <tr *ngFor=\"let batchNo of matrixData.batchNumbers\" class=\"data-row\">\n              <td class=\"batch-cell\">{{ batchNo }}</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"value-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getMinimumValue(batchNo, testName, subTestName) | number:'1.2-3' }}\n                </td>\n              </ng-container>\n            </tr>\n          </tbody>\n\n          <!-- Footer Rows -->\n          <tfoot>\n            <!-- Unit Row -->\n            <tr class=\"footer-row unit-row\">\n              <td class=\"footer-label\">Unit</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getUnit(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n\n            <!-- Pp | PpK Row -->\n            <tr class=\"footer-row pp-row\">\n              <td class=\"footer-label\">Pp | PpK</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getPpPpKValue(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n\n            <!-- Cp | CpK Row -->\n            <tr class=\"footer-row cp-row\">\n              <td class=\"footer-label\">Cp | CpK</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getCpCpKValue(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n\n            <!-- Actions Row -->\n            <tr class=\"footer-row actions-row\">\n              <td class=\"footer-label\">Actions</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"action-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  <button mat-icon-button class=\"action-button\"\n                          (click)=\"openActionModal($event, testName, subTestName)\">\n                    <mat-icon>more_vert</mat-icon>\n                  </button>\n                </td>\n              </ng-container>\n            </tr>\n          </tfoot>\n        </table>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Loading data...</p>\n  </div>\n\n  <div *ngIf=\"!loading && !matrixData\" class=\"error-container\">\n    <p>No data available or error loading data.</p>\n  </div>\n\n  <!-- Modal Action Dropdown -->\n  <div *ngIf=\"showActionModal\" class=\"modal-overlay\" (click)=\"closeActionModal()\">\n    <div class=\"action-modal\"\n         [style.top]=\"modalPosition.top\"\n         [style.left]=\"modalPosition.left\"\n         (click)=\"$event.stopPropagation()\">\n      <div class=\"modal-header\">\n        <h4>Select Action</h4>\n        <button mat-icon-button (click)=\"closeActionModal()\" class=\"close-button\">\n          <mat-icon>close</mat-icon>\n        </button>\n      </div>\n      <mat-list class=\"action-list\">\n        <mat-list-item (click)=\"onPCAClick(selectedTestName, selectedSubTestName)\" class=\"action-item\">\n          <mat-icon matListItemIcon>analytics</mat-icon>\n          <span matListItemTitle>PCA</span>\n        </mat-list-item>\n        <mat-list-item (click)=\"onNelsonClick(selectedTestName, selectedSubTestName)\" class=\"action-item\">\n          <mat-icon matListItemIcon>trending_up</mat-icon>\n          <span matListItemTitle>Nelson</span>\n        </mat-list-item>\n        <mat-list-item (click)=\"onSQCClick(selectedTestName, selectedSubTestName)\" class=\"action-item\">\n          <mat-icon matListItemIcon>assessment</mat-icon>\n          <span matListItemTitle>SQC</span>\n        </mat-list-item>\n        <mat-list-item (click)=\"onDownloadClick(selectedTestName, selectedSubTestName)\" class=\"action-item\">\n          <mat-icon matListItemIcon>download</mat-icon>\n          <span matListItemTitle>Download</span>\n        </mat-list-item>\n      </mat-list>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;;;ICDlDC,EAAA,CAAAC,cAAA,QAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAxDH,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,UAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,UAAA,CAAAC,YAAA,CAAAC,MAAA,KAAoD;;;;;IAC1ET,EAAA,CAAAC,cAAA,QAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAlDH,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,iBAAAC,MAAA,CAAAC,UAAA,CAAAG,SAAA,kBAAAJ,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAAD,MAAA,KAA8C;;;;;IAHpET,EADF,CAAAC,cAAA,aAAyC,QACpC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EADA,CAAAW,UAAA,IAAAC,uCAAA,eAAsB,IAAAC,uCAAA,eACA;IACxBb,EAAA,CAAAG,YAAA,EAAM;;;;IAJDH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,cAAAC,MAAA,CAAAQ,OAAA,KAAsB;IACtBd,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,kBAAA,8BAAAC,MAAA,CAAAC,UAAA,KAAyC;IACxCP,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAC,UAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAC,UAAA,CAAgB;;;;;IAgBVP,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAC,cAAA,aAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAFsBH,EAAA,CAAAI,SAAA,EAA2D;;IACpFJ,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,WAAA,MACF;;;;;IAMAjB,EAAA,CAAAC,cAAA,aAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAa,cAAA,MACF;;;;;IAHFlB,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAQ,6DAAA,iBAA0F;;;;;;IAAxCnB,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAC,WAAA,EAAsC;;;;;IAYxFrB,EAAA,CAAAC,cAAA,aAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAsB,WAAA,OAAAhB,MAAA,CAAAiB,eAAA,CAAAC,UAAA,EAAAC,WAAA,EAAAC,cAAA,iBACF;;;;;IAHF1B,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAgB,kEAAA,iBAAuF;;;;;;IAAxC3B,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAK,WAAA,EAAsC;;;;;IAFvFzB,EADF,CAAAC,cAAA,aAAqE,aAC5C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAW,UAAA,IAAAiB,6DAAA,2BAA4D;IAK9D5B,EAAA,CAAAG,YAAA,EAAK;;;;;IANoBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAA6B,iBAAA,CAAAL,UAAA,CAAa;IACDxB,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;;;;;IAcxDV,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAwB,OAAA,CAAAC,WAAA,EAAAC,cAAA,OACF;;;;;IAHFhC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAsB,6DAAA,iBAAwF;;;;;;IAAxCjC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAW,WAAA,EAAsC;;;;;IAUtF/B,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4B,aAAA,CAAAC,YAAA,EAAAC,eAAA,OACF;;;;;IAHFpC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAA0B,6DAAA,iBAAwF;;;;;;IAAxCrC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAe,YAAA,EAAsC;;;;;IAUtFnC,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAgC,aAAA,CAAAC,YAAA,EAAAC,eAAA,OACF;;;;;IAHFxC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAA8B,6DAAA,iBAAwF;;;;;;IAAxCzC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAmB,YAAA,EAAsC;;;;;;IAWpFvC,EADF,CAAAC,cAAA,aAAwF,iBAErB;IAAzDD,EAAA,CAAA0C,UAAA,mBAAAC,sFAAAC,MAAA;MAAA,MAAAC,eAAA,GAAA7C,EAAA,CAAA8C,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAC,YAAA,GAAAjD,EAAA,CAAAkD,aAAA,GAAAF,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAA8C,eAAA,CAAAR,MAAA,EAAAK,YAAA,EAAAJ,eAAA,CAA8C;IAAA,EAAC;IAC9D7C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAEvBF,EAFuB,CAAAG,YAAA,EAAW,EACvB,EACN;;;;;IANPH,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAA0C,6DAAA,iBAAwF;;;;;;IAAxCrD,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAA6B,YAAA,EAAsC;;;;;IA3EhGjD,EAFJ,CAAAC,cAAA,kBAA6D,sBAC1C,qBACC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAClCF,EADkC,CAAAG,YAAA,EAAiB,EACjC;IASRH,EARV,CAAAC,cAAA,uBAAkB,aACa,gBAEC,YAEnB,aAEwB,aACU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAW,UAAA,KAAA2C,wDAAA,2BAA4D;IAK9DtD,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAW,UAAA,KAAA4C,wDAAA,2BAA4D;IAMhEvD,EADE,CAAAG,YAAA,EAAK,EACC;IAGRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAW,UAAA,KAAA6C,8CAAA,iBAAqE;IAQvExD,EAAA,CAAAG,YAAA,EAAQ;IAMJH,EAHJ,CAAAC,cAAA,aAAO,cAE2B,cACL;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAW,UAAA,KAAA8C,wDAAA,2BAA4D;IAK9DzD,EAAA,CAAAG,YAAA,EAAK;IAIHH,EADF,CAAAC,cAAA,cAA8B,cACH;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAW,UAAA,KAAA+C,wDAAA,2BAA4D;IAK9D1D,EAAA,CAAAG,YAAA,EAAK;IAIHH,EADF,CAAAC,cAAA,cAA8B,cACH;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAW,UAAA,KAAAgD,wDAAA,2BAA4D;IAK9D3D,EAAA,CAAAG,YAAA,EAAK;IAIHH,EADF,CAAAC,cAAA,cAAmC,cACR;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAW,UAAA,KAAAiD,wDAAA,2BAA4D;IAaxE5D,EALU,CAAAG,YAAA,EAAK,EACC,EACF,EACJ,EACW,EACV;;;;IA5EoCH,EAAA,CAAAI,SAAA,IAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAQvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUpCV,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAC,YAAA,CAA0B;IAebR,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;;;;;IAetEV,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAA6D,SAAA,kBAA2B;IAC3B7D,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACpBF,EADoB,CAAAG,YAAA,EAAI,EAClB;;;;;IAGJH,EADF,CAAAC,cAAA,cAA6D,QACxD;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC7CF,EAD6C,CAAAG,YAAA,EAAI,EAC3C;;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAgF;IAA7BD,EAAA,CAAA0C,UAAA,mBAAAoB,yDAAA;MAAA9D,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAA0D,gBAAA,EAAkB;IAAA,EAAC;IAC7EhE,EAAA,CAAAC,cAAA,cAGwC;IAAnCD,EAAA,CAAA0C,UAAA,mBAAAuB,yDAAArB,MAAA;MAAA5C,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,OAAA/D,EAAA,CAAAmD,WAAA,CAASP,MAAA,CAAAsB,eAAA,EAAwB;IAAA,EAAC;IAEnClE,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,iBAA0E;IAAlDD,EAAA,CAAA0C,UAAA,mBAAAyB,4DAAA;MAAAnE,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAA0D,gBAAA,EAAkB;IAAA,EAAC;IAClDhE,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAEnBF,EAFmB,CAAAG,YAAA,EAAW,EACnB,EACL;IAEJH,EADF,CAAAC,cAAA,mBAA8B,wBACmE;IAAhFD,EAAA,CAAA0C,UAAA,mBAAA0B,mEAAA;MAAApE,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAA+D,UAAA,CAAA/D,MAAA,CAAAgE,gBAAA,EAAAhE,MAAA,CAAAiE,mBAAA,CAAiD;IAAA,EAAC;IACxEvE,EAAA,CAAAC,cAAA,oBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACnB;IAChBH,EAAA,CAAAC,cAAA,yBAAkG;IAAnFD,EAAA,CAAA0C,UAAA,mBAAA8B,oEAAA;MAAAxE,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAAmE,aAAA,CAAAnE,MAAA,CAAAgE,gBAAA,EAAAhE,MAAA,CAAAiE,mBAAA,CAAoD;IAAA,EAAC;IAC3EvE,EAAA,CAAAC,cAAA,oBAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EACtB;IAChBH,EAAA,CAAAC,cAAA,yBAA+F;IAAhFD,EAAA,CAAA0C,UAAA,mBAAAgC,oEAAA;MAAA1E,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAAqE,UAAA,CAAArE,MAAA,CAAAgE,gBAAA,EAAAhE,MAAA,CAAAiE,mBAAA,CAAiD;IAAA,EAAC;IACxEvE,EAAA,CAAAC,cAAA,oBAA0B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACnB;IAChBH,EAAA,CAAAC,cAAA,yBAAoG;IAArFD,EAAA,CAAA0C,UAAA,mBAAAkC,oEAAA;MAAA5E,EAAA,CAAA8C,aAAA,CAAAiB,IAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAkD,aAAA;MAAA,OAAAlD,EAAA,CAAAmD,WAAA,CAAS7C,MAAA,CAAAuE,eAAA,CAAAvE,MAAA,CAAAgE,gBAAA,EAAAhE,MAAA,CAAAiE,mBAAA,CAAsD;IAAA,EAAC;IAC7EvE,EAAA,CAAAC,cAAA,oBAA0B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAIvCF,EAJuC,CAAAG,YAAA,EAAO,EACxB,EACP,EACP,EACF;;;;IA5BCH,EAAA,CAAAI,SAAA,EAA+B;IAC/BJ,EADA,CAAA8E,WAAA,QAAAxE,MAAA,CAAAyE,aAAA,CAAAC,GAAA,CAA+B,SAAA1E,MAAA,CAAAyE,aAAA,CAAAE,IAAA,CACE;;;ADxF1C,OAAM,MAAOC,oBAAoB;EAS/BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAR/B,KAAA7E,UAAU,GAAsB,IAAI;IACpC,KAAAO,OAAO,GAAG,IAAI;IACd,KAAAuE,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAhB,gBAAgB,GAAG,EAAE;IACrB,KAAAC,mBAAmB,GAAG,EAAE;IACxB,KAAAQ,aAAa,GAAG;MAAEC,GAAG,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAE;EAEI;EAE/CM,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,IAAI,CAACL,WAAW,CAACM,aAAa,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACbL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC;QACnC,IAAI,CAACtF,UAAU,GAAGsF,IAAI;QACtB,IAAI,CAACC,qBAAqB,EAAE;QAC5B,IAAI,CAAChF,OAAO,GAAG,KAAK;QACpB0E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAClF,UAAU,CAAC;MAC/D,CAAC;MACDwF,KAAK,EAAGA,KAAK,IAAI;QACfP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACjF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQgF,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACvF,UAAU,EAAE;IAEtB,IAAI,CAAC8E,gBAAgB,GAAG,CAAC,SAAS,CAAC;IACnC,IAAI,CAAC9E,UAAU,CAACG,SAAS,CAACsF,OAAO,CAACC,QAAQ,IAAG;MAC3C,IAAI,CAAC1F,UAAW,CAACa,cAAc,CAAC6E,QAAQ,CAAC,CAACD,OAAO,CAACE,WAAW,IAAG;QAC9D,IAAI,CAACb,gBAAgB,CAACc,IAAI,CAAC,GAAGF,QAAQ,IAAIC,WAAW,EAAE,CAAC;MAC1D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA3E,eAAeA,CAAC6E,OAAe,EAAEH,QAAgB,EAAEC,WAAmB;IACpE,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE,OAAO,IAAI;IACjC,MAAMsF,IAAI,GAAG,IAAI,CAACtF,UAAU,CAAC8F,UAAU,CAACD,OAAO,CAAC,GAAGH,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAC3E,OAAOL,IAAI,GAAGA,IAAI,CAACS,OAAO,GAAG,IAAI;EACnC;EAEApE,aAAaA,CAAC+D,QAAgB,EAAEC,WAAmB;IACjD,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE,OAAO,EAAE;IAE/B,MAAMgG,SAAS,GAAG,IAAI,CAAChG,UAAU,CAACiG,qBAAqB,CAACP,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAChF,IAAIK,SAAS,IAAIA,SAAS,CAACE,EAAE,KAAK,IAAI,IAAIF,SAAS,CAACG,GAAG,KAAK,IAAI,EAAE;MAChE,OAAO,GAAGH,SAAS,CAACE,EAAE,CAACE,OAAO,CAAC,CAAC,CAAC,MAAMJ,SAAS,CAACG,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;IACnE;IAEA,OAAO,EAAE;EACX;EAEArE,aAAaA,CAAC2D,QAAgB,EAAEC,WAAmB;IACjD,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE,OAAO,EAAE;IAE/B,MAAMqG,SAAS,GAAG,IAAI,CAACrG,UAAU,CAACsG,qBAAqB,CAACZ,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAChF,IAAIU,SAAS,IAAIA,SAAS,CAACE,EAAE,KAAK,IAAI,IAAIF,SAAS,CAACG,GAAG,KAAK,IAAI,EAAE;MAChE,OAAO,GAAGH,SAAS,CAACE,EAAE,CAACH,OAAO,CAAC,CAAC,CAAC,MAAMC,SAAS,CAACG,GAAG,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAE;IACnE;IAEA,OAAO,EAAE;EACX;EAEA7E,OAAOA,CAACmE,QAAgB,EAAEC,WAAmB;IAC3C,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE,OAAO,EAAE;IAC/B;IACA,MAAMyG,IAAI,GAAG,IAAI,CAACzG,UAAU,CAAC0G,qBAAqB,CAAChB,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAC3E,IAAIc,IAAI,EAAE,OAAOA,IAAI;IAErB;IACA,KAAK,MAAMZ,OAAO,IAAI,IAAI,CAAC7F,UAAU,CAACC,YAAY,EAAE;MAClD,MAAMqF,IAAI,GAAG,IAAI,CAACtF,UAAU,CAAC8F,UAAU,CAACD,OAAO,CAAC,GAAGH,QAAQ,CAAC,GAAGC,WAAW,CAAC;MAC3E,IAAIL,IAAI,EAAE;QACR,OAAOA,IAAI,CAACqB,GAAG;MACjB;IACF;IACA,OAAO,EAAE;EACX;EAEA;EACAC,aAAaA,CAACC,MAAc,EAAEnB,QAAgB,EAAEC,WAAmB;IACjE,MAAMc,IAAI,GAAG,IAAI,CAAClF,OAAO,CAACmE,QAAQ,EAAEC,WAAW,CAAC;IAChD,MAAMmB,KAAK,GAAG,IAAI,CAACnF,aAAa,CAAC+D,QAAQ,EAAEC,WAAW,CAAC;IACvD,MAAMoB,KAAK,GAAG,IAAI,CAAChF,aAAa,CAAC2D,QAAQ,EAAEC,WAAW,CAAC;IAEvD,MAAMqB,OAAO,GAAG,WAAWH,MAAM;YACzBnB,QAAQ;eACLC,WAAW;QAClBc,IAAI;YACAK,KAAK,IAAI,KAAK;YACdC,KAAK,IAAI,KAAK,EAAE;IAExBE,KAAK,CAACD,OAAO,CAAC;EAChB;EAEAlD,UAAUA,CAAC4B,QAAgB,EAAEC,WAAmB;IAC9C,IAAI,CAACiB,aAAa,CAAC,KAAK,EAAElB,QAAQ,EAAEC,WAAW,CAAC;IAChD,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEAS,aAAaA,CAACwB,QAAgB,EAAEC,WAAmB;IACjD,IAAI,CAACiB,aAAa,CAAC,QAAQ,EAAElB,QAAQ,EAAEC,WAAW,CAAC;IACnD,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEAW,UAAUA,CAACsB,QAAgB,EAAEC,WAAmB;IAC9C,IAAI,CAACiB,aAAa,CAAC,KAAK,EAAElB,QAAQ,EAAEC,WAAW,CAAC;IAChD,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEAa,eAAeA,CAACoB,QAAgB,EAAEC,WAAmB;IACnD,IAAI,CAACiB,aAAa,CAAC,UAAU,EAAElB,QAAQ,EAAEC,WAAW,CAAC;IACrD,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEA;EACAZ,eAAeA,CAACqE,KAAiB,EAAExB,QAAgB,EAAEC,WAAmB;IACtEuB,KAAK,CAACvD,eAAe,EAAE;IACvB,IAAI,CAACI,gBAAgB,GAAG2B,QAAQ;IAChC,IAAI,CAAC1B,mBAAmB,GAAG2B,WAAW;IAEtC;IACA,MAAMwB,IAAI,GAAID,KAAK,CAACE,MAAsB,CAACC,qBAAqB,EAAE;IAClE,IAAI,CAAC7C,aAAa,GAAG;MACnBC,GAAG,EAAG0C,IAAI,CAACG,MAAM,GAAGC,MAAM,CAACC,OAAO,GAAG,CAAC,GAAI,IAAI;MAC9C9C,IAAI,EAAGyC,IAAI,CAACzC,IAAI,GAAG6C,MAAM,CAACE,OAAO,GAAG,EAAE,GAAI;KAC3C;IAED,IAAI,CAAC1C,eAAe,GAAG,IAAI;EAC7B;EAEAtB,gBAAgBA,CAAA;IACd,IAAI,CAACsB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAChB,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;EAC/B;;;uCA3IWW,oBAAoB,EAAAlF,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBjD,oBAAoB;MAAAkD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtI,EAAA,CAAAuI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1B/B7I,EADF,CAAAC,cAAA,aAA6B,SACvB;UAAAD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UA+GvCH,EA5GA,CAAAW,UAAA,IAAAoI,mCAAA,iBAAyC,IAAAC,wCAAA,uBAOoB,IAAAC,mCAAA,iBA2Fd,IAAAC,mCAAA,iBAKc,IAAAC,mCAAA,kBAKmB;UA+BlFnJ,EAAA,CAAAG,YAAA,EAAM;;;UA3IEH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAe,UAAA,UAAA+H,GAAA,CAAAhI,OAAA,CAAc;UAOWd,EAAA,CAAAI,SAAA,EAA4B;UAA5BJ,EAAA,CAAAe,UAAA,UAAA+H,GAAA,CAAAhI,OAAA,IAAAgI,GAAA,CAAAvI,UAAA,CAA4B;UA2FrDP,EAAA,CAAAI,SAAA,EAAa;UAAbJ,EAAA,CAAAe,UAAA,SAAA+H,GAAA,CAAAhI,OAAA,CAAa;UAKbd,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAe,UAAA,UAAA+H,GAAA,CAAAhI,OAAA,KAAAgI,GAAA,CAAAvI,UAAA,CAA6B;UAK7BP,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAe,UAAA,SAAA+H,GAAA,CAAAxD,eAAA,CAAqB;;;qBDjGzB9F,YAAY,EAAA4J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZ9J,cAAc,EACdC,aAAa,EAAA8J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACbjK,wBAAwB,EAAAkK,EAAA,CAAAC,kBAAA,EACxBlK,aAAa,EAAAmK,EAAA,CAAAC,OAAA,EACbnK,eAAe,EAAAoK,EAAA,CAAAC,aAAA,EACfpK,eAAe,EACfC,aAAa,EAAAoK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}