/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export * from './module';
export { MatPaginatedTabHeader } from './paginated-tab-header';
export { MatTabBodyPortal, MatTabBody, } from './tab-body';
export { MAT_TABS_CONFIG } from './tab-config';
export { MatTabContent, MAT_TAB_CONTENT } from './tab-content';
export { MatTabLabel, MAT_TAB, MAT_TAB_LABEL } from './tab-label';
export { MatTab, MAT_TAB_GROUP } from './tab';
export { MatInkBar, _MAT_INK_BAR_POSITIONER_FACTORY, _MAT_INK_BAR_POSITIONER, } from './ink-bar';
export { MatTabHeader } from './tab-header';
export { MatTabGroup, MatTabChangeEvent, } from './tab-group';
export { MatTab<PERSON><PERSON>, MatTabNavPanel, MatTabLink } from './tab-nav-bar/tab-nav-bar';
export { matTabsAnimations } from './tabs-animations';
export { MatTabLabelWrapper } from './tab-label-wrapper';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHVibGljLWFwaS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3NyYy9tYXRlcmlhbC90YWJzL3B1YmxpYy1hcGkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsY0FBYyxVQUFVLENBQUM7QUFDekIsT0FBTyxFQUFrQixxQkFBcUIsRUFBQyxNQUFNLHdCQUF3QixDQUFDO0FBQzlFLE9BQU8sRUFDTCxnQkFBZ0IsRUFDaEIsVUFBVSxHQUdYLE1BQU0sWUFBWSxDQUFDO0FBQ3BCLE9BQU8sRUFBZ0IsZUFBZSxFQUFDLE1BQU0sY0FBYyxDQUFDO0FBQzVELE9BQU8sRUFBQyxhQUFhLEVBQUUsZUFBZSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQzdELE9BQU8sRUFBQyxXQUFXLEVBQUUsT0FBTyxFQUFFLGFBQWEsRUFBQyxNQUFNLGFBQWEsQ0FBQztBQUNoRSxPQUFPLEVBQUMsTUFBTSxFQUFFLGFBQWEsRUFBQyxNQUFNLE9BQU8sQ0FBQztBQUM1QyxPQUFPLEVBQ0wsU0FBUyxFQUVULCtCQUErQixFQUMvQix1QkFBdUIsR0FDeEIsTUFBTSxXQUFXLENBQUM7QUFDbkIsT0FBTyxFQUFDLFlBQVksRUFBQyxNQUFNLGNBQWMsQ0FBQztBQUMxQyxPQUFPLEVBQ0wsV0FBVyxFQUNYLGlCQUFpQixHQUdsQixNQUFNLGFBQWEsQ0FBQztBQUNyQixPQUFPLEVBQUMsU0FBUyxFQUFFLGNBQWMsRUFBRSxVQUFVLEVBQUMsTUFBTSwyQkFBMkIsQ0FBQztBQUNoRixPQUFPLEVBQUMsaUJBQWlCLEVBQUMsTUFBTSxtQkFBbUIsQ0FBQztBQUNwRCxPQUFPLEVBQUMsa0JBQWtCLEVBQUMsTUFBTSxxQkFBcUIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5leHBvcnQgKiBmcm9tICcuL21vZHVsZSc7XG5leHBvcnQge1Njcm9sbERpcmVjdGlvbiwgTWF0UGFnaW5hdGVkVGFiSGVhZGVyfSBmcm9tICcuL3BhZ2luYXRlZC10YWItaGVhZGVyJztcbmV4cG9ydCB7XG4gIE1hdFRhYkJvZHlQb3J0YWwsXG4gIE1hdFRhYkJvZHksXG4gIE1hdFRhYkJvZHlQb3NpdGlvblN0YXRlLFxuICBNYXRUYWJCb2R5T3JpZ2luU3RhdGUsXG59IGZyb20gJy4vdGFiLWJvZHknO1xuZXhwb3J0IHtNYXRUYWJzQ29uZmlnLCBNQVRfVEFCU19DT05GSUd9IGZyb20gJy4vdGFiLWNvbmZpZyc7XG5leHBvcnQge01hdFRhYkNvbnRlbnQsIE1BVF9UQUJfQ09OVEVOVH0gZnJvbSAnLi90YWItY29udGVudCc7XG5leHBvcnQge01hdFRhYkxhYmVsLCBNQVRfVEFCLCBNQVRfVEFCX0xBQkVMfSBmcm9tICcuL3RhYi1sYWJlbCc7XG5leHBvcnQge01hdFRhYiwgTUFUX1RBQl9HUk9VUH0gZnJvbSAnLi90YWInO1xuZXhwb3J0IHtcbiAgTWF0SW5rQmFyLFxuICBfTWF0SW5rQmFyUG9zaXRpb25lcixcbiAgX01BVF9JTktfQkFSX1BPU0lUSU9ORVJfRkFDVE9SWSxcbiAgX01BVF9JTktfQkFSX1BPU0lUSU9ORVIsXG59IGZyb20gJy4vaW5rLWJhcic7XG5leHBvcnQge01hdFRhYkhlYWRlcn0gZnJvbSAnLi90YWItaGVhZGVyJztcbmV4cG9ydCB7XG4gIE1hdFRhYkdyb3VwLFxuICBNYXRUYWJDaGFuZ2VFdmVudCxcbiAgTWF0VGFiR3JvdXBCYXNlSGVhZGVyLFxuICBNYXRUYWJIZWFkZXJQb3NpdGlvbixcbn0gZnJvbSAnLi90YWItZ3JvdXAnO1xuZXhwb3J0IHtNYXRUYWJOYXYsIE1hdFRhYk5hdlBhbmVsLCBNYXRUYWJMaW5rfSBmcm9tICcuL3RhYi1uYXYtYmFyL3RhYi1uYXYtYmFyJztcbmV4cG9ydCB7bWF0VGFic0FuaW1hdGlvbnN9IGZyb20gJy4vdGFicy1hbmltYXRpb25zJztcbmV4cG9ydCB7TWF0VGFiTGFiZWxXcmFwcGVyfSBmcm9tICcuL3RhYi1sYWJlbC13cmFwcGVyJztcbiJdfQ==