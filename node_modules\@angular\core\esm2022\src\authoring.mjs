/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { ɵINPUT_SIGNAL_BRAND_WRITE_TYPE, } from './authoring/input/input_signal';
export { output } from './authoring/output/output';
export { getOutputDestroyRef as ɵgetOutputDestroyRef, OutputEmitterRef, } from './authoring/output/output_emitter_ref';
//# sourceMappingURL=data:application/json;base64,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