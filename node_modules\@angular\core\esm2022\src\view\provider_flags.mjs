/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// This default value is when checking the hierarchy for a token.
//
// It means both:
// - the token is not provided by the current injector,
// - only the element injectors should be checked (ie do not check module injectors
//
//          mod1
//         /
//       el1   mod2
//         \  /
//         el2
//
// When requesting el2.injector.get(token), we should check in the following order and return the
// first found value:
// - el2.injector.get(token, default)
// - el1.injector.get(token, NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR) -> do not check the module
// - mod2.injector.get(token, default)
export const NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR = {};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJvdmlkZXJfZmxhZ3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy92aWV3L3Byb3ZpZGVyX2ZsYWdzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILGlFQUFpRTtBQUNqRSxFQUFFO0FBQ0YsaUJBQWlCO0FBQ2pCLHVEQUF1RDtBQUN2RCxtRkFBbUY7QUFDbkYsRUFBRTtBQUNGLGdCQUFnQjtBQUNoQixZQUFZO0FBQ1osbUJBQW1CO0FBQ25CLGVBQWU7QUFDZixjQUFjO0FBQ2QsRUFBRTtBQUNGLGlHQUFpRztBQUNqRyxxQkFBcUI7QUFDckIscUNBQXFDO0FBQ3JDLDhGQUE4RjtBQUM5RixzQ0FBc0M7QUFDdEMsTUFBTSxDQUFDLE1BQU0scUNBQXFDLEdBQUcsRUFBRSxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuZGV2L2xpY2Vuc2VcbiAqL1xuXG4vLyBUaGlzIGRlZmF1bHQgdmFsdWUgaXMgd2hlbiBjaGVja2luZyB0aGUgaGllcmFyY2h5IGZvciBhIHRva2VuLlxuLy9cbi8vIEl0IG1lYW5zIGJvdGg6XG4vLyAtIHRoZSB0b2tlbiBpcyBub3QgcHJvdmlkZWQgYnkgdGhlIGN1cnJlbnQgaW5qZWN0b3IsXG4vLyAtIG9ubHkgdGhlIGVsZW1lbnQgaW5qZWN0b3JzIHNob3VsZCBiZSBjaGVja2VkIChpZSBkbyBub3QgY2hlY2sgbW9kdWxlIGluamVjdG9yc1xuLy9cbi8vICAgICAgICAgIG1vZDFcbi8vICAgICAgICAgL1xuLy8gICAgICAgZWwxICAgbW9kMlxuLy8gICAgICAgICBcXCAgL1xuLy8gICAgICAgICBlbDJcbi8vXG4vLyBXaGVuIHJlcXVlc3RpbmcgZWwyLmluamVjdG9yLmdldCh0b2tlbiksIHdlIHNob3VsZCBjaGVjayBpbiB0aGUgZm9sbG93aW5nIG9yZGVyIGFuZCByZXR1cm4gdGhlXG4vLyBmaXJzdCBmb3VuZCB2YWx1ZTpcbi8vIC0gZWwyLmluamVjdG9yLmdldCh0b2tlbiwgZGVmYXVsdClcbi8vIC0gZWwxLmluamVjdG9yLmdldCh0b2tlbiwgTk9UX0ZPVU5EX0NIRUNLX09OTFlfRUxFTUVOVF9JTkpFQ1RPUikgLT4gZG8gbm90IGNoZWNrIHRoZSBtb2R1bGVcbi8vIC0gbW9kMi5pbmplY3Rvci5nZXQodG9rZW4sIGRlZmF1bHQpXG5leHBvcnQgY29uc3QgTk9UX0ZPVU5EX0NIRUNLX09OTFlfRUxFTUVOVF9JTkpFQ1RPUiA9IHt9O1xuIl19