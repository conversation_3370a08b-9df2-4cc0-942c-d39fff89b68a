/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import * as o from '../output/output_ast';
import { Identifiers as R3 } from './r3_identifiers';
import { jitOnlyGuardedExpression, refsToArray } from './util';
import { DefinitionMap } from './view/util';
/**
 * How the selector scope of an NgModule (its declarations, imports, and exports) should be emitted
 * as a part of the NgModule definition.
 */
export var R3SelectorScopeMode;
(function (R3SelectorScopeMode) {
    /**
     * Emit the declarations inline into the module definition.
     *
     * This option is useful in certain contexts where it's known that JIT support is required. The
     * tradeoff here is that this emit style prevents directives and pipes from being tree-shaken if
     * they are unused, but the NgModule is used.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["Inline"] = 0] = "Inline";
    /**
     * Emit the declarations using a side effectful function call, `ɵɵsetNgModuleScope`, that is
     * guarded with the `ngJitMode` flag.
     *
     * This form of emit supports JIT and can be optimized away if the `ngJitMode` flag is set to
     * false, which allows unused directives and pipes to be tree-shaken.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["SideEffect"] = 1] = "SideEffect";
    /**
     * Don't generate selector scopes at all.
     *
     * This is useful for contexts where JIT support is known to be unnecessary.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["Omit"] = 2] = "Omit";
})(R3SelectorScopeMode || (R3SelectorScopeMode = {}));
/**
 * The type of the NgModule meta data.
 * - Global: Used for full and partial compilation modes which mainly includes R3References.
 * - Local: Used for the local compilation mode which mainly includes the raw expressions as appears
 * in the NgModule decorator.
 */
export var R3NgModuleMetadataKind;
(function (R3NgModuleMetadataKind) {
    R3NgModuleMetadataKind[R3NgModuleMetadataKind["Global"] = 0] = "Global";
    R3NgModuleMetadataKind[R3NgModuleMetadataKind["Local"] = 1] = "Local";
})(R3NgModuleMetadataKind || (R3NgModuleMetadataKind = {}));
/**
 * Construct an `R3NgModuleDef` for the given `R3NgModuleMetadata`.
 */
export function compileNgModule(meta) {
    const statements = [];
    const definitionMap = new DefinitionMap();
    definitionMap.set('type', meta.type.value);
    // Assign bootstrap definition. In local compilation mode (i.e., for
    // `R3NgModuleMetadataKind.LOCAL`) we assign the bootstrap field using the runtime
    // `ɵɵsetNgModuleScope`.
    if (meta.kind === R3NgModuleMetadataKind.Global && meta.bootstrap.length > 0) {
        definitionMap.set('bootstrap', refsToArray(meta.bootstrap, meta.containsForwardDecls));
    }
    if (meta.selectorScopeMode === R3SelectorScopeMode.Inline) {
        // If requested to emit scope information inline, pass the `declarations`, `imports` and
        // `exports` to the `ɵɵdefineNgModule()` call directly.
        if (meta.declarations.length > 0) {
            definitionMap.set('declarations', refsToArray(meta.declarations, meta.containsForwardDecls));
        }
        if (meta.imports.length > 0) {
            definitionMap.set('imports', refsToArray(meta.imports, meta.containsForwardDecls));
        }
        if (meta.exports.length > 0) {
            definitionMap.set('exports', refsToArray(meta.exports, meta.containsForwardDecls));
        }
    }
    else if (meta.selectorScopeMode === R3SelectorScopeMode.SideEffect) {
        // In this mode, scope information is not passed into `ɵɵdefineNgModule` as it
        // would prevent tree-shaking of the declarations, imports and exports references. Instead, it's
        // patched onto the NgModule definition with a `ɵɵsetNgModuleScope` call that's guarded by the
        // `ngJitMode` flag.
        const setNgModuleScopeCall = generateSetNgModuleScopeCall(meta);
        if (setNgModuleScopeCall !== null) {
            statements.push(setNgModuleScopeCall);
        }
    }
    else {
        // Selector scope emit was not requested, so skip it.
    }
    if (meta.schemas !== null && meta.schemas.length > 0) {
        definitionMap.set('schemas', o.literalArr(meta.schemas.map((ref) => ref.value)));
    }
    if (meta.id !== null) {
        definitionMap.set('id', meta.id);
        // Generate a side-effectful call to register this NgModule by its id, as per the semantics of
        // NgModule ids.
        statements.push(o.importExpr(R3.registerNgModuleType).callFn([meta.type.value, meta.id]).toStmt());
    }
    const expression = o
        .importExpr(R3.defineNgModule)
        .callFn([definitionMap.toLiteralMap()], undefined, true);
    const type = createNgModuleType(meta);
    return { expression, type, statements };
}
/**
 * This function is used in JIT mode to generate the call to `ɵɵdefineNgModule()` from a call to
 * `ɵɵngDeclareNgModule()`.
 */
export function compileNgModuleDeclarationExpression(meta) {
    const definitionMap = new DefinitionMap();
    definitionMap.set('type', new o.WrappedNodeExpr(meta.type));
    if (meta.bootstrap !== undefined) {
        definitionMap.set('bootstrap', new o.WrappedNodeExpr(meta.bootstrap));
    }
    if (meta.declarations !== undefined) {
        definitionMap.set('declarations', new o.WrappedNodeExpr(meta.declarations));
    }
    if (meta.imports !== undefined) {
        definitionMap.set('imports', new o.WrappedNodeExpr(meta.imports));
    }
    if (meta.exports !== undefined) {
        definitionMap.set('exports', new o.WrappedNodeExpr(meta.exports));
    }
    if (meta.schemas !== undefined) {
        definitionMap.set('schemas', new o.WrappedNodeExpr(meta.schemas));
    }
    if (meta.id !== undefined) {
        definitionMap.set('id', new o.WrappedNodeExpr(meta.id));
    }
    return o.importExpr(R3.defineNgModule).callFn([definitionMap.toLiteralMap()]);
}
export function createNgModuleType(meta) {
    if (meta.kind === R3NgModuleMetadataKind.Local) {
        return new o.ExpressionType(meta.type.value);
    }
    const { type: moduleType, declarations, exports, imports, includeImportTypes, publicDeclarationTypes, } = meta;
    return new o.ExpressionType(o.importExpr(R3.NgModuleDeclaration, [
        new o.ExpressionType(moduleType.type),
        publicDeclarationTypes === null
            ? tupleTypeOf(declarations)
            : tupleOfTypes(publicDeclarationTypes),
        includeImportTypes ? tupleTypeOf(imports) : o.NONE_TYPE,
        tupleTypeOf(exports),
    ]));
}
/**
 * Generates a function call to `ɵɵsetNgModuleScope` with all necessary information so that the
 * transitive module scope can be computed during runtime in JIT mode. This call is marked pure
 * such that the references to declarations, imports and exports may be elided causing these
 * symbols to become tree-shakeable.
 */
function generateSetNgModuleScopeCall(meta) {
    const scopeMap = new DefinitionMap();
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.declarations.length > 0) {
            scopeMap.set('declarations', refsToArray(meta.declarations, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.declarationsExpression) {
            scopeMap.set('declarations', meta.declarationsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.imports.length > 0) {
            scopeMap.set('imports', refsToArray(meta.imports, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.importsExpression) {
            scopeMap.set('imports', meta.importsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.exports.length > 0) {
            scopeMap.set('exports', refsToArray(meta.exports, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.exportsExpression) {
            scopeMap.set('exports', meta.exportsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Local && meta.bootstrapExpression) {
        scopeMap.set('bootstrap', meta.bootstrapExpression);
    }
    if (Object.keys(scopeMap.values).length === 0) {
        return null;
    }
    // setNgModuleScope(...)
    const fnCall = new o.InvokeFunctionExpr(
    /* fn */ o.importExpr(R3.setNgModuleScope), 
    /* args */ [meta.type.value, scopeMap.toLiteralMap()]);
    // (ngJitMode guard) && setNgModuleScope(...)
    const guardedCall = jitOnlyGuardedExpression(fnCall);
    // function() { (ngJitMode guard) && setNgModuleScope(...); }
    const iife = new o.FunctionExpr(/* params */ [], /* statements */ [guardedCall.toStmt()]);
    // (function() { (ngJitMode guard) && setNgModuleScope(...); })()
    const iifeCall = new o.InvokeFunctionExpr(/* fn */ iife, /* args */ []);
    return iifeCall.toStmt();
}
function tupleTypeOf(exp) {
    const types = exp.map((ref) => o.typeofExpr(ref.type));
    return exp.length > 0 ? o.expressionType(o.literalArr(types)) : o.NONE_TYPE;
}
function tupleOfTypes(types) {
    const typeofTypes = types.map((type) => o.typeofExpr(type));
    return types.length > 0 ? o.expressionType(o.literalArr(typeofTypes)) : o.NONE_TYPE;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicjNfbW9kdWxlX2NvbXBpbGVyLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tcGlsZXIvc3JjL3JlbmRlcjMvcjNfbW9kdWxlX2NvbXBpbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUdILE9BQU8sS0FBSyxDQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFFMUMsT0FBTyxFQUFDLFdBQVcsSUFBSSxFQUFFLEVBQUMsTUFBTSxrQkFBa0IsQ0FBQztBQUNuRCxPQUFPLEVBQUMsd0JBQXdCLEVBQXFDLFdBQVcsRUFBQyxNQUFNLFFBQVEsQ0FBQztBQUNoRyxPQUFPLEVBQUMsYUFBYSxFQUFDLE1BQU0sYUFBYSxDQUFDO0FBRTFDOzs7R0FHRztBQUNILE1BQU0sQ0FBTixJQUFZLG1CQXlCWDtBQXpCRCxXQUFZLG1CQUFtQjtJQUM3Qjs7Ozs7O09BTUc7SUFDSCxpRUFBTSxDQUFBO0lBRU47Ozs7OztPQU1HO0lBQ0gseUVBQVUsQ0FBQTtJQUVWOzs7O09BSUc7SUFDSCw2REFBSSxDQUFBO0FBQ04sQ0FBQyxFQXpCVyxtQkFBbUIsS0FBbkIsbUJBQW1CLFFBeUI5QjtBQUVEOzs7OztHQUtHO0FBQ0gsTUFBTSxDQUFOLElBQVksc0JBR1g7QUFIRCxXQUFZLHNCQUFzQjtJQUNoQyx1RUFBTSxDQUFBO0lBQ04scUVBQUssQ0FBQTtBQUNQLENBQUMsRUFIVyxzQkFBc0IsS0FBdEIsc0JBQXNCLFFBR2pDO0FBK0lEOztHQUVHO0FBQ0gsTUFBTSxVQUFVLGVBQWUsQ0FBQyxJQUF3QjtJQUN0RCxNQUFNLFVBQVUsR0FBa0IsRUFBRSxDQUFDO0lBQ3JDLE1BQU0sYUFBYSxHQUFHLElBQUksYUFBYSxFQUFvQixDQUFDO0lBQzVELGFBQWEsQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7SUFFM0Msb0VBQW9FO0lBQ3BFLGtGQUFrRjtJQUNsRix3QkFBd0I7SUFDeEIsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLHNCQUFzQixDQUFDLE1BQU0sSUFBSSxJQUFJLENBQUMsU0FBUyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztRQUM3RSxhQUFhLENBQUMsR0FBRyxDQUFDLFdBQVcsRUFBRSxXQUFXLENBQUMsSUFBSSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxDQUFDO0lBQ3pGLENBQUM7SUFFRCxJQUFJLElBQUksQ0FBQyxpQkFBaUIsS0FBSyxtQkFBbUIsQ0FBQyxNQUFNLEVBQUUsQ0FBQztRQUMxRCx3RkFBd0Y7UUFDeEYsdURBQXVEO1FBRXZELElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDakMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUMsQ0FBQztRQUMvRixDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsT0FBTyxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUM1QixhQUFhLENBQUMsR0FBRyxDQUFDLFNBQVMsRUFBRSxXQUFXLENBQUMsSUFBSSxDQUFDLE9BQU8sRUFBRSxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxDQUFDO1FBQ3JGLENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzVCLGFBQWEsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLFdBQVcsQ0FBQyxJQUFJLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDLENBQUM7UUFDckYsQ0FBQztJQUNILENBQUM7U0FBTSxJQUFJLElBQUksQ0FBQyxpQkFBaUIsS0FBSyxtQkFBbUIsQ0FBQyxVQUFVLEVBQUUsQ0FBQztRQUNyRSw4RUFBOEU7UUFDOUUsZ0dBQWdHO1FBQ2hHLDhGQUE4RjtRQUM5RixvQkFBb0I7UUFDcEIsTUFBTSxvQkFBb0IsR0FBRyw0QkFBNEIsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUNoRSxJQUFJLG9CQUFvQixLQUFLLElBQUksRUFBRSxDQUFDO1lBQ2xDLFVBQVUsQ0FBQyxJQUFJLENBQUMsb0JBQW9CLENBQUMsQ0FBQztRQUN4QyxDQUFDO0lBQ0gsQ0FBQztTQUFNLENBQUM7UUFDTixxREFBcUQ7SUFDdkQsQ0FBQztJQUVELElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxJQUFJLElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDckQsYUFBYSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQUMsR0FBRyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUNuRixDQUFDO0lBRUQsSUFBSSxJQUFJLENBQUMsRUFBRSxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ3JCLGFBQWEsQ0FBQyxHQUFHLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUVqQyw4RkFBOEY7UUFDOUYsZ0JBQWdCO1FBQ2hCLFVBQVUsQ0FBQyxJQUFJLENBQ2IsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEtBQUssRUFBRSxJQUFJLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsQ0FDbEYsQ0FBQztJQUNKLENBQUM7SUFFRCxNQUFNLFVBQVUsR0FBRyxDQUFDO1NBQ2pCLFVBQVUsQ0FBQyxFQUFFLENBQUMsY0FBYyxDQUFDO1NBQzdCLE1BQU0sQ0FBQyxDQUFDLGFBQWEsQ0FBQyxZQUFZLEVBQUUsQ0FBQyxFQUFFLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUMzRCxNQUFNLElBQUksR0FBRyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUV0QyxPQUFPLEVBQUMsVUFBVSxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUMsQ0FBQztBQUN4QyxDQUFDO0FBRUQ7OztHQUdHO0FBQ0gsTUFBTSxVQUFVLG9DQUFvQyxDQUFDLElBQTZCO0lBQ2hGLE1BQU0sYUFBYSxHQUFHLElBQUksYUFBYSxFQUFvQixDQUFDO0lBQzVELGFBQWEsQ0FBQyxHQUFHLENBQUMsTUFBTSxFQUFFLElBQUksQ0FBQyxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUM1RCxJQUFJLElBQUksQ0FBQyxTQUFTLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDakMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO0lBQ3hFLENBQUM7SUFDRCxJQUFJLElBQUksQ0FBQyxZQUFZLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDcEMsYUFBYSxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDO0lBQzlFLENBQUM7SUFDRCxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDL0IsYUFBYSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ3BFLENBQUM7SUFDRCxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDL0IsYUFBYSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ3BFLENBQUM7SUFDRCxJQUFJLElBQUksQ0FBQyxPQUFPLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDL0IsYUFBYSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDO0lBQ3BFLENBQUM7SUFDRCxJQUFJLElBQUksQ0FBQyxFQUFFLEtBQUssU0FBUyxFQUFFLENBQUM7UUFDMUIsYUFBYSxDQUFDLEdBQUcsQ0FBQyxJQUFJLEVBQUUsSUFBSSxDQUFDLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQzFELENBQUM7SUFDRCxPQUFPLENBQUMsQ0FBQyxVQUFVLENBQUMsRUFBRSxDQUFDLGNBQWMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLGFBQWEsQ0FBQyxZQUFZLEVBQUUsQ0FBQyxDQUFDLENBQUM7QUFDaEYsQ0FBQztBQUVELE1BQU0sVUFBVSxrQkFBa0IsQ0FBQyxJQUF3QjtJQUN6RCxJQUFJLElBQUksQ0FBQyxJQUFJLEtBQUssc0JBQXNCLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDL0MsT0FBTyxJQUFJLENBQUMsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQztJQUMvQyxDQUFDO0lBRUQsTUFBTSxFQUNKLElBQUksRUFBRSxVQUFVLEVBQ2hCLFlBQVksRUFDWixPQUFPLEVBQ1AsT0FBTyxFQUNQLGtCQUFrQixFQUNsQixzQkFBc0IsR0FDdkIsR0FBRyxJQUFJLENBQUM7SUFFVCxPQUFPLElBQUksQ0FBQyxDQUFDLGNBQWMsQ0FDekIsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUMsbUJBQW1CLEVBQUU7UUFDbkMsSUFBSSxDQUFDLENBQUMsY0FBYyxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUM7UUFDckMsc0JBQXNCLEtBQUssSUFBSTtZQUM3QixDQUFDLENBQUMsV0FBVyxDQUFDLFlBQVksQ0FBQztZQUMzQixDQUFDLENBQUMsWUFBWSxDQUFDLHNCQUFzQixDQUFDO1FBQ3hDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ3ZELFdBQVcsQ0FBQyxPQUFPLENBQUM7S0FDckIsQ0FBQyxDQUNILENBQUM7QUFDSixDQUFDO0FBRUQ7Ozs7O0dBS0c7QUFDSCxTQUFTLDRCQUE0QixDQUFDLElBQXdCO0lBQzVELE1BQU0sUUFBUSxHQUFHLElBQUksYUFBYSxFQUs5QixDQUFDO0lBRUwsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLHNCQUFzQixDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQ2hELElBQUksSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDakMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxjQUFjLEVBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUMsQ0FBQztRQUMxRixDQUFDO0lBQ0gsQ0FBQztTQUFNLENBQUM7UUFDTixJQUFJLElBQUksQ0FBQyxzQkFBc0IsRUFBRSxDQUFDO1lBQ2hDLFFBQVEsQ0FBQyxHQUFHLENBQUMsY0FBYyxFQUFFLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO1FBQzVELENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLHNCQUFzQixDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQ2hELElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDNUIsUUFBUSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUMsQ0FBQztRQUNoRixDQUFDO0lBQ0gsQ0FBQztTQUFNLENBQUM7UUFDTixJQUFJLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzNCLFFBQVEsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBQ2xELENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLHNCQUFzQixDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQ2hELElBQUksSUFBSSxDQUFDLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDNUIsUUFBUSxDQUFDLEdBQUcsQ0FBQyxTQUFTLEVBQUUsV0FBVyxDQUFDLElBQUksQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUMsQ0FBQztRQUNoRixDQUFDO0lBQ0gsQ0FBQztTQUFNLENBQUM7UUFDTixJQUFJLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1lBQzNCLFFBQVEsQ0FBQyxHQUFHLENBQUMsU0FBUyxFQUFFLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1FBQ2xELENBQUM7SUFDSCxDQUFDO0lBRUQsSUFBSSxJQUFJLENBQUMsSUFBSSxLQUFLLHNCQUFzQixDQUFDLEtBQUssSUFBSSxJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztRQUMzRSxRQUFRLENBQUMsR0FBRyxDQUFDLFdBQVcsRUFBRSxJQUFJLENBQUMsbUJBQW1CLENBQUMsQ0FBQztJQUN0RCxDQUFDO0lBRUQsSUFBSSxNQUFNLENBQUMsSUFBSSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxNQUFNLEtBQUssQ0FBQyxFQUFFLENBQUM7UUFDOUMsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDO0lBRUQsd0JBQXdCO0lBQ3hCLE1BQU0sTUFBTSxHQUFHLElBQUksQ0FBQyxDQUFDLGtCQUFrQjtJQUNyQyxRQUFRLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxFQUFFLENBQUMsZ0JBQWdCLENBQUM7SUFDMUMsVUFBVSxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxLQUFLLEVBQUUsUUFBUSxDQUFDLFlBQVksRUFBRSxDQUFDLENBQ3RELENBQUM7SUFFRiw2Q0FBNkM7SUFDN0MsTUFBTSxXQUFXLEdBQUcsd0JBQXdCLENBQUMsTUFBTSxDQUFDLENBQUM7SUFFckQsNkRBQTZEO0lBQzdELE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxDQUFDLFlBQVksQ0FBQyxZQUFZLENBQUMsRUFBRSxFQUFFLGdCQUFnQixDQUFDLENBQUMsV0FBVyxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUMsQ0FBQztJQUUxRixpRUFBaUU7SUFDakUsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLENBQUMsa0JBQWtCLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxVQUFVLENBQUMsRUFBRSxDQUFDLENBQUM7SUFFeEUsT0FBTyxRQUFRLENBQUMsTUFBTSxFQUFFLENBQUM7QUFDM0IsQ0FBQztBQUVELFNBQVMsV0FBVyxDQUFDLEdBQWtCO0lBQ3JDLE1BQU0sS0FBSyxHQUFHLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUM7SUFDdkQsT0FBTyxHQUFHLENBQUMsTUFBTSxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxTQUFTLENBQUM7QUFDOUUsQ0FBQztBQUVELFNBQVMsWUFBWSxDQUFDLEtBQXFCO0lBQ3pDLE1BQU0sV0FBVyxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUM1RCxPQUFPLEtBQUssQ0FBQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYyxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLFNBQVMsQ0FBQztBQUN0RixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuZGV2L2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQge1IzRGVjbGFyZU5nTW9kdWxlRmFjYWRlfSBmcm9tICcuLi9jb21waWxlcl9mYWNhZGVfaW50ZXJmYWNlJztcbmltcG9ydCAqIGFzIG8gZnJvbSAnLi4vb3V0cHV0L291dHB1dF9hc3QnO1xuXG5pbXBvcnQge0lkZW50aWZpZXJzIGFzIFIzfSBmcm9tICcuL3IzX2lkZW50aWZpZXJzJztcbmltcG9ydCB7aml0T25seUd1YXJkZWRFeHByZXNzaW9uLCBSM0NvbXBpbGVkRXhwcmVzc2lvbiwgUjNSZWZlcmVuY2UsIHJlZnNUb0FycmF5fSBmcm9tICcuL3V0aWwnO1xuaW1wb3J0IHtEZWZpbml0aW9uTWFwfSBmcm9tICcuL3ZpZXcvdXRpbCc7XG5cbi8qKlxuICogSG93IHRoZSBzZWxlY3RvciBzY29wZSBvZiBhbiBOZ01vZHVsZSAoaXRzIGRlY2xhcmF0aW9ucywgaW1wb3J0cywgYW5kIGV4cG9ydHMpIHNob3VsZCBiZSBlbWl0dGVkXG4gKiBhcyBhIHBhcnQgb2YgdGhlIE5nTW9kdWxlIGRlZmluaXRpb24uXG4gKi9cbmV4cG9ydCBlbnVtIFIzU2VsZWN0b3JTY29wZU1vZGUge1xuICAvKipcbiAgICogRW1pdCB0aGUgZGVjbGFyYXRpb25zIGlubGluZSBpbnRvIHRoZSBtb2R1bGUgZGVmaW5pdGlvbi5cbiAgICpcbiAgICogVGhpcyBvcHRpb24gaXMgdXNlZnVsIGluIGNlcnRhaW4gY29udGV4dHMgd2hlcmUgaXQncyBrbm93biB0aGF0IEpJVCBzdXBwb3J0IGlzIHJlcXVpcmVkLiBUaGVcbiAgICogdHJhZGVvZmYgaGVyZSBpcyB0aGF0IHRoaXMgZW1pdCBzdHlsZSBwcmV2ZW50cyBkaXJlY3RpdmVzIGFuZCBwaXBlcyBmcm9tIGJlaW5nIHRyZWUtc2hha2VuIGlmXG4gICAqIHRoZXkgYXJlIHVudXNlZCwgYnV0IHRoZSBOZ01vZHVsZSBpcyB1c2VkLlxuICAgKi9cbiAgSW5saW5lLFxuXG4gIC8qKlxuICAgKiBFbWl0IHRoZSBkZWNsYXJhdGlvbnMgdXNpbmcgYSBzaWRlIGVmZmVjdGZ1bCBmdW5jdGlvbiBjYWxsLCBgybXJtXNldE5nTW9kdWxlU2NvcGVgLCB0aGF0IGlzXG4gICAqIGd1YXJkZWQgd2l0aCB0aGUgYG5nSml0TW9kZWAgZmxhZy5cbiAgICpcbiAgICogVGhpcyBmb3JtIG9mIGVtaXQgc3VwcG9ydHMgSklUIGFuZCBjYW4gYmUgb3B0aW1pemVkIGF3YXkgaWYgdGhlIGBuZ0ppdE1vZGVgIGZsYWcgaXMgc2V0IHRvXG4gICAqIGZhbHNlLCB3aGljaCBhbGxvd3MgdW51c2VkIGRpcmVjdGl2ZXMgYW5kIHBpcGVzIHRvIGJlIHRyZWUtc2hha2VuLlxuICAgKi9cbiAgU2lkZUVmZmVjdCxcblxuICAvKipcbiAgICogRG9uJ3QgZ2VuZXJhdGUgc2VsZWN0b3Igc2NvcGVzIGF0IGFsbC5cbiAgICpcbiAgICogVGhpcyBpcyB1c2VmdWwgZm9yIGNvbnRleHRzIHdoZXJlIEpJVCBzdXBwb3J0IGlzIGtub3duIHRvIGJlIHVubmVjZXNzYXJ5LlxuICAgKi9cbiAgT21pdCxcbn1cblxuLyoqXG4gKiBUaGUgdHlwZSBvZiB0aGUgTmdNb2R1bGUgbWV0YSBkYXRhLlxuICogLSBHbG9iYWw6IFVzZWQgZm9yIGZ1bGwgYW5kIHBhcnRpYWwgY29tcGlsYXRpb24gbW9kZXMgd2hpY2ggbWFpbmx5IGluY2x1ZGVzIFIzUmVmZXJlbmNlcy5cbiAqIC0gTG9jYWw6IFVzZWQgZm9yIHRoZSBsb2NhbCBjb21waWxhdGlvbiBtb2RlIHdoaWNoIG1haW5seSBpbmNsdWRlcyB0aGUgcmF3IGV4cHJlc3Npb25zIGFzIGFwcGVhcnNcbiAqIGluIHRoZSBOZ01vZHVsZSBkZWNvcmF0b3IuXG4gKi9cbmV4cG9ydCBlbnVtIFIzTmdNb2R1bGVNZXRhZGF0YUtpbmQge1xuICBHbG9iYWwsXG4gIExvY2FsLFxufVxuXG5pbnRlcmZhY2UgUjNOZ01vZHVsZU1ldGFkYXRhQ29tbW9uIHtcbiAga2luZDogUjNOZ01vZHVsZU1ldGFkYXRhS2luZDtcblxuICAvKipcbiAgICogQW4gZXhwcmVzc2lvbiByZXByZXNlbnRpbmcgdGhlIG1vZHVsZSB0eXBlIGJlaW5nIGNvbXBpbGVkLlxuICAgKi9cbiAgdHlwZTogUjNSZWZlcmVuY2U7XG5cbiAgLyoqXG4gICAqIEhvdyB0byBlbWl0IHRoZSBzZWxlY3RvciBzY29wZSB2YWx1ZXMgKGRlY2xhcmF0aW9ucywgaW1wb3J0cywgZXhwb3J0cykuXG4gICAqL1xuICBzZWxlY3RvclNjb3BlTW9kZTogUjNTZWxlY3RvclNjb3BlTW9kZTtcblxuICAvKipcbiAgICogVGhlIHNldCBvZiBzY2hlbWFzIHRoYXQgZGVjbGFyZSBlbGVtZW50cyB0byBiZSBhbGxvd2VkIGluIHRoZSBOZ01vZHVsZS5cbiAgICovXG4gIHNjaGVtYXM6IFIzUmVmZXJlbmNlW10gfCBudWxsO1xuXG4gIC8qKiBVbmlxdWUgSUQgb3IgZXhwcmVzc2lvbiByZXByZXNlbnRpbmcgdGhlIHVuaXF1ZSBJRCBvZiBhbiBOZ01vZHVsZS4gKi9cbiAgaWQ6IG8uRXhwcmVzc2lvbiB8IG51bGw7XG59XG5cbi8qKlxuICogTWV0YWRhdGEgcmVxdWlyZWQgYnkgdGhlIG1vZHVsZSBjb21waWxlciBpbiBmdWxsL3BhcnRpYWwgbW9kZSB0byBnZW5lcmF0ZSBhIG1vZHVsZSBkZWYgKGDJtW1vZGApXG4gKiBmb3IgYSB0eXBlLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIFIzTmdNb2R1bGVNZXRhZGF0YUdsb2JhbCBleHRlbmRzIFIzTmdNb2R1bGVNZXRhZGF0YUNvbW1vbiB7XG4gIGtpbmQ6IFIzTmdNb2R1bGVNZXRhZGF0YUtpbmQuR2xvYmFsO1xuXG4gIC8qKlxuICAgKiBBbiBhcnJheSBvZiBleHByZXNzaW9ucyByZXByZXNlbnRpbmcgdGhlIGJvb3RzdHJhcCBjb21wb25lbnRzIHNwZWNpZmllZCBieSB0aGUgbW9kdWxlLlxuICAgKi9cbiAgYm9vdHN0cmFwOiBSM1JlZmVyZW5jZVtdO1xuXG4gIC8qKlxuICAgKiBBbiBhcnJheSBvZiBleHByZXNzaW9ucyByZXByZXNlbnRpbmcgdGhlIGRpcmVjdGl2ZXMgYW5kIHBpcGVzIGRlY2xhcmVkIGJ5IHRoZSBtb2R1bGUuXG4gICAqL1xuICBkZWNsYXJhdGlvbnM6IFIzUmVmZXJlbmNlW107XG5cbiAgLyoqXG4gICAqIFRob3NlIGRlY2xhcmF0aW9ucyB3aGljaCBzaG91bGQgYmUgdmlzaWJsZSB0byBkb3duc3RyZWFtIGNvbnN1bWVycy4gSWYgbm90IHNwZWNpZmllZCwgYWxsXG4gICAqIGRlY2xhcmF0aW9ucyBhcmUgbWFkZSB2aXNpYmxlIHRvIGRvd25zdHJlYW0gY29uc3VtZXJzLlxuICAgKi9cbiAgcHVibGljRGVjbGFyYXRpb25UeXBlczogby5FeHByZXNzaW9uW10gfCBudWxsO1xuXG4gIC8qKlxuICAgKiBBbiBhcnJheSBvZiBleHByZXNzaW9ucyByZXByZXNlbnRpbmcgdGhlIGltcG9ydHMgb2YgdGhlIG1vZHVsZS5cbiAgICovXG4gIGltcG9ydHM6IFIzUmVmZXJlbmNlW107XG5cbiAgLyoqXG4gICAqIFdoZXRoZXIgb3Igbm90IHRvIGluY2x1ZGUgYGltcG9ydHNgIGluIGdlbmVyYXRlZCB0eXBlIGRlY2xhcmF0aW9ucy5cbiAgICovXG4gIGluY2x1ZGVJbXBvcnRUeXBlczogYm9vbGVhbjtcblxuICAvKipcbiAgICogQW4gYXJyYXkgb2YgZXhwcmVzc2lvbnMgcmVwcmVzZW50aW5nIHRoZSBleHBvcnRzIG9mIHRoZSBtb2R1bGUuXG4gICAqL1xuICBleHBvcnRzOiBSM1JlZmVyZW5jZVtdO1xuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRvIGdlbmVyYXRlIGNsb3N1cmUgd3JhcHBlcnMgZm9yIGJvb3RzdHJhcCwgZGVjbGFyYXRpb25zLCBpbXBvcnRzLCBhbmQgZXhwb3J0cy5cbiAgICovXG4gIGNvbnRhaW5zRm9yd2FyZERlY2xzOiBib29sZWFuO1xufVxuXG4vKipcbiAqIE1ldGFkYXRhIHJlcXVpcmVkIGJ5IHRoZSBtb2R1bGUgY29tcGlsZXIgaW4gbG9jYWwgbW9kZSB0byBnZW5lcmF0ZSBhIG1vZHVsZSBkZWYgKGDJtW1vZGApIGZvciBhXG4gKiB0eXBlLlxuICovXG5leHBvcnQgaW50ZXJmYWNlIFIzTmdNb2R1bGVNZXRhZGF0YUxvY2FsIGV4dGVuZHMgUjNOZ01vZHVsZU1ldGFkYXRhQ29tbW9uIHtcbiAga2luZDogUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5Mb2NhbDtcblxuICAvKipcbiAgICogVGhlIG91dHB1dCBleHByZXNzaW9uIHJlcHJlc2VudGluZyB0aGUgYm9vdHN0cmFwIGNvbXBvbmVudHMgc3BlY2lmaWVkIGJ5IHRoZSBtb2R1bGUuXG4gICAqL1xuICBib290c3RyYXBFeHByZXNzaW9uOiBvLkV4cHJlc3Npb24gfCBudWxsO1xuXG4gIC8qKlxuICAgKiBUaGUgb3V0cHV0IGV4cHJlc3Npb24gcmVwcmVzZW50aW5nIHRoZSBkZWNsYXJhdGlvbnMgb2YgdGhlIG1vZHVsZS5cbiAgICovXG4gIGRlY2xhcmF0aW9uc0V4cHJlc3Npb246IG8uRXhwcmVzc2lvbiB8IG51bGw7XG5cbiAgLyoqXG4gICAqIFRoZSBvdXRwdXQgZXhwcmVzc2lvbiByZXByZXNlbnRpbmcgdGhlIGltcG9ydHMgb2YgdGhlIG1vZHVsZS5cbiAgICovXG4gIGltcG9ydHNFeHByZXNzaW9uOiBvLkV4cHJlc3Npb24gfCBudWxsO1xuXG4gIC8qKlxuICAgKiBUaGUgb3V0cHV0IGV4cHJlc3Npb24gcmVwcmVzZW50aW5nIHRoZSBleHBvcnRzIG9mIHRoZSBtb2R1bGUuXG4gICAqL1xuICBleHBvcnRzRXhwcmVzc2lvbjogby5FeHByZXNzaW9uIHwgbnVsbDtcblxuICAvKipcbiAgICogTG9jYWwgY29tcGlsYXRpb24gbW9kZSBhbHdheXMgcmVxdWlyZXMgc2NvcGUgdG8gYmUgaGFuZGxlZCB1c2luZyBzaWRlIGVmZmVjdCBmdW5jdGlvbiBjYWxscy5cbiAgICovXG4gIHNlbGVjdG9yU2NvcGVNb2RlOiBSM1NlbGVjdG9yU2NvcGVNb2RlLlNpZGVFZmZlY3Q7XG59XG5cbi8qKlxuICogTWV0YWRhdGEgcmVxdWlyZWQgYnkgdGhlIG1vZHVsZSBjb21waWxlciB0byBnZW5lcmF0ZSBhIG1vZHVsZSBkZWYgKGDJtW1vZGApIGZvciBhIHR5cGUuXG4gKi9cbmV4cG9ydCB0eXBlIFIzTmdNb2R1bGVNZXRhZGF0YSA9IFIzTmdNb2R1bGVNZXRhZGF0YUdsb2JhbCB8IFIzTmdNb2R1bGVNZXRhZGF0YUxvY2FsO1xuXG4vKipcbiAqIFRoZSBzaGFwZSBvZiB0aGUgb2JqZWN0IGxpdGVyYWwgdGhhdCBpcyBwYXNzZWQgdG8gdGhlIGDJtcm1ZGVmaW5lTmdNb2R1bGUoKWAgY2FsbC5cbiAqL1xuaW50ZXJmYWNlIFIzTmdNb2R1bGVEZWZNYXAge1xuICAvKipcbiAgICogQW4gZXhwcmVzc2lvbiByZXByZXNlbnRpbmcgdGhlIG1vZHVsZSB0eXBlIGJlaW5nIGNvbXBpbGVkLlxuICAgKi9cbiAgdHlwZTogby5FeHByZXNzaW9uO1xuICAvKipcbiAgICogQW4gZXhwcmVzc2lvbiBldmFsdWF0aW5nIHRvIGFuIGFycmF5IG9mIGV4cHJlc3Npb25zIHJlcHJlc2VudGluZyB0aGUgYm9vdHN0cmFwIGNvbXBvbmVudHNcbiAgICogc3BlY2lmaWVkIGJ5IHRoZSBtb2R1bGUuXG4gICAqL1xuICBib290c3RyYXA/OiBvLkV4cHJlc3Npb247XG4gIC8qKlxuICAgKiBBbiBleHByZXNzaW9uIGV2YWx1YXRpbmcgdG8gYW4gYXJyYXkgb2YgZXhwcmVzc2lvbnMgcmVwcmVzZW50aW5nIHRoZSBkaXJlY3RpdmVzIGFuZCBwaXBlc1xuICAgKiBkZWNsYXJlZCBieSB0aGUgbW9kdWxlLlxuICAgKi9cbiAgZGVjbGFyYXRpb25zPzogby5FeHByZXNzaW9uO1xuICAvKipcbiAgICogQW4gZXhwcmVzc2lvbiBldmFsdWF0aW5nIHRvIGFuIGFycmF5IG9mIGV4cHJlc3Npb25zIHJlcHJlc2VudGluZyB0aGUgaW1wb3J0cyBvZiB0aGUgbW9kdWxlLlxuICAgKi9cbiAgaW1wb3J0cz86IG8uRXhwcmVzc2lvbjtcbiAgLyoqXG4gICAqIEFuIGV4cHJlc3Npb24gZXZhbHVhdGluZyB0byBhbiBhcnJheSBvZiBleHByZXNzaW9ucyByZXByZXNlbnRpbmcgdGhlIGV4cG9ydHMgb2YgdGhlIG1vZHVsZS5cbiAgICovXG4gIGV4cG9ydHM/OiBvLkV4cHJlc3Npb247XG4gIC8qKlxuICAgKiBBIGxpdGVyYWwgYXJyYXkgZXhwcmVzc2lvbiBjb250YWluaW5nIHRoZSBzY2hlbWFzIHRoYXQgZGVjbGFyZSBlbGVtZW50cyB0byBiZSBhbGxvd2VkIGluIHRoZVxuICAgKiBOZ01vZHVsZS5cbiAgICovXG4gIHNjaGVtYXM/OiBvLkxpdGVyYWxBcnJheUV4cHI7XG4gIC8qKlxuICAgKiBBbiBleHByZXNzaW9uIGV2YWx1YXRpbmcgdG8gdGhlIHVuaXF1ZSBJRCBvZiBhbiBOZ01vZHVsZS5cbiAgICogKi9cbiAgaWQ/OiBvLkV4cHJlc3Npb247XG59XG5cbi8qKlxuICogQ29uc3RydWN0IGFuIGBSM05nTW9kdWxlRGVmYCBmb3IgdGhlIGdpdmVuIGBSM05nTW9kdWxlTWV0YWRhdGFgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gY29tcGlsZU5nTW9kdWxlKG1ldGE6IFIzTmdNb2R1bGVNZXRhZGF0YSk6IFIzQ29tcGlsZWRFeHByZXNzaW9uIHtcbiAgY29uc3Qgc3RhdGVtZW50czogby5TdGF0ZW1lbnRbXSA9IFtdO1xuICBjb25zdCBkZWZpbml0aW9uTWFwID0gbmV3IERlZmluaXRpb25NYXA8UjNOZ01vZHVsZURlZk1hcD4oKTtcbiAgZGVmaW5pdGlvbk1hcC5zZXQoJ3R5cGUnLCBtZXRhLnR5cGUudmFsdWUpO1xuXG4gIC8vIEFzc2lnbiBib290c3RyYXAgZGVmaW5pdGlvbi4gSW4gbG9jYWwgY29tcGlsYXRpb24gbW9kZSAoaS5lLiwgZm9yXG4gIC8vIGBSM05nTW9kdWxlTWV0YWRhdGFLaW5kLkxPQ0FMYCkgd2UgYXNzaWduIHRoZSBib290c3RyYXAgZmllbGQgdXNpbmcgdGhlIHJ1bnRpbWVcbiAgLy8gYMm1ybVzZXROZ01vZHVsZVNjb3BlYC5cbiAgaWYgKG1ldGEua2luZCA9PT0gUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5HbG9iYWwgJiYgbWV0YS5ib290c3RyYXAubGVuZ3RoID4gMCkge1xuICAgIGRlZmluaXRpb25NYXAuc2V0KCdib290c3RyYXAnLCByZWZzVG9BcnJheShtZXRhLmJvb3RzdHJhcCwgbWV0YS5jb250YWluc0ZvcndhcmREZWNscykpO1xuICB9XG5cbiAgaWYgKG1ldGEuc2VsZWN0b3JTY29wZU1vZGUgPT09IFIzU2VsZWN0b3JTY29wZU1vZGUuSW5saW5lKSB7XG4gICAgLy8gSWYgcmVxdWVzdGVkIHRvIGVtaXQgc2NvcGUgaW5mb3JtYXRpb24gaW5saW5lLCBwYXNzIHRoZSBgZGVjbGFyYXRpb25zYCwgYGltcG9ydHNgIGFuZFxuICAgIC8vIGBleHBvcnRzYCB0byB0aGUgYMm1ybVkZWZpbmVOZ01vZHVsZSgpYCBjYWxsIGRpcmVjdGx5LlxuXG4gICAgaWYgKG1ldGEuZGVjbGFyYXRpb25zLmxlbmd0aCA+IDApIHtcbiAgICAgIGRlZmluaXRpb25NYXAuc2V0KCdkZWNsYXJhdGlvbnMnLCByZWZzVG9BcnJheShtZXRhLmRlY2xhcmF0aW9ucywgbWV0YS5jb250YWluc0ZvcndhcmREZWNscykpO1xuICAgIH1cblxuICAgIGlmIChtZXRhLmltcG9ydHMubGVuZ3RoID4gMCkge1xuICAgICAgZGVmaW5pdGlvbk1hcC5zZXQoJ2ltcG9ydHMnLCByZWZzVG9BcnJheShtZXRhLmltcG9ydHMsIG1ldGEuY29udGFpbnNGb3J3YXJkRGVjbHMpKTtcbiAgICB9XG5cbiAgICBpZiAobWV0YS5leHBvcnRzLmxlbmd0aCA+IDApIHtcbiAgICAgIGRlZmluaXRpb25NYXAuc2V0KCdleHBvcnRzJywgcmVmc1RvQXJyYXkobWV0YS5leHBvcnRzLCBtZXRhLmNvbnRhaW5zRm9yd2FyZERlY2xzKSk7XG4gICAgfVxuICB9IGVsc2UgaWYgKG1ldGEuc2VsZWN0b3JTY29wZU1vZGUgPT09IFIzU2VsZWN0b3JTY29wZU1vZGUuU2lkZUVmZmVjdCkge1xuICAgIC8vIEluIHRoaXMgbW9kZSwgc2NvcGUgaW5mb3JtYXRpb24gaXMgbm90IHBhc3NlZCBpbnRvIGDJtcm1ZGVmaW5lTmdNb2R1bGVgIGFzIGl0XG4gICAgLy8gd291bGQgcHJldmVudCB0cmVlLXNoYWtpbmcgb2YgdGhlIGRlY2xhcmF0aW9ucywgaW1wb3J0cyBhbmQgZXhwb3J0cyByZWZlcmVuY2VzLiBJbnN0ZWFkLCBpdCdzXG4gICAgLy8gcGF0Y2hlZCBvbnRvIHRoZSBOZ01vZHVsZSBkZWZpbml0aW9uIHdpdGggYSBgybXJtXNldE5nTW9kdWxlU2NvcGVgIGNhbGwgdGhhdCdzIGd1YXJkZWQgYnkgdGhlXG4gICAgLy8gYG5nSml0TW9kZWAgZmxhZy5cbiAgICBjb25zdCBzZXROZ01vZHVsZVNjb3BlQ2FsbCA9IGdlbmVyYXRlU2V0TmdNb2R1bGVTY29wZUNhbGwobWV0YSk7XG4gICAgaWYgKHNldE5nTW9kdWxlU2NvcGVDYWxsICE9PSBudWxsKSB7XG4gICAgICBzdGF0ZW1lbnRzLnB1c2goc2V0TmdNb2R1bGVTY29wZUNhbGwpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICAvLyBTZWxlY3RvciBzY29wZSBlbWl0IHdhcyBub3QgcmVxdWVzdGVkLCBzbyBza2lwIGl0LlxuICB9XG5cbiAgaWYgKG1ldGEuc2NoZW1hcyAhPT0gbnVsbCAmJiBtZXRhLnNjaGVtYXMubGVuZ3RoID4gMCkge1xuICAgIGRlZmluaXRpb25NYXAuc2V0KCdzY2hlbWFzJywgby5saXRlcmFsQXJyKG1ldGEuc2NoZW1hcy5tYXAoKHJlZikgPT4gcmVmLnZhbHVlKSkpO1xuICB9XG5cbiAgaWYgKG1ldGEuaWQgIT09IG51bGwpIHtcbiAgICBkZWZpbml0aW9uTWFwLnNldCgnaWQnLCBtZXRhLmlkKTtcblxuICAgIC8vIEdlbmVyYXRlIGEgc2lkZS1lZmZlY3RmdWwgY2FsbCB0byByZWdpc3RlciB0aGlzIE5nTW9kdWxlIGJ5IGl0cyBpZCwgYXMgcGVyIHRoZSBzZW1hbnRpY3Mgb2ZcbiAgICAvLyBOZ01vZHVsZSBpZHMuXG4gICAgc3RhdGVtZW50cy5wdXNoKFxuICAgICAgby5pbXBvcnRFeHByKFIzLnJlZ2lzdGVyTmdNb2R1bGVUeXBlKS5jYWxsRm4oW21ldGEudHlwZS52YWx1ZSwgbWV0YS5pZF0pLnRvU3RtdCgpLFxuICAgICk7XG4gIH1cblxuICBjb25zdCBleHByZXNzaW9uID0gb1xuICAgIC5pbXBvcnRFeHByKFIzLmRlZmluZU5nTW9kdWxlKVxuICAgIC5jYWxsRm4oW2RlZmluaXRpb25NYXAudG9MaXRlcmFsTWFwKCldLCB1bmRlZmluZWQsIHRydWUpO1xuICBjb25zdCB0eXBlID0gY3JlYXRlTmdNb2R1bGVUeXBlKG1ldGEpO1xuXG4gIHJldHVybiB7ZXhwcmVzc2lvbiwgdHlwZSwgc3RhdGVtZW50c307XG59XG5cbi8qKlxuICogVGhpcyBmdW5jdGlvbiBpcyB1c2VkIGluIEpJVCBtb2RlIHRvIGdlbmVyYXRlIHRoZSBjYWxsIHRvIGDJtcm1ZGVmaW5lTmdNb2R1bGUoKWAgZnJvbSBhIGNhbGwgdG9cbiAqIGDJtcm1bmdEZWNsYXJlTmdNb2R1bGUoKWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjb21waWxlTmdNb2R1bGVEZWNsYXJhdGlvbkV4cHJlc3Npb24obWV0YTogUjNEZWNsYXJlTmdNb2R1bGVGYWNhZGUpOiBvLkV4cHJlc3Npb24ge1xuICBjb25zdCBkZWZpbml0aW9uTWFwID0gbmV3IERlZmluaXRpb25NYXA8UjNOZ01vZHVsZURlZk1hcD4oKTtcbiAgZGVmaW5pdGlvbk1hcC5zZXQoJ3R5cGUnLCBuZXcgby5XcmFwcGVkTm9kZUV4cHIobWV0YS50eXBlKSk7XG4gIGlmIChtZXRhLmJvb3RzdHJhcCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgZGVmaW5pdGlvbk1hcC5zZXQoJ2Jvb3RzdHJhcCcsIG5ldyBvLldyYXBwZWROb2RlRXhwcihtZXRhLmJvb3RzdHJhcCkpO1xuICB9XG4gIGlmIChtZXRhLmRlY2xhcmF0aW9ucyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgZGVmaW5pdGlvbk1hcC5zZXQoJ2RlY2xhcmF0aW9ucycsIG5ldyBvLldyYXBwZWROb2RlRXhwcihtZXRhLmRlY2xhcmF0aW9ucykpO1xuICB9XG4gIGlmIChtZXRhLmltcG9ydHMgIT09IHVuZGVmaW5lZCkge1xuICAgIGRlZmluaXRpb25NYXAuc2V0KCdpbXBvcnRzJywgbmV3IG8uV3JhcHBlZE5vZGVFeHByKG1ldGEuaW1wb3J0cykpO1xuICB9XG4gIGlmIChtZXRhLmV4cG9ydHMgIT09IHVuZGVmaW5lZCkge1xuICAgIGRlZmluaXRpb25NYXAuc2V0KCdleHBvcnRzJywgbmV3IG8uV3JhcHBlZE5vZGVFeHByKG1ldGEuZXhwb3J0cykpO1xuICB9XG4gIGlmIChtZXRhLnNjaGVtYXMgIT09IHVuZGVmaW5lZCkge1xuICAgIGRlZmluaXRpb25NYXAuc2V0KCdzY2hlbWFzJywgbmV3IG8uV3JhcHBlZE5vZGVFeHByKG1ldGEuc2NoZW1hcykpO1xuICB9XG4gIGlmIChtZXRhLmlkICE9PSB1bmRlZmluZWQpIHtcbiAgICBkZWZpbml0aW9uTWFwLnNldCgnaWQnLCBuZXcgby5XcmFwcGVkTm9kZUV4cHIobWV0YS5pZCkpO1xuICB9XG4gIHJldHVybiBvLmltcG9ydEV4cHIoUjMuZGVmaW5lTmdNb2R1bGUpLmNhbGxGbihbZGVmaW5pdGlvbk1hcC50b0xpdGVyYWxNYXAoKV0pO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlTmdNb2R1bGVUeXBlKG1ldGE6IFIzTmdNb2R1bGVNZXRhZGF0YSk6IG8uRXhwcmVzc2lvblR5cGUge1xuICBpZiAobWV0YS5raW5kID09PSBSM05nTW9kdWxlTWV0YWRhdGFLaW5kLkxvY2FsKSB7XG4gICAgcmV0dXJuIG5ldyBvLkV4cHJlc3Npb25UeXBlKG1ldGEudHlwZS52YWx1ZSk7XG4gIH1cblxuICBjb25zdCB7XG4gICAgdHlwZTogbW9kdWxlVHlwZSxcbiAgICBkZWNsYXJhdGlvbnMsXG4gICAgZXhwb3J0cyxcbiAgICBpbXBvcnRzLFxuICAgIGluY2x1ZGVJbXBvcnRUeXBlcyxcbiAgICBwdWJsaWNEZWNsYXJhdGlvblR5cGVzLFxuICB9ID0gbWV0YTtcblxuICByZXR1cm4gbmV3IG8uRXhwcmVzc2lvblR5cGUoXG4gICAgby5pbXBvcnRFeHByKFIzLk5nTW9kdWxlRGVjbGFyYXRpb24sIFtcbiAgICAgIG5ldyBvLkV4cHJlc3Npb25UeXBlKG1vZHVsZVR5cGUudHlwZSksXG4gICAgICBwdWJsaWNEZWNsYXJhdGlvblR5cGVzID09PSBudWxsXG4gICAgICAgID8gdHVwbGVUeXBlT2YoZGVjbGFyYXRpb25zKVxuICAgICAgICA6IHR1cGxlT2ZUeXBlcyhwdWJsaWNEZWNsYXJhdGlvblR5cGVzKSxcbiAgICAgIGluY2x1ZGVJbXBvcnRUeXBlcyA/IHR1cGxlVHlwZU9mKGltcG9ydHMpIDogby5OT05FX1RZUEUsXG4gICAgICB0dXBsZVR5cGVPZihleHBvcnRzKSxcbiAgICBdKSxcbiAgKTtcbn1cblxuLyoqXG4gKiBHZW5lcmF0ZXMgYSBmdW5jdGlvbiBjYWxsIHRvIGDJtcm1c2V0TmdNb2R1bGVTY29wZWAgd2l0aCBhbGwgbmVjZXNzYXJ5IGluZm9ybWF0aW9uIHNvIHRoYXQgdGhlXG4gKiB0cmFuc2l0aXZlIG1vZHVsZSBzY29wZSBjYW4gYmUgY29tcHV0ZWQgZHVyaW5nIHJ1bnRpbWUgaW4gSklUIG1vZGUuIFRoaXMgY2FsbCBpcyBtYXJrZWQgcHVyZVxuICogc3VjaCB0aGF0IHRoZSByZWZlcmVuY2VzIHRvIGRlY2xhcmF0aW9ucywgaW1wb3J0cyBhbmQgZXhwb3J0cyBtYXkgYmUgZWxpZGVkIGNhdXNpbmcgdGhlc2VcbiAqIHN5bWJvbHMgdG8gYmVjb21lIHRyZWUtc2hha2VhYmxlLlxuICovXG5mdW5jdGlvbiBnZW5lcmF0ZVNldE5nTW9kdWxlU2NvcGVDYWxsKG1ldGE6IFIzTmdNb2R1bGVNZXRhZGF0YSk6IG8uU3RhdGVtZW50IHwgbnVsbCB7XG4gIGNvbnN0IHNjb3BlTWFwID0gbmV3IERlZmluaXRpb25NYXA8e1xuICAgIGRlY2xhcmF0aW9uczogby5FeHByZXNzaW9uO1xuICAgIGltcG9ydHM6IG8uRXhwcmVzc2lvbjtcbiAgICBleHBvcnRzOiBvLkV4cHJlc3Npb247XG4gICAgYm9vdHN0cmFwOiBvLkV4cHJlc3Npb247XG4gIH0+KCk7XG5cbiAgaWYgKG1ldGEua2luZCA9PT0gUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5HbG9iYWwpIHtcbiAgICBpZiAobWV0YS5kZWNsYXJhdGlvbnMubGVuZ3RoID4gMCkge1xuICAgICAgc2NvcGVNYXAuc2V0KCdkZWNsYXJhdGlvbnMnLCByZWZzVG9BcnJheShtZXRhLmRlY2xhcmF0aW9ucywgbWV0YS5jb250YWluc0ZvcndhcmREZWNscykpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBpZiAobWV0YS5kZWNsYXJhdGlvbnNFeHByZXNzaW9uKSB7XG4gICAgICBzY29wZU1hcC5zZXQoJ2RlY2xhcmF0aW9ucycsIG1ldGEuZGVjbGFyYXRpb25zRXhwcmVzc2lvbik7XG4gICAgfVxuICB9XG5cbiAgaWYgKG1ldGEua2luZCA9PT0gUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5HbG9iYWwpIHtcbiAgICBpZiAobWV0YS5pbXBvcnRzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNjb3BlTWFwLnNldCgnaW1wb3J0cycsIHJlZnNUb0FycmF5KG1ldGEuaW1wb3J0cywgbWV0YS5jb250YWluc0ZvcndhcmREZWNscykpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBpZiAobWV0YS5pbXBvcnRzRXhwcmVzc2lvbikge1xuICAgICAgc2NvcGVNYXAuc2V0KCdpbXBvcnRzJywgbWV0YS5pbXBvcnRzRXhwcmVzc2lvbik7XG4gICAgfVxuICB9XG5cbiAgaWYgKG1ldGEua2luZCA9PT0gUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5HbG9iYWwpIHtcbiAgICBpZiAobWV0YS5leHBvcnRzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNjb3BlTWFwLnNldCgnZXhwb3J0cycsIHJlZnNUb0FycmF5KG1ldGEuZXhwb3J0cywgbWV0YS5jb250YWluc0ZvcndhcmREZWNscykpO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBpZiAobWV0YS5leHBvcnRzRXhwcmVzc2lvbikge1xuICAgICAgc2NvcGVNYXAuc2V0KCdleHBvcnRzJywgbWV0YS5leHBvcnRzRXhwcmVzc2lvbik7XG4gICAgfVxuICB9XG5cbiAgaWYgKG1ldGEua2luZCA9PT0gUjNOZ01vZHVsZU1ldGFkYXRhS2luZC5Mb2NhbCAmJiBtZXRhLmJvb3RzdHJhcEV4cHJlc3Npb24pIHtcbiAgICBzY29wZU1hcC5zZXQoJ2Jvb3RzdHJhcCcsIG1ldGEuYm9vdHN0cmFwRXhwcmVzc2lvbik7XG4gIH1cblxuICBpZiAoT2JqZWN0LmtleXMoc2NvcGVNYXAudmFsdWVzKS5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIC8vIHNldE5nTW9kdWxlU2NvcGUoLi4uKVxuICBjb25zdCBmbkNhbGwgPSBuZXcgby5JbnZva2VGdW5jdGlvbkV4cHIoXG4gICAgLyogZm4gKi8gby5pbXBvcnRFeHByKFIzLnNldE5nTW9kdWxlU2NvcGUpLFxuICAgIC8qIGFyZ3MgKi8gW21ldGEudHlwZS52YWx1ZSwgc2NvcGVNYXAudG9MaXRlcmFsTWFwKCldLFxuICApO1xuXG4gIC8vIChuZ0ppdE1vZGUgZ3VhcmQpICYmIHNldE5nTW9kdWxlU2NvcGUoLi4uKVxuICBjb25zdCBndWFyZGVkQ2FsbCA9IGppdE9ubHlHdWFyZGVkRXhwcmVzc2lvbihmbkNhbGwpO1xuXG4gIC8vIGZ1bmN0aW9uKCkgeyAobmdKaXRNb2RlIGd1YXJkKSAmJiBzZXROZ01vZHVsZVNjb3BlKC4uLik7IH1cbiAgY29uc3QgaWlmZSA9IG5ldyBvLkZ1bmN0aW9uRXhwcigvKiBwYXJhbXMgKi8gW10sIC8qIHN0YXRlbWVudHMgKi8gW2d1YXJkZWRDYWxsLnRvU3RtdCgpXSk7XG5cbiAgLy8gKGZ1bmN0aW9uKCkgeyAobmdKaXRNb2RlIGd1YXJkKSAmJiBzZXROZ01vZHVsZVNjb3BlKC4uLik7IH0pKClcbiAgY29uc3QgaWlmZUNhbGwgPSBuZXcgby5JbnZva2VGdW5jdGlvbkV4cHIoLyogZm4gKi8gaWlmZSwgLyogYXJncyAqLyBbXSk7XG5cbiAgcmV0dXJuIGlpZmVDYWxsLnRvU3RtdCgpO1xufVxuXG5mdW5jdGlvbiB0dXBsZVR5cGVPZihleHA6IFIzUmVmZXJlbmNlW10pOiBvLlR5cGUge1xuICBjb25zdCB0eXBlcyA9IGV4cC5tYXAoKHJlZikgPT4gby50eXBlb2ZFeHByKHJlZi50eXBlKSk7XG4gIHJldHVybiBleHAubGVuZ3RoID4gMCA/IG8uZXhwcmVzc2lvblR5cGUoby5saXRlcmFsQXJyKHR5cGVzKSkgOiBvLk5PTkVfVFlQRTtcbn1cblxuZnVuY3Rpb24gdHVwbGVPZlR5cGVzKHR5cGVzOiBvLkV4cHJlc3Npb25bXSk6IG8uVHlwZSB7XG4gIGNvbnN0IHR5cGVvZlR5cGVzID0gdHlwZXMubWFwKCh0eXBlKSA9PiBvLnR5cGVvZkV4cHIodHlwZSkpO1xuICByZXR1cm4gdHlwZXMubGVuZ3RoID4gMCA/IG8uZXhwcmVzc2lvblR5cGUoby5saXRlcmFsQXJyKHR5cGVvZlR5cGVzKSkgOiBvLk5PTkVfVFlQRTtcbn1cbiJdfQ==