/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export function applyValueToInputField(instance, inputSignalNode, privateName, value) {
    if (inputSignalNode !== null) {
        inputSignalNode.applyValueToInputSignal(inputSignalNode, value);
    }
    else {
        instance[privateName] = value;
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXBwbHlfdmFsdWVfaW5wdXRfZmllbGQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9yZW5kZXIzL2FwcGx5X3ZhbHVlX2lucHV0X2ZpZWxkLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUlILE1BQU0sVUFBVSxzQkFBc0IsQ0FDcEMsUUFBVyxFQUNYLGVBQXlELEVBQ3pELFdBQW1CLEVBQ25CLEtBQWM7SUFFZCxJQUFJLGVBQWUsS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUM3QixlQUFlLENBQUMsdUJBQXVCLENBQUMsZUFBZSxFQUFFLEtBQUssQ0FBQyxDQUFDO0lBQ2xFLENBQUM7U0FBTSxDQUFDO1FBQ0wsUUFBZ0IsQ0FBQyxXQUFXLENBQUMsR0FBRyxLQUFLLENBQUM7SUFDekMsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCB7SW5wdXRTaWduYWxOb2RlfSBmcm9tICcuLi9hdXRob3JpbmcvaW5wdXQvaW5wdXRfc2lnbmFsX25vZGUnO1xuXG5leHBvcnQgZnVuY3Rpb24gYXBwbHlWYWx1ZVRvSW5wdXRGaWVsZDxUPihcbiAgaW5zdGFuY2U6IFQsXG4gIGlucHV0U2lnbmFsTm9kZTogbnVsbCB8IElucHV0U2lnbmFsTm9kZTx1bmtub3duLCB1bmtub3duPixcbiAgcHJpdmF0ZU5hbWU6IHN0cmluZyxcbiAgdmFsdWU6IHVua25vd24sXG4pIHtcbiAgaWYgKGlucHV0U2lnbmFsTm9kZSAhPT0gbnVsbCkge1xuICAgIGlucHV0U2lnbmFsTm9kZS5hcHBseVZhbHVlVG9JbnB1dFNpZ25hbChpbnB1dFNpZ25hbE5vZGUsIHZhbHVlKTtcbiAgfSBlbHNlIHtcbiAgICAoaW5zdGFuY2UgYXMgYW55KVtwcml2YXRlTmFtZV0gPSB2YWx1ZTtcbiAgfVxufVxuIl19