@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, dialog);

/// Generates custom tokens for the dialog.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the dialog
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: (
    container-elevation-shadow: token-utils.hardcode(none, $exclude-hardcoded),
    container-max-width: token-utils.hardcode(560px, $exclude-hardcoded),
    container-small-max-width: token-utils.hardcode(calc(100vw - 32px), $exclude-hardcoded),
    container-min-width: token-utils.hardcode(280px, $exclude-hardcoded),
    actions-alignment: token-utils.hardcode(flex-end, $exclude-hardcoded),
    content-padding: token-utils.hardcode(20px 24px, $exclude-hardcoded),
    with-actions-content-padding: token-utils.hardcode(20px 24px 0, $exclude-hardcoded),
    actions-padding: token-utils.hardcode(16px 24px, $exclude-hardcoded),

    // The vertical padding values are a bit weird, because MDC uses a `::before` pseudo
    // element to size the title which in turn means that we can't set a specific padding
    // and get the exact same space out of it. These values were determined through
    // trial and error so that the first line of text is 24px from the top and the bottom
    // of the text is 16px from the content under it.
    headline-padding: token-utils.hardcode(6px 24px 13px, $exclude-hardcoded),
  );

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
