{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'json-pulse-app';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [RouterOutlet],\n      styles: [\"\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEseUJBQXlCIiwic291cmNlc0NvbnRlbnQiOlsiLyogQXBwIGNvbXBvbmVudCBzdHlsZXMgKi9cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "styles"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\app.component.ts", "C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'json-pulse-app';\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAS9C,OAAM,MAAOC,YAAY;EAPzBC,YAAA;IAQE,KAAAC,KAAK,GAAG,gBAAgB;;;;uCADbF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVzBN,EAAA,CAAAQ,SAAA,oBAA+B;;;qBDMnBf,YAAY;MAAAgB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}