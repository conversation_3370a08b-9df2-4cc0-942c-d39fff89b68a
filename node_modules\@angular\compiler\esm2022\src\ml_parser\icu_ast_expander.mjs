/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { ParseError } from '../parse_util';
import * as html from './ast';
// http://cldr.unicode.org/index/cldr-spec/plural-rules
const PLURAL_CASES = ['zero', 'one', 'two', 'few', 'many', 'other'];
/**
 * Expands special forms into elements.
 *
 * For example,
 *
 * ```
 * { messages.length, plural,
 *   =0 {zero}
 *   =1 {one}
 *   other {more than one}
 * }
 * ```
 *
 * will be expanded into
 *
 * ```
 * <ng-container [ngPlural]="messages.length">
 *   <ng-template ngPluralCase="=0">zero</ng-template>
 *   <ng-template ngPluralCase="=1">one</ng-template>
 *   <ng-template ngPluralCase="other">more than one</ng-template>
 * </ng-container>
 * ```
 */
export function expandNodes(nodes) {
    const expander = new _Expander();
    return new ExpansionResult(html.visitAll(expander, nodes), expander.isExpanded, expander.errors);
}
export class ExpansionResult {
    constructor(nodes, expanded, errors) {
        this.nodes = nodes;
        this.expanded = expanded;
        this.errors = errors;
    }
}
export class ExpansionError extends ParseError {
    constructor(span, errorMsg) {
        super(span, errorMsg);
    }
}
/**
 * Expand expansion forms (plural, select) to directives
 *
 * @internal
 */
class _Expander {
    constructor() {
        this.isExpanded = false;
        this.errors = [];
    }
    visitElement(element, context) {
        return new html.Element(element.name, element.attrs, html.visitAll(this, element.children), element.sourceSpan, element.startSourceSpan, element.endSourceSpan);
    }
    visitAttribute(attribute, context) {
        return attribute;
    }
    visitText(text, context) {
        return text;
    }
    visitComment(comment, context) {
        return comment;
    }
    visitExpansion(icu, context) {
        this.isExpanded = true;
        return icu.type === 'plural'
            ? _expandPluralForm(icu, this.errors)
            : _expandDefaultForm(icu, this.errors);
    }
    visitExpansionCase(icuCase, context) {
        throw new Error('Should not be reached');
    }
    visitBlock(block, context) {
        return new html.Block(block.name, block.parameters, html.visitAll(this, block.children), block.sourceSpan, block.nameSpan, block.startSourceSpan, block.endSourceSpan);
    }
    visitBlockParameter(parameter, context) {
        return parameter;
    }
    visitLetDeclaration(decl, context) {
        return decl;
    }
}
// Plural forms are expanded to `NgPlural` and `NgPluralCase`s
function _expandPluralForm(ast, errors) {
    const children = ast.cases.map((c) => {
        if (PLURAL_CASES.indexOf(c.value) === -1 && !c.value.match(/^=\d+$/)) {
            errors.push(new ExpansionError(c.valueSourceSpan, `Plural cases should be "=<number>" or one of ${PLURAL_CASES.join(', ')}`));
        }
        const expansionResult = expandNodes(c.expression);
        errors.push(...expansionResult.errors);
        return new html.Element(`ng-template`, [
            new html.Attribute('ngPluralCase', `${c.value}`, c.valueSourceSpan, undefined /* keySpan */, undefined /* valueSpan */, undefined /* valueTokens */, undefined /* i18n */),
        ], expansionResult.nodes, c.sourceSpan, c.sourceSpan, c.sourceSpan);
    });
    const switchAttr = new html.Attribute('[ngPlural]', ast.switchValue, ast.switchValueSourceSpan, undefined /* keySpan */, undefined /* valueSpan */, undefined /* valueTokens */, undefined /* i18n */);
    return new html.Element('ng-container', [switchAttr], children, ast.sourceSpan, ast.sourceSpan, ast.sourceSpan);
}
// ICU messages (excluding plural form) are expanded to `NgSwitch`  and `NgSwitchCase`s
function _expandDefaultForm(ast, errors) {
    const children = ast.cases.map((c) => {
        const expansionResult = expandNodes(c.expression);
        errors.push(...expansionResult.errors);
        if (c.value === 'other') {
            // other is the default case when no values match
            return new html.Element(`ng-template`, [
                new html.Attribute('ngSwitchDefault', '', c.valueSourceSpan, undefined /* keySpan */, undefined /* valueSpan */, undefined /* valueTokens */, undefined /* i18n */),
            ], expansionResult.nodes, c.sourceSpan, c.sourceSpan, c.sourceSpan);
        }
        return new html.Element(`ng-template`, [
            new html.Attribute('ngSwitchCase', `${c.value}`, c.valueSourceSpan, undefined /* keySpan */, undefined /* valueSpan */, undefined /* valueTokens */, undefined /* i18n */),
        ], expansionResult.nodes, c.sourceSpan, c.sourceSpan, c.sourceSpan);
    });
    const switchAttr = new html.Attribute('[ngSwitch]', ast.switchValue, ast.switchValueSourceSpan, undefined /* keySpan */, undefined /* valueSpan */, undefined /* valueTokens */, undefined /* i18n */);
    return new html.Element('ng-container', [switchAttr], children, ast.sourceSpan, ast.sourceSpan, ast.sourceSpan);
}
//# sourceMappingURL=data:application/json;base64,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