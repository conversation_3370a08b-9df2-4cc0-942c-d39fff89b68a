/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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