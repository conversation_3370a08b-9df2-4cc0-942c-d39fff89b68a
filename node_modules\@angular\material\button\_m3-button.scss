@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';
@use '../core/style/elevation';
@use '../core/theming/theming';
@use '../core/theming/inspection';


/// Generates custom tokens for the button.
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  @if $color-variant {
    $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
    $system: m3-utils.replace-colors-with-variant($system, secondary, $color-variant);
  }

  @return (
    base: (
      button-filled-container-shape: map.get($system, corner-full),
      button-filled-horizontal-padding: 24px,
      button-filled-icon-offset: -8px,
      button-filled-icon-spacing: 8px,
      button-filled-label-text-transform: null,
      button-outlined-container-shape: map.get($system, corner-full),
      button-outlined-horizontal-padding: 24px,
      button-outlined-icon-offset: -8px,
      button-outlined-icon-spacing: 8px,
      button-outlined-outline-width: 1px,
      button-outlined-label-text-transform: null,
      button-protected-container-shape: map.get($system, corner-full),
      button-protected-horizontal-padding: 24px,
      button-protected-icon-offset: -8px,
      button-protected-icon-spacing: 8px,
      button-protected-label-text-transform: null,
      button-text-container-shape: map.get($system, corner-full),
      button-text-horizontal-padding: 12px,
      button-text-icon-offset: -4px,
      button-text-icon-spacing: 8px,
      button-text-with-icon-horizontal-padding: 16px,
      button-text-label-text-transform: null,
      button-tonal-container-shape: map.get($system, corner-full),
      button-tonal-horizontal-padding: 24px,
      button-tonal-icon-offset: -8px,
      button-tonal-icon-spacing: 8px,
      button-tonal-label-text-transform: null,
    ),
    color: (
      button-filled-container-color: map.get($system, primary),
      button-filled-disabled-container-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 12%),
      button-filled-disabled-label-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      button-filled-disabled-state-layer-color: map.get($system, on-surface-variant),
      button-filled-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      button-filled-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      button-filled-label-text-color: map.get($system, on-primary),
      button-filled-pressed-state-layer-opacity:map.get($system, pressed-state-layer-opacity),
      button-filled-ripple-color: m3-utils.color-with-opacity(
          map.get($system, on-primary), map.get($system, pressed-state-layer-opacity)),
      button-filled-state-layer-color: map.get($system, on-primary),
      button-outlined-disabled-label-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      button-outlined-disabled-outline-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 12%),
      button-outlined-disabled-state-layer-color: map.get($system, on-surface-variant),
      button-outlined-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      button-outlined-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      button-outlined-label-text-color: map.get($system, primary),
      button-outlined-outline-color: map.get($system, outline),
      button-outlined-pressed-state-layer-opacity:map.get($system, pressed-state-layer-opacity),
      button-outlined-ripple-color: m3-utils.color-with-opacity(
          map.get($system, primary), map.get($system, pressed-state-layer-opacity)),
      button-outlined-state-layer-color: map.get($system, primary),
      button-protected-container-color: map.get($system, surface),
      button-protected-container-elevation-shadow:
          elevation.get-box-shadow(map.get($system, level1)),
      button-protected-disabled-container-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 12%),
      button-protected-disabled-container-elevation-shadow:
          elevation.get-box-shadow(map.get($system, level0)),
      button-protected-disabled-label-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      button-protected-disabled-state-layer-color: map.get($system, on-surface-variant),
      button-protected-focus-container-elevation-shadow:
          elevation.get-box-shadow(map.get($system, level1)),
      button-protected-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      button-protected-hover-container-elevation-shadow:
          elevation.get-box-shadow(map.get($system, level2)),
      button-protected-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      button-protected-label-text-color: map.get($system, primary),
      button-protected-pressed-container-elevation-shadow:
          elevation.get-box-shadow(map.get($system, level1)),
      button-protected-pressed-state-layer-opacity:map.get($system, pressed-state-layer-opacity),
      button-protected-ripple-color: m3-utils.color-with-opacity(
          map.get($system, primary), map.get($system, pressed-state-layer-opacity)),
      button-protected-state-layer-color: map.get($system, primary),
      button-text-disabled-label-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      button-text-disabled-state-layer-color: map.get($system, on-surface-variant),
      button-text-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      button-text-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      button-text-label-text-color: map.get($system, primary),
      button-text-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      button-text-ripple-color: m3-utils.color-with-opacity(
          map.get($system, primary), map.get($system, pressed-state-layer-opacity)),
      button-text-state-layer-color: map.get($system, primary),
      button-tonal-container-color: map.get($system, secondary-container),
      button-tonal-disabled-container-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 12%),
      button-tonal-disabled-label-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      button-tonal-disabled-state-layer-color: map.get($system, on-surface-variant),
      button-tonal-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      button-tonal-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      button-tonal-label-text-color: map.get($system, on-secondary-container),
      button-tonal-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      button-tonal-ripple-color: m3-utils.color-with-opacity(
          map.get($system, on-secondary-container), map.get($system, pressed-state-layer-opacity)),
      button-tonal-state-layer-color: map.get($system, on-secondary-container),
    ),
    typography: (
      button-filled-label-text-font: map.get($system, label-large-font),
      button-filled-label-text-size: map.get($system, label-large-size),
      button-filled-label-text-tracking: map.get($system, label-large-tracking),
      button-filled-label-text-weight: map.get($system, label-large-weight),
      button-outlined-label-text-font: map.get($system, label-large-font),
      button-outlined-label-text-size: map.get($system, label-large-size),
      button-outlined-label-text-tracking: map.get($system, label-large-tracking),
      button-outlined-label-text-weight: map.get($system, label-large-weight),
      button-protected-label-text-font: map.get($system, label-large-font),
      button-protected-label-text-size: map.get($system, label-large-size),
      button-protected-label-text-tracking: map.get($system, label-large-tracking),
      button-protected-label-text-weight: map.get($system, label-large-weight),
      button-text-label-text-font: map.get($system, label-large-font),
      button-text-label-text-size: map.get($system, label-large-size),
      button-text-label-text-tracking: map.get($system, label-large-tracking),
      button-text-label-text-weight: map.get($system, label-large-weight),
      button-tonal-label-text-font: map.get($system, label-large-font),
      button-tonal-label-text-size: map.get($system, label-large-size),
      button-tonal-label-text-tracking: map.get($system, label-large-tracking),
      button-tonal-label-text-weight: map.get($system, label-large-weight),
    ),
    density: get-density-tokens(map.get($theme, inspection.$internals, density-scale)),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -3);
  $index: ($scale * -1) + 1;

  @return (
    button-filled-touch-target-display: list.nth((block, block, none, none), $index),
    button-filled-container-height: list.nth((40px, 36px, 32px, 28px), $index),
    button-outlined-container-height: list.nth((40px, 36px, 32px, 28px), $index),
    button-outlined-touch-target-display: list.nth((block, block, none, none), $index),
    button-protected-touch-target-display: list.nth((block, block, none, none), $index),
    button-protected-container-height: list.nth((40px, 36px, 32px, 28px), $index),
    button-text-touch-target-display: list.nth((block, block, none, none), $index),
    button-text-container-height: list.nth((40px, 36px, 32px, 28px), $index),
    button-tonal-container-height: list.nth((40px, 36px, 32px, 28px), $index),
    button-tonal-touch-target-display: list.nth((block, block, none, none), $index),
  );
}
