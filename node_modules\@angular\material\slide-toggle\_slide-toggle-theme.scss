@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use './m2-slide-toggle';
@use './m3-slide-toggle';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-slide-toggle.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      // TODO: See if this can be removed
      @include token-utils.create-token-values-mixed(m2-slide-toggle.get-unthemable-tokens());

      .mat-mdc-slide-toggle {
        @include token-utils.create-token-values-mixed(m2-slide-toggle.get-unthemable-tokens());
      }
    }
  }
}

/// Outputs color theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-slide-toggle.get-tokens($theme, $color-variant), color));
  } @else {
    $mat-tokens: m2-slide-toggle.get-color-tokens($theme);

    // Add values for MDC slide toggles tokens
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-slide-toggle.get-color-tokens($theme));

      & {
        // TODO(andrewjs): Remove this once all tokens are migrated to
        // mat internally.
        --mdc-slide-toggle-disabled-label-text-color: #{inspection.get-theme-color(
            $theme,
            foreground,
            disabled-text
          )};
        // TODO(wagnermaciel): Use our token system to define this css variable.
        --mat-slide-toggle-disabled-label-text-color: #{inspection.get-theme-color(
            $theme,
            foreground,
            disabled-text
          )};
      }

      .mat-mdc-slide-toggle {
        // TODO(wagnermaciel): Use our token system to define this css variable.
        --mat-slide-toggle-label-text-color: #{inspection.get-theme-color(
          $theme,
          foreground,
          text
        )};

        // Change the color palette related tokens to accent or warn if applicable
        &.mat-accent {
          @include token-utils.create-token-values-mixed(
              m2-slide-toggle.private-get-color-palette-color-tokens($theme, accent));
        }

        &.mat-warn {
          @include token-utils.create-token-values-mixed(
              m2-slide-toggle.private-get-color-palette-color-tokens($theme, warn));
        }
      }
    }
  }
}

/// Outputs typography theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-slide-toggle.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      // TODO: See if this can be removed
      @include token-utils.create-token-values-mixed(m2-slide-toggle.get-typography-tokens($theme));

      .mat-mdc-slide-toggle {
        @include token-utils.create-token-values-mixed(
            m2-slide-toggle.get-typography-tokens($theme));
      }
    }
  }
}

/// Outputs density theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    // There are no M3 density tokens for this component
  } @else {
    @include sass-utils.current-selector-or-root() {
      // TODO: See if this can be removed
      @include token-utils.create-token-values-mixed(m2-slide-toggle.get-density-tokens($theme));

      .mat-mdc-slide-toggle {
        @include token-utils.create-token-values-mixed(m2-slide-toggle.get-density-tokens($theme));
      }
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: slide-toggle,
      tokens: token-utils.get-overrides(m3-slide-toggle.get-tokens(), slide-toggle)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-slide-toggle') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
