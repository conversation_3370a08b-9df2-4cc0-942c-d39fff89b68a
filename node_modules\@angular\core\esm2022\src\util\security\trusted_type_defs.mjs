/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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