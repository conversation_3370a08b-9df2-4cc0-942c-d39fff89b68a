{"ast": null, "code": "import { i as isDataSource } from './data-source-D34wiQZj.mjs';\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction CdkTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction CdkTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 0);\n    i0.ɵɵelementContainer(3, 2)(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CdkTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 2)(2, 3)(3, 4);\n  }\n}\nfunction CdkTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction CdkTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nexport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, booleanAttribute, Input, ContentChild, ElementRef, IterableDiffers, ViewContainerRef, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ChangeDetectorRef, DOCUMENT, EventEmitter, Injector, HostAttributeToken, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { b as _VIEW_REPEATER_STRATEGY, _ as _RecycleViewRepeaterStrategy, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport '@angular/common';\nimport './element-x4z00URv.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCellDef,\n    selectors: [[\"\", \"cdkCellDef\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkHeaderCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderCellDef,\n    selectors: [[\"\", \"cdkHeaderCellDef\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n  /** @docs-private */\n  template = inject(TemplateRef);\n  constructor() {}\n  static ɵfac = function CdkFooterCellDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterCellDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterCellDef,\n    selectors: [[\"\", \"cdkFooterCellDef\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterCellDef]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  _name;\n  /** Whether the cell is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  get stickyEnd() {\n    return this._stickyEnd;\n  }\n  set stickyEnd(value) {\n    if (value !== this._stickyEnd) {\n      this._stickyEnd = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _stickyEnd = false;\n  /** @docs-private */\n  cell;\n  /** @docs-private */\n  headerCell;\n  /** @docs-private */\n  footerCell;\n  /**\n   * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n   * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n   * do not match are replaced by the '-' character.\n   */\n  cssClassFriendlyName;\n  /**\n   * Class name for cells in this column.\n   * @docs-private\n   */\n  _columnCssClassName;\n  constructor() {}\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  _setNameInput(value) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n  static ɵfac = function CdkColumnDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkColumnDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkColumnDef,\n    selectors: [[\"\", \"cdkColumnDef\", \"\"]],\n    contentQueries: function CdkColumnDef_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkCellDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkHeaderCellDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkFooterCellDef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerCell = _t.first);\n      }\n    },\n    inputs: {\n      name: [0, \"cdkColumnDef\", \"name\"],\n      sticky: [2, \"sticky\", \"sticky\", booleanAttribute],\n      stickyEnd: [2, \"stickyEnd\", \"stickyEnd\", booleanAttribute]\n    },\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([{\n      provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n      useExisting: CdkColumnDef\n    }]), i0.ɵɵInputTransformsFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkColumnDef]',\n      providers: [{\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: CdkColumnDef\n      }]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input,\n      args: ['cdkColumnDef']\n    }],\n    sticky: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stickyEnd: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cell: [{\n      type: ContentChild,\n      args: [CdkCellDef]\n    }],\n    headerCell: [{\n      type: ContentChild,\n      args: [CdkHeaderCellDef]\n    }],\n    footerCell: [{\n      type: ContentChild,\n      args: [CdkFooterCellDef]\n    }]\n  });\n})();\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n  constructor(columnDef, elementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n  constructor() {\n    super(inject(CdkColumnDef), inject(ElementRef));\n  }\n  static ɵfac = function CdkHeaderCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderCell,\n    selectors: [[\"cdk-header-cell\"], [\"th\", \"cdk-header-cell\", \"\"]],\n    hostAttrs: [\"role\", \"columnheader\", 1, \"cdk-header-cell\"],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-header-cell, th[cdk-header-cell]',\n      host: {\n        'class': 'cdk-header-cell',\n        'role': 'columnheader'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static ɵfac = function CdkFooterCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterCell,\n    selectors: [[\"cdk-footer-cell\"], [\"td\", \"cdk-footer-cell\", \"\"]],\n    hostAttrs: [1, \"cdk-footer-cell\"],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n      host: {\n        'class': 'cdk-footer-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n    super(columnDef, elementRef);\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n  static ɵfac = function CdkCell_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCell)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCell,\n    selectors: [[\"cdk-cell\"], [\"td\", \"cdk-cell\", \"\"]],\n    hostAttrs: [1, \"cdk-cell\"],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCell, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-cell, td[cdk-cell]',\n      host: {\n        'class': 'cdk-cell'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n  template = inject(TemplateRef);\n  _differs = inject(IterableDiffers);\n  /** The columns to be displayed on this row. */\n  columns;\n  /** Differ used to check if any changes were made to the columns. */\n  _columnsDiffer;\n  constructor() {}\n  ngOnChanges(changes) {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = changes['columns'] && changes['columns'].currentValue || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff() {\n    return this._columnsDiffer.diff(this.columns);\n  }\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column) {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n  static ɵfac = function BaseRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseRowDef,\n    standalone: true,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseRowDef, [{\n    type: Directive\n  }], () => [], null);\n})();\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static ɵfac = function CdkHeaderRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkHeaderRowDef,\n    selectors: [[\"\", \"cdkHeaderRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkHeaderRowDef\", \"columns\"],\n      sticky: [2, \"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkHeaderRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkHeaderRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkHeaderRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  _hasStickyChanged = false;\n  /** Whether the row is sticky. */\n  get sticky() {\n    return this._sticky;\n  }\n  set sticky(value) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _sticky = false;\n  constructor() {\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  ngOnChanges(changes) {\n    super.ngOnChanges(changes);\n  }\n  /** Whether the sticky state has changed. */\n  hasStickyChanged() {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n  /** Resets the sticky changed state. */\n  resetStickyChanged() {\n    this._hasStickyChanged = false;\n  }\n  static ɵfac = function CdkFooterRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkFooterRowDef,\n    selectors: [[\"\", \"cdkFooterRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkFooterRowDef\", \"columns\"],\n      sticky: [2, \"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute]\n    },\n    standalone: true,\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkFooterRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkFooterRowDef'\n      }]\n    }]\n  }], () => [], {\n    sticky: [{\n      type: Input,\n      args: [{\n        alias: 'cdkFooterRowDefSticky',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n  _table = inject(CDK_TABLE, {\n    optional: true\n  });\n  /**\n   * Function that should return true if this row template should be used for the provided index\n   * and row data. If left undefined, this row will be considered the default row template to use\n   * when no other when functions return true for the data.\n   * For every row, there must be at least one when function that passes or an undefined to default.\n   */\n  when;\n  constructor() {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    super(inject(TemplateRef), inject(IterableDiffers));\n  }\n  static ɵfac = function CdkRowDef_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRowDef)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkRowDef,\n    selectors: [[\"\", \"cdkRowDef\", \"\"]],\n    inputs: {\n      columns: [0, \"cdkRowDefColumns\", \"columns\"],\n      when: [0, \"cdkRowDefWhen\", \"when\"]\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkRowDef]',\n      inputs: [{\n        name: 'columns',\n        alias: 'cdkRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'cdkRowDefWhen'\n      }]\n    }]\n  }], () => [], null);\n})();\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n  _viewContainer = inject(ViewContainerRef);\n  /** The ordered list of cells to render within this outlet's view container */\n  cells;\n  /** The data context to be provided to each cell */\n  context;\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static mostRecentCellOutlet = null;\n  constructor() {\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n  static ɵfac = function CdkCellOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCellOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCellOutlet,\n    selectors: [[\"\", \"cdkCellOutlet\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCellOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCellOutlet]'\n    }]\n  }], () => [], null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n  static ɵfac = function CdkHeaderRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkHeaderRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkHeaderRow,\n    selectors: [[\"cdk-header-row\"], [\"tr\", \"cdk-header-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-header-row\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkHeaderRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-header-row, tr[cdk-header-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n  static ɵfac = function CdkFooterRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkFooterRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkFooterRow,\n    selectors: [[\"cdk-footer-row\"], [\"tr\", \"cdk-footer-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-footer-row\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkFooterRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-footer-row, tr[cdk-footer-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-footer-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n  static ɵfac = function CdkRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkRow,\n    selectors: [[\"cdk-row\"], [\"tr\", \"cdk-row\", \"\"]],\n    hostAttrs: [\"role\", \"row\", 1, \"cdk-row\"],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkCellOutlet\", \"\"]],\n    template: function CdkRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainer(0, 0);\n      }\n    },\n    dependencies: [CdkCellOutlet],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRow, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-row, tr[cdk-row]',\n      template: CDK_ROW_TEMPLATE,\n      host: {\n        'class': 'cdk-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n  templateRef = inject(TemplateRef);\n  _contentClassName = 'cdk-no-data-row';\n  constructor() {}\n  static ɵfac = function CdkNoDataRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkNoDataRow)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkNoDataRow,\n    selectors: [[\"ng-template\", \"cdkNoDataRow\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkNoDataRow]'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n  _isNativeHtmlTable;\n  _stickCellCss;\n  _isBrowser;\n  _needsPositionStickyOnElement;\n  direction;\n  _positionListener;\n  _tableInjector;\n  _elemSizeCache = new WeakMap();\n  _resizeObserver = globalThis?.ResizeObserver ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries)) : null;\n  _updatedStickyColumnsParamsToReplay = [];\n  _stickyColumnsReplayTimeout = null;\n  _cachedCellWidths = [];\n  _borderCellCss;\n  _destroyed = false;\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   * @param _tableInjector The table's Injector.\n   */\n  constructor(_isNativeHtmlTable, _stickCellCss, _isBrowser = true, _needsPositionStickyOnElement = true, direction, _positionListener, _tableInjector) {\n    this._isNativeHtmlTable = _isNativeHtmlTable;\n    this._stickCellCss = _stickCellCss;\n    this._isBrowser = _isBrowser;\n    this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n    this.direction = direction;\n    this._positionListener = _positionListener;\n    this._tableInjector = _tableInjector;\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`\n    };\n  }\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows, stickyDirections) {\n    if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n      this._removeFromStickyColumnReplayQueue(rows);\n    }\n    const elementsToClear = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n      elementsToClear.push(row, ...Array.from(row.children));\n    }\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    afterNextRender({\n      write: () => {\n        for (const element of elementsToClear) {\n          this._removeStickyStyle(element, stickyDirections);\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n   */\n  updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true, replay = true) {\n    // Don't cache any state if none of the columns are sticky.\n    if (!rows.length || !this._isBrowser || !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n      this._positionListener?.stickyColumnsUpdated({\n        sizes: []\n      });\n      this._positionListener?.stickyEndColumnsUpdated({\n        sizes: []\n      });\n      return;\n    }\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    const firstRow = rows[0];\n    const numCells = firstRow.children.length;\n    const isRtl = this.direction === 'rtl';\n    const start = isRtl ? 'right' : 'left';\n    const end = isRtl ? 'left' : 'right';\n    const lastStickyStart = stickyStartStates.lastIndexOf(true);\n    const firstStickyEnd = stickyEndStates.indexOf(true);\n    let cellWidths;\n    let startPositions;\n    let endPositions;\n    if (replay) {\n      this._updateStickyColumnReplayQueue({\n        rows: [...rows],\n        stickyStartStates: [...stickyStartStates],\n        stickyEndStates: [...stickyEndStates]\n      });\n    }\n    afterNextRender({\n      earlyRead: () => {\n        cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n        startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n        endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      },\n      write: () => {\n        for (const row of rows) {\n          for (let i = 0; i < numCells; i++) {\n            const cell = row.children[i];\n            if (stickyStartStates[i]) {\n              this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n            }\n            if (stickyEndStates[i]) {\n              this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n            }\n          }\n        }\n        if (this._positionListener && cellWidths.some(w => !!w)) {\n          this._positionListener.stickyColumnsUpdated({\n            sizes: lastStickyStart === -1 ? [] : cellWidths.slice(0, lastStickyStart + 1).map((width, index) => stickyStartStates[index] ? width : null)\n          });\n          this._positionListener.stickyEndColumnsUpdated({\n            sizes: firstStickyEnd === -1 ? [] : cellWidths.slice(firstStickyEnd).map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null).reverse()\n          });\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick, stickyStates, position) {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n    // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n    // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n    // sticky states need to be reversed as well.\n    const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n    const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n    // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n    const stickyOffsets = [];\n    const stickyCellHeights = [];\n    const elementsToStick = [];\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    afterNextRender({\n      earlyRead: () => {\n        for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          stickyOffsets[rowIndex] = stickyOffset;\n          const row = rows[rowIndex];\n          elementsToStick[rowIndex] = this._isNativeHtmlTable ? Array.from(row.children) : [row];\n          const height = this._retrieveElementSize(row).height;\n          stickyOffset += height;\n          stickyCellHeights[rowIndex] = height;\n        }\n      },\n      write: () => {\n        const borderedRowIndex = states.lastIndexOf(true);\n        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n          const offset = stickyOffsets[rowIndex];\n          const isBorderedRowIndex = rowIndex === borderedRowIndex;\n          for (const element of elementsToStick[rowIndex]) {\n            this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n          }\n        }\n        if (position === 'top') {\n          this._positionListener?.stickyHeaderRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        } else {\n          this._positionListener?.stickyFooterRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick\n          });\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement, stickyStates) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    afterNextRender({\n      write: () => {\n        const tfoot = tableElement.querySelector('tfoot');\n        if (tfoot) {\n          if (stickyStates.some(state => !state)) {\n            this._removeStickyStyle(tfoot, ['bottom']);\n          } else {\n            this._addStickyStyle(tfoot, 'bottom', 0, false);\n          }\n        }\n      }\n    }, {\n      injector: this._tableInjector\n    });\n  }\n  /** Triggered by the table's OnDestroy hook. */\n  destroy() {\n    if (this._stickyColumnsReplayTimeout) {\n      clearTimeout(this._stickyColumnsReplayTimeout);\n    }\n    this._resizeObserver?.disconnect();\n    this._destroyed = true;\n  }\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element, stickyDirections) {\n    if (!element.classList.contains(this._stickCellCss)) {\n      return;\n    }\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(element, dir, dirValue, isBorderElement) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element) {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1\n    };\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n    return zIndex ? `${zIndex}` : '';\n  }\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row, recalculateCellWidths = true) {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n    const cellWidths = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      const cell = firstRowCells[i];\n      cellWidths.push(this._retrieveElementSize(cell).width);\n    }\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths, stickyStates) {\n    const positions = [];\n    let nextPosition = 0;\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n    return positions;\n  }\n  /**\n   * Retreives the most recently observed size of the specified element from the cache, or\n   * meaures it directly if not yet cached.\n   */\n  _retrieveElementSize(element) {\n    const cachedSize = this._elemSizeCache.get(element);\n    if (cachedSize) {\n      return cachedSize;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const size = {\n      width: clientRect.width,\n      height: clientRect.height\n    };\n    if (!this._resizeObserver) {\n      return size;\n    }\n    this._elemSizeCache.set(element, size);\n    this._resizeObserver.observe(element, {\n      box: 'border-box'\n    });\n    return size;\n  }\n  /**\n   * Conditionally enqueue the requested sticky update and clear previously queued updates\n   * for the same rows.\n   */\n  _updateStickyColumnReplayQueue(params) {\n    this._removeFromStickyColumnReplayQueue(params.rows);\n    // No need to replay if a flush is pending.\n    if (!this._stickyColumnsReplayTimeout) {\n      this._updatedStickyColumnsParamsToReplay.push(params);\n    }\n  }\n  /** Remove updates for the specified rows from the queue. */\n  _removeFromStickyColumnReplayQueue(rows) {\n    const rowsSet = new Set(rows);\n    for (const update of this._updatedStickyColumnsParamsToReplay) {\n      update.rows = update.rows.filter(row => !rowsSet.has(row));\n    }\n    this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(update => !!update.rows.length);\n  }\n  /** Update _elemSizeCache with the observed sizes. */\n  _updateCachedSizes(entries) {\n    let needsColumnUpdate = false;\n    for (const entry of entries) {\n      const newEntry = entry.borderBoxSize?.length ? {\n        width: entry.borderBoxSize[0].inlineSize,\n        height: entry.borderBoxSize[0].blockSize\n      } : {\n        width: entry.contentRect.width,\n        height: entry.contentRect.height\n      };\n      if (newEntry.width !== this._elemSizeCache.get(entry.target)?.width && isCell(entry.target)) {\n        needsColumnUpdate = true;\n      }\n      this._elemSizeCache.set(entry.target, newEntry);\n    }\n    if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n      if (this._stickyColumnsReplayTimeout) {\n        clearTimeout(this._stickyColumnsReplayTimeout);\n      }\n      this._stickyColumnsReplayTimeout = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n          this.updateStickyColumns(update.rows, update.stickyStartStates, update.stickyEndStates, true, false);\n        }\n        this._updatedStickyColumnsParamsToReplay = [];\n        this._stickyColumnsReplayTimeout = null;\n      }, 0);\n    }\n  }\n}\nfunction isCell(element) {\n  return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass => element.classList.contains(klass));\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n  return Error(`Could not find a matching row definition for the` + `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n  return Error('Missing definitions for header, footer, and row; ' + 'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n  static ɵfac = function CdkRecycleRows_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkRecycleRows)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkRecycleRows,\n    selectors: [[\"cdk-table\", \"recycleRows\", \"\"], [\"table\", \"cdk-table\", \"\", \"recycleRows\", \"\"]],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([{\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _RecycleViewRepeaterStrategy\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._rowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function DataRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DataRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DataRowOutlet,\n    selectors: [[\"\", \"rowOutlet\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[rowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._headerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function HeaderRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HeaderRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: HeaderRowOutlet,\n    selectors: [[\"\", \"headerRowOutlet\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HeaderRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[headerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._footerRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function FooterRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FooterRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FooterRowOutlet,\n    selectors: [[\"\", \"footerRowOutlet\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FooterRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[footerRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n  constructor() {\n    const table = inject(CDK_TABLE);\n    table._noDataRowOutlet = this;\n    table._outletAssigned();\n  }\n  static ɵfac = function NoDataRowOutlet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoDataRowOutlet)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NoDataRowOutlet,\n    selectors: [[\"\", \"noDataRowOutlet\", \"\"]],\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoDataRowOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[noDataRowOutlet]'\n    }]\n  }], () => [], null);\n})();\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n  _differs = inject(IterableDiffers);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n  _viewportRuler = inject(ViewportRuler);\n  _stickyPositioningListener = inject(STICKY_POSITIONING_LISTENER, {\n    optional: true,\n    skipSelf: true\n  });\n  _document = inject(DOCUMENT);\n  /** Latest data provided by the data source. */\n  _data;\n  /** Subject that emits when the component has been destroyed. */\n  _onDestroy = new Subject();\n  /** List of the rendered rows as identified by their `RenderRow` object. */\n  _renderRows;\n  /** Subscription that listens for the data provided by the data source. */\n  _renderChangeSubscription;\n  /**\n   * Map of all the user's defined columns (header, data, and footer cell template) identified by\n   * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n   * any custom column definitions added to `_customColumnDefs`.\n   */\n  _columnDefsByName = new Map();\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n   */\n  _rowDefs;\n  /**\n   * Set of all header row definitions that can be used by this table. Populated by the rows\n   * gathered by using `ContentChildren` as well as any custom row definitions added to\n   * `_customHeaderRowDefs`.\n   */\n  _headerRowDefs;\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to\n   * `_customFooterRowDefs`.\n   */\n  _footerRowDefs;\n  /** Differ used to find the changes in the data provided by the data source. */\n  _dataDiffer;\n  /** Stores the row definition that does not have a when predicate. */\n  _defaultRowDef;\n  /**\n   * Column definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * column definitions as *its* content child.\n   */\n  _customColumnDefs = new Set();\n  /**\n   * Data row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in data rows as *its* content child.\n   */\n  _customRowDefs = new Set();\n  /**\n   * Header row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in header rows as *its* content child.\n   */\n  _customHeaderRowDefs = new Set();\n  /**\n   * Footer row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n   * built-in footer row as *its* content child.\n   */\n  _customFooterRowDefs = new Set();\n  /** No data row that was defined outside of the direct content children of the table. */\n  _customNoDataRow;\n  /**\n   * Whether the header row definition has been changed. Triggers an update to the header row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  _headerRowDefChanged = true;\n  /**\n   * Whether the footer row definition has been changed. Triggers an update to the footer row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  _footerRowDefChanged = true;\n  /**\n   * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n   * change.\n   */\n  _stickyColumnStylesNeedReset = true;\n  /**\n   * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n   * `false`, cached values will be used instead. This is only applicable to tables with\n   * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n   */\n  _forceRecalculateCellWidths = true;\n  /**\n   * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n   * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n   * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n   * and row template matches, which allows the `IterableDiffer` to check rows by reference\n   * and understand which rows are added/moved/removed.\n   *\n   * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n   * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n   * contains an array of created pairs. The array is necessary to handle cases where the data\n   * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n   * stored.\n   */\n  _cachedRenderRowsMap = new Map();\n  /** Whether the table is applied to a native `<table>`. */\n  _isNativeHtmlTable;\n  /**\n   * Utility class that is responsible for applying the appropriate sticky positioning styles to\n   * the table's rows and cells.\n   */\n  _stickyStyler;\n  /**\n   * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n   * table subclasses.\n   */\n  stickyCssClass = 'cdk-table-sticky';\n  /**\n   * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n   * the position is set in a selector associated with the value of stickyCssClass. May be\n   * overridden by table subclasses\n   */\n  needsPositionStickyOnElement = true;\n  /** Whether the component is being rendered on the server. */\n  _isServer;\n  /** Whether the no data row is currently showing anything. */\n  _isShowingNoDataRow = false;\n  /** Whether the table has rendered out all the outlets for the first time. */\n  _hasAllOutlets = false;\n  /** Whether the table is done initializing. */\n  _hasInitialized = false;\n  /** Aria role to apply to the table's cells based on the table's own role. */\n  _getCellRole() {\n    // Perform this lazily in case the table's role was updated by a directive after construction.\n    if (this._cellRoleInternal === undefined) {\n      // Note that we set `role=\"cell\"` even on native `td` elements,\n      // because some browsers seem to require it. See #29784.\n      const tableRole = this._elementRef.nativeElement.getAttribute('role');\n      return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n    }\n    return this._cellRoleInternal;\n  }\n  _cellRoleInternal = undefined;\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  get trackBy() {\n    return this._trackByFn;\n  }\n  set trackBy(fn) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  _trackByFn;\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  get dataSource() {\n    return this._dataSource;\n  }\n  set dataSource(dataSource) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  _dataSource;\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  get multiTemplateDataRows() {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(value) {\n    this._multiTemplateDataRows = value;\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  _multiTemplateDataRows = false;\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  get fixedLayout() {\n    return this._fixedLayout;\n  }\n  set fixedLayout(value) {\n    this._fixedLayout = value;\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  _fixedLayout = false;\n  /**\n   * Emits when the table completes rendering a set of data rows based on the latest data from the\n   * data source, even if the set of rows is empty.\n   */\n  contentChanged = new EventEmitter();\n  // TODO(andrewseguin): Remove max value as the end index\n  //   and instead calculate the view on init and scroll.\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   *\n   * @docs-private\n   */\n  viewChange = new BehaviorSubject({\n    start: 0,\n    end: Number.MAX_VALUE\n  });\n  // Outlets in the table's template where the header, data rows, and footer will be inserted.\n  _rowOutlet;\n  _headerRowOutlet;\n  _footerRowOutlet;\n  _noDataRowOutlet;\n  /**\n   * The column definitions provided by the user that contain what the header, data, and footer\n   * cells should render for each column.\n   */\n  _contentColumnDefs;\n  /** Set of data row definitions that were provided to the table as content children. */\n  _contentRowDefs;\n  /** Set of header row definitions that were provided to the table as content children. */\n  _contentHeaderRowDefs;\n  /** Set of footer row definitions that were provided to the table as content children. */\n  _contentFooterRowDefs;\n  /** Row definition that will only be rendered if there's no data in the table. */\n  _noDataRow;\n  _injector = inject(Injector);\n  constructor() {\n    const role = inject(new HostAttributeToken('role'), {\n      optional: true\n    });\n    if (!role) {\n      this._elementRef.nativeElement.setAttribute('role', 'table');\n    }\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n  }\n  ngOnInit() {\n    this._setupStickyStyler();\n    this._viewportRuler.change().pipe(takeUntil(this._onDestroy)).subscribe(() => {\n      this._forceRecalculateCellWidths = true;\n    });\n  }\n  ngAfterContentInit() {\n    this._hasInitialized = true;\n  }\n  ngAfterContentChecked() {\n    // Only start re-rendering in `ngAfterContentChecked` after the first render.\n    if (this._canRender()) {\n      this._render();\n    }\n  }\n  ngOnDestroy() {\n    this._stickyStyler?.destroy();\n    [this._rowOutlet?.viewContainer, this._headerRowOutlet?.viewContainer, this._footerRowOutlet?.viewContainer, this._cachedRenderRowsMap, this._customColumnDefs, this._customRowDefs, this._customHeaderRowDefs, this._customFooterRowDefs, this._columnDefsByName].forEach(def => {\n      def?.clear();\n    });\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n    this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, change => {\n      if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n        this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n      }\n    });\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange(record => {\n      const rowView = viewContainer.get(record.currentIndex);\n      rowView.context.$implicit = record.item.data;\n    });\n    this._updateNoDataRow();\n    this.contentChanged.next();\n    this.updateStickyColumnStyles();\n  }\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef) {\n    this._customRowDefs.add(rowDef);\n  }\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef) {\n    this._customRowDefs.delete(rowDef);\n  }\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow) {\n    this._customNoDataRow = noDataRow;\n  }\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const thead = closestTableSection(this._headerRowOutlet, 'thead');\n      if (thead) {\n        thead.style.display = headerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles() {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n      if (tfoot) {\n        tfoot.style.display = footerRows.length ? '' : 'none';\n      }\n    }\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if (this._isNativeHtmlTable && !this._fixedLayout || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n      this._stickyColumnStylesNeedReset = false;\n    }\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n  /** Invoked whenever an outlet is created and has been assigned to the table. */\n  _outletAssigned() {\n    // Trigger the first render once all outlets have been assigned. We do it this way, as\n    // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n    // the next change detection will happen.\n    // Also we can't use queries to resolve the outlets, because they're wrapped in a\n    // conditional, so we have to rely on them being assigned via DI.\n    if (!this._hasAllOutlets && this._rowOutlet && this._headerRowOutlet && this._footerRowOutlet && this._noDataRowOutlet) {\n      this._hasAllOutlets = true;\n      // In some setups this may fire before `ngAfterContentInit`\n      // so we need a check here. See #28538.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n  }\n  /** Whether the table has all the information to start rendering. */\n  _canRender() {\n    return this._hasAllOutlets && this._hasInitialized;\n  }\n  /** Renders the table if its state has changed. */\n  _render() {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (!this._headerRowDefs.length && !this._footerRowDefs.length && !this._rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingRowDefsError();\n    }\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n    this._checkStickyStates();\n  }\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  _getAllRenderRows() {\n    const renderRows = [];\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n    if (!this._data) {\n      return renderRows;\n    }\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n        const cache = this._cachedRenderRowsMap.get(renderRow.data);\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef).push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n    return renderRows;\n  }\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  _getRenderRowsForData(data, dataIndex, cache) {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift();\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {\n          data,\n          rowDef,\n          dataIndex\n        };\n      }\n    });\n  }\n  /** Update the map containing the content's column definitions. */\n  _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n    const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n    columnDefs.forEach(columnDef => {\n      if (this._columnDefsByName.has(columnDef.name) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n  /** Update the list of all available row definitions that can be used. */\n  _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n    this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (!this.multiTemplateDataRows && defaultRowDefs.length > 1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  _renderUpdatedColumns() {\n    const columnsDiffReducer = (acc, def) => {\n      // The differ should be run for every column, even if `acc` is already\n      // true (see #29922)\n      const diff = !!def.getColumnsDiff();\n      return acc || diff;\n    };\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  _switchDataSource(dataSource) {\n    this._data = [];\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      if (this._rowOutlet) {\n        this._rowOutlet.viewContainer.clear();\n      }\n    }\n    this._dataSource = dataSource;\n  }\n  /** Set up a subscription for the data provided by the data source. */\n  _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n    let dataStream;\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = of(this.dataSource);\n    }\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n    this._renderChangeSubscription = dataStream.pipe(takeUntil(this._onDestroy)).subscribe(data => {\n      this._data = data || [];\n      this.renderRows();\n    });\n  }\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  _addStickyColumnStyles(rows, rowDef) {\n    const columnDefs = Array.from(rowDef?.columns || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n  }\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet) {\n    const renderedRows = [];\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i);\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n    return renderedRows;\n  }\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data, dataIndex) {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n    let rowDefs = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n    return rowDefs;\n  }\n  _getEmbeddedViewArgs(renderRow, index) {\n    const rowDef = renderRow.rowDef;\n    const context = {\n      $implicit: renderRow.data\n    };\n    return {\n      templateRef: rowDef.template,\n      context,\n      index\n    };\n  }\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  _renderRow(outlet, rowDef, index, context = {}) {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n  _renderCellTemplateForItem(rowDef, context) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex);\n      const context = viewRef.context;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n  /** Gets the column definitions for the provided row def. */\n  _getCellTemplates(rowDef) {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n      return rowDef.extractCellTemplate(column);\n    });\n  }\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  _checkStickyStates() {\n    const stickyCheckReducer = (acc, d) => {\n      return acc || d.hasStickyChanged();\n    };\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  _setupStickyStyler() {\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, this._platform.isBrowser, this.needsPositionStickyOnElement, direction, this._stickyPositioningListener, this._injector);\n    (this._dir ? this._dir.change : of()).pipe(takeUntil(this._onDestroy)).subscribe(value => {\n      this._stickyStyler.direction = value;\n      this.updateStickyColumnStyles();\n    });\n  }\n  /** Filters definitions that belong to this table from a QueryList. */\n  _getOwnDefs(items) {\n    return items.filter(item => !item._table || item._table === this);\n  }\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n    if (!noDataRow) {\n      return;\n    }\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n    const container = this._noDataRowOutlet.viewContainer;\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode = view.rootNodes[0];\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n    this._isShowingNoDataRow = shouldShow;\n    this._changeDetectorRef.markForCheck();\n  }\n  static ɵfac = function CdkTable_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTable)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkTable,\n    selectors: [[\"cdk-table\"], [\"table\", \"cdk-table\", \"\"]],\n    contentQueries: function CdkTable_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, CdkNoDataRow, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkColumnDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkRowDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkHeaderRowDef, 5);\n        i0.ɵɵcontentQuery(dirIndex, CdkFooterRowDef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._noDataRow = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentColumnDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentRowDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentHeaderRowDefs = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentFooterRowDefs = _t);\n      }\n    },\n    hostAttrs: [1, \"cdk-table\"],\n    hostVars: 2,\n    hostBindings: function CdkTable_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-table-fixed-layout\", ctx.fixedLayout);\n      }\n    },\n    inputs: {\n      trackBy: \"trackBy\",\n      dataSource: \"dataSource\",\n      multiTemplateDataRows: [2, \"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute],\n      fixedLayout: [2, \"fixedLayout\", \"fixedLayout\", booleanAttribute]\n    },\n    outputs: {\n      contentChanged: \"contentChanged\"\n    },\n    exportAs: [\"cdkTable\"],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_TABLE,\n      useExisting: CdkTable\n    }, {\n      provide: _VIEW_REPEATER_STRATEGY,\n      useClass: _DisposeViewRepeaterStrategy\n    },\n    // Prevent nested tables from seeing this table's StickyPositioningListener.\n    {\n      provide: STICKY_POSITIONING_LISTENER,\n      useValue: null\n    }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n    ngContentSelectors: _c1,\n    decls: 5,\n    vars: 2,\n    consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n    template: function CdkTable_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵprojection(0);\n        i0.ɵɵprojection(1, 1);\n        i0.ɵɵtemplate(2, CdkTable_Conditional_2_Template, 1, 0)(3, CdkTable_Conditional_3_Template, 7, 0)(4, CdkTable_Conditional_4_Template, 4, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isServer ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._isNativeHtmlTable ? 3 : 4);\n      }\n    },\n    dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n    styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTable, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-table, table[cdk-table]',\n      exportAs: 'cdkTable',\n      template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n      host: {\n        'class': 'cdk-table',\n        '[class.cdk-table-fixed-layout]': 'fixedLayout'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      providers: [{\n        provide: CDK_TABLE,\n        useExisting: CdkTable\n      }, {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"]\n    }]\n  }], () => [], {\n    trackBy: [{\n      type: Input\n    }],\n    dataSource: [{\n      type: Input\n    }],\n    multiTemplateDataRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fixedLayout: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    contentChanged: [{\n      type: Output\n    }],\n    _contentColumnDefs: [{\n      type: ContentChildren,\n      args: [CdkColumnDef, {\n        descendants: true\n      }]\n    }],\n    _contentRowDefs: [{\n      type: ContentChildren,\n      args: [CdkRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentHeaderRowDefs: [{\n      type: ContentChildren,\n      args: [CdkHeaderRowDef, {\n        descendants: true\n      }]\n    }],\n    _contentFooterRowDefs: [{\n      type: ContentChildren,\n      args: [CdkFooterRowDef, {\n        descendants: true\n      }]\n    }],\n    _noDataRow: [{\n      type: ContentChild,\n      args: [CdkNoDataRow]\n    }]\n  });\n})();\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n  return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n  const uppercaseSection = section.toUpperCase();\n  let current = outlet.viewContainer.element.nativeElement;\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? current.nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n  return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n  _table = inject(CdkTable, {\n    optional: true\n  });\n  _options = inject(TEXT_COLUMN_OPTIONS, {\n    optional: true\n  });\n  /** Column name that should be used to reference this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._name = name;\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  _name;\n  /**\n   * Text label that should be used for the column header. If this property is not\n   * set, the header text will default to the column name with its first letter capitalized.\n   */\n  headerText;\n  /**\n   * Accessor function to retrieve the data rendered for each cell. If this\n   * property is not set, the data cells will render the value found in the data's property matching\n   * the column's name. For example, if the column is named `id`, then the rendered value will be\n   * value defined by the data's `id` property.\n   */\n  dataAccessor;\n  /** Alignment of the cell values. */\n  justify = 'start';\n  /** @docs-private */\n  columnDef;\n  /**\n   * The column cell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  cell;\n  /**\n   * The column headerCell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  headerCell;\n  constructor() {\n    this._options = this._options || {};\n  }\n  ngOnInit() {\n    this._syncColumnDefName();\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n    if (!this.dataAccessor) {\n      this.dataAccessor = this._options.defaultDataAccessor || ((data, name) => data[name]);\n    }\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n    return name[0].toUpperCase() + name.slice(1);\n  }\n  /** Synchronizes the column definition name with the text column name. */\n  _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n  static ɵfac = function CdkTextColumn_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTextColumn)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkTextColumn,\n    selectors: [[\"cdk-text-column\"]],\n    viewQuery: function CdkTextColumn_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkColumnDef, 7);\n        i0.ɵɵviewQuery(CdkCellDef, 7);\n        i0.ɵɵviewQuery(CdkHeaderCellDef, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.columnDef = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cell = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCell = _t.first);\n      }\n    },\n    inputs: {\n      name: \"name\",\n      headerText: \"headerText\",\n      dataAccessor: \"dataAccessor\",\n      justify: \"justify\"\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[\"cdkColumnDef\", \"\"], [\"cdk-header-cell\", \"\", 3, \"text-align\", 4, \"cdkHeaderCellDef\"], [\"cdk-cell\", \"\", 3, \"text-align\", 4, \"cdkCellDef\"], [\"cdk-header-cell\", \"\"], [\"cdk-cell\", \"\"]],\n    template: function CdkTextColumn_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementContainerStart(0, 0);\n        i0.ɵɵtemplate(1, CdkTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, CdkTextColumn_td_2_Template, 2, 3, \"td\", 2);\n        i0.ɵɵelementContainerEnd();\n      }\n    },\n    dependencies: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-text-column',\n      template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell]\n    }]\n  }], () => [], {\n    name: [{\n      type: Input\n    }],\n    headerText: [{\n      type: Input\n    }],\n    dataAccessor: [{\n      type: Input\n    }],\n    justify: [{\n      type: Input\n    }],\n    columnDef: [{\n      type: ViewChild,\n      args: [CdkColumnDef, {\n        static: true\n      }]\n    }],\n    cell: [{\n      type: ViewChild,\n      args: [CdkCellDef, {\n        static: true\n      }]\n    }],\n    headerCell: [{\n      type: ViewChild,\n      args: [CdkHeaderCellDef, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst EXPORTED_DECLARATIONS = [CdkTable, CdkRowDef, CdkCellDef, CdkCellOutlet, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkCell, CdkRow, CdkHeaderCell, CdkFooterCell, CdkHeaderRow, CdkHeaderRowDef, CdkFooterRow, CdkFooterRowDef, DataRowOutlet, HeaderRowOutlet, FooterRowOutlet, CdkTextColumn, CdkNoDataRow, CdkRecycleRows, NoDataRowOutlet];\nclass CdkTableModule {\n  static ɵfac = function CdkTableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTableModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CdkTableModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ScrollingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTableModule, [{\n    type: NgModule,\n    args: [{\n      exports: EXPORTED_DECLARATIONS,\n      imports: [ScrollingModule, ...EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_POSITIONING_LISTENER, TEXT_COLUMN_OPTIONS };", "map": {"version": 3, "names": ["i", "isDataSource", "_c0", "_c1", "CdkTable_Conditional_2_Template", "rf", "ctx", "i0", "ɵɵprojection", "CdkTable_Conditional_3_Template", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "CdkTable_Conditional_4_Template", "CdkTextColumn_th_1_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "CdkTextColumn_td_2_Template", "data_r2", "$implicit", "dataAccessor", "name", "D", "DataSource", "InjectionToken", "inject", "TemplateRef", "Directive", "booleanAttribute", "Input", "ContentChild", "ElementRef", "Iterable<PERSON><PERSON><PERSON>", "ViewContainerRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "afterNextRender", "ChangeDetectorRef", "DOCUMENT", "EventEmitter", "Injector", "HostAttributeToken", "Output", "ContentChildren", "ViewChild", "NgModule", "Subject", "BehaviorSubject", "isObservable", "of", "takeUntil", "b", "_VIEW_REPEATER_STRATEGY", "_", "_RecycleViewRepeaterStrategy", "a", "_ViewRepeaterOperation", "_DisposeViewRepeaterStrategy", "Directionality", "P", "Platform", "ViewportRuler", "ScrollingModule", "CDK_TABLE", "TEXT_COLUMN_OPTIONS", "CdkCellDef", "template", "constructor", "ɵfac", "CdkCellDef_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "CdkHeaderCellDef", "CdkHeaderCellDef_Factory", "CdkFooterCellDef", "CdkFooterCellDef_Factory", "CdkColumnDef", "_table", "optional", "_hasStickyChanged", "_name", "_setNameInput", "sticky", "_sticky", "value", "stickyEnd", "_stickyEnd", "cell", "headerCell", "<PERSON><PERSON><PERSON><PERSON>", "cssClassFriendlyName", "_columnCssClassName", "hasStickyChanged", "resetStickyChanged", "_updateColumnCssClassName", "replace", "CdkColumnDef_Factory", "contentQueries", "CdkColumnDef_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInputTransformsFeature", "providers", "transform", "BaseCdkCell", "columnDef", "elementRef", "nativeElement", "classList", "add", "CdkHeaderCell", "CdkHeaderCell_Factory", "hostAttrs", "ɵɵInheritDefinitionFeature", "host", "CdkFooterCell", "role", "_getCellRole", "setAttribute", "CdkFooterCell_Factory", "CdkCell", "CdkCell_Factory", "CDK_ROW_TEMPLATE", "BaseRowDef", "_differs", "columns", "_<PERSON><PERSON><PERSON><PERSON>", "ngOnChanges", "changes", "currentValue", "find", "create", "diff", "getColumnsDiff", "extractCellTemplate", "column", "CdkHeaderRowDef", "CdkFooterRowDef", "BaseRowDef_Factory", "ɵɵNgOnChangesFeature", "CdkHeaderRowDef_Factory", "alias", "CdkFooterRowDef_Factory", "CdkRowDef", "when", "CdkRowDef_Factory", "CdkCellOutlet", "_viewContainer", "cells", "context", "mostRecentCellOutlet", "ngOnDestroy", "CdkCellOutlet_Factory", "CdkHeaderRow", "CdkHeaderRow_Factory", "ɵcmp", "ɵɵdefineComponent", "ɵɵStandaloneFeature", "decls", "vars", "consts", "CdkHeaderRow_Template", "dependencies", "encapsulation", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "imports", "CdkFooterRow", "CdkFooterRow_Factory", "CdkFooterRow_Template", "CdkRow", "CdkRow_Factory", "CdkRow_Template", "CdkNoDataRow", "templateRef", "_contentClassName", "CdkNoDataRow_Factory", "STICKY_DIRECTIONS", "<PERSON>y<PERSON><PERSON><PERSON>", "_isNativeHtmlTable", "_stickCellCss", "_isBrowser", "_needsPositionStickyOnElement", "direction", "_positionListener", "_tableInjector", "_elemSizeCache", "WeakMap", "_resizeObserver", "globalThis", "ResizeObserver", "entries", "_updateCachedSizes", "_updatedStickyColumnsParamsToReplay", "_stickyColumnsReplayTimeout", "_cachedCellWidths", "_borderCellCss", "_destroyed", "clearStickyPositioning", "rows", "stickyDirections", "includes", "_removeFromStickyColumnReplayQueue", "elementsToClear", "row", "nodeType", "ELEMENT_NODE", "push", "Array", "from", "children", "write", "element", "_removeStickyStyle", "injector", "updateStickyColumns", "stickyStartStates", "stickyEndStates", "recalculateCellWidths", "replay", "length", "some", "state", "stickyColumnsUpdated", "sizes", "stickyEndColumnsUpdated", "firstRow", "num<PERSON>ells", "isRtl", "start", "end", "lastStickyStart", "lastIndexOf", "firstStickyEnd", "indexOf", "cellWidths", "startPositions", "endPositions", "_updateStickyColumnReplayQueue", "earlyRead", "_get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_getStickyStartColumnPositions", "_getStickyEndColumnPositions", "_addStickyStyle", "w", "slice", "map", "width", "index", "reverse", "stickRows", "rowsToStick", "stickyStates", "position", "states", "stickyOffsets", "stickyCellHeights", "elementsToStick", "rowIndex", "stickyOffset", "height", "_retrieveElementSize", "borderedRowIndex", "offset", "isBorderedRowIndex", "stickyHeaderRowsUpdated", "offsets", "elements", "stickyFooterRowsUpdated", "updateStickyFooterContainer", "tableElement", "tfoot", "querySelector", "destroy", "clearTimeout", "disconnect", "contains", "dir", "style", "remove", "hasDirection", "zIndex", "_getCalculatedZIndex", "dir<PERSON><PERSON><PERSON>", "isBorderElement", "cssText", "zIndexIncrements", "top", "bottom", "left", "right", "firstRowCells", "widths", "positions", "nextPosition", "cachedSize", "get", "clientRect", "getBoundingClientRect", "size", "set", "observe", "box", "params", "rowsSet", "Set", "update", "filter", "has", "needsColumnUpdate", "entry", "newEntry", "borderBoxSize", "inlineSize", "blockSize", "contentRect", "target", "isCell", "setTimeout", "klass", "getTableUnknownColumnError", "id", "Error", "getTableDuplicateColumnNameError", "getTableMultipleDefaultRowDefsError", "getTableMissingMatchingRowDefError", "data", "JSON", "stringify", "getTableMissingRowDefsError", "getTableUnknownDataSourceError", "getTableTextColumnMissingParentTableError", "getTableTextColumnMissingNameError", "STICKY_POSITIONING_LISTENER", "CdkRecycleRows", "CdkRecycleRows_Factory", "useClass", "DataRowOutlet", "viewContainer", "table", "_rowOutlet", "_outletAssigned", "DataRowOutlet_Factory", "HeaderRowOutlet", "_headerRowOutlet", "HeaderRowOutlet_Factory", "FooterRowOutlet", "_footerRowOutlet", "FooterRowOutlet_Factory", "NoDataRowOutlet", "_noDataRowOutlet", "NoDataRowOutlet_Factory", "CdkTable", "_changeDetectorRef", "_elementRef", "_dir", "_platform", "_view<PERSON><PERSON><PERSON>er", "_viewportRuler", "_stickyPositioningListener", "skipSelf", "_document", "_data", "_onD<PERSON>roy", "_renderRows", "_renderChangeSubscription", "_columnDefsByName", "Map", "_rowDefs", "_headerRowDefs", "_footerRowDefs", "_data<PERSON><PERSON>er", "_defaultRowDef", "_customColumnDefs", "_customRowDefs", "_customHeaderRowDefs", "_customFooterRowDefs", "_customNoDataRow", "_headerRowDefChanged", "_footerRowDefChanged", "_stickyColumnStylesNeedReset", "_forceRecalculateCellWidths", "_cachedRenderRowsMap", "_sticky<PERSON><PERSON><PERSON>", "stickyCssClass", "needsPositionStickyOnElement", "_isServer", "_isShowingNoDataRow", "_hasAllOutlets", "_hasInitialized", "_cellRoleInternal", "undefined", "tableRole", "getAttribute", "trackBy", "_trackByFn", "fn", "console", "warn", "dataSource", "_dataSource", "_switchDataSource", "multiTemplateDataRows", "_multiTemplateDataRows", "_forceRenderDataRows", "updateStickyColumnStyles", "fixedLayout", "_fixedLayout", "contentChanged", "viewChange", "Number", "MAX_VALUE", "_contentColumnDefs", "_contentRowDefs", "_contentHeaderRowDefs", "_contentFooterRowDefs", "_noDataRow", "_injector", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "_i", "dataRow", "dataIndex", "ngOnInit", "_setupStickyStyler", "change", "pipe", "subscribe", "ngAfterContentInit", "ngAfterContentChecked", "_canRender", "_render", "for<PERSON>ach", "def", "clear", "next", "complete", "renderRows", "_getAllRenderRows", "_updateNoDataRow", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "item", "operation", "INSERTED", "_renderCellTemplateForItem", "rowDef", "_updateRowIndexContext", "forEachIdentityChange", "row<PERSON>iew", "addColumnDef", "removeColumnDef", "delete", "addRowDef", "removeRowDef", "addHeaderRowDef", "headerRowDef", "removeHeaderRowDef", "addFooterRowDef", "footerRowDef", "removeFooterRowDef", "setNoDataRow", "noDataRow", "updateStickyHeaderRowStyles", "headerRows", "_getRenderedRows", "thead", "closestTableSection", "display", "updateStickyFooterRowStyles", "footerRows", "dataRows", "headerRow", "_addStickyColumnStyles", "footerRow", "values", "_cacheRowDefs", "_cacheColumnDefs", "columnsChanged", "_renderUpdatedColumns", "rowDefsChanged", "_forceRenderHeaderRows", "_forceRenderFooterRows", "_observe<PERSON><PERSON><PERSON><PERSON><PERSON>", "_checkStickyStates", "prevCachedRenderRows", "renderRowsForData", "_getRenderRowsForData", "j", "renderRow", "cache", "rowDefs", "_getRowDefs", "cachedRenderRows", "shift", "columnDefs", "mergeArrayAndSet", "_getOwnDefs", "defaultRowDefs", "columnsDiffReducer", "acc", "dataColumnsChanged", "reduce", "headerColumnsChanged", "footerColumnsChanged", "unsubscribe", "dataStream", "connect", "isArray", "_renderRow", "columnName", "rowOutlet", "renderedRows", "viewRef", "rootNodes", "outlet", "view", "createEmbeddedView", "cellTemplate", "_getCellTemplates", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderIndex", "count", "last", "even", "odd", "columnId", "stickyCheckReducer", "d", "items", "shouldShow", "container", "rootNode", "CdkTable_Factory", "CdkTable_ContentQueries", "hostVars", "hostBindings", "CdkTable_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "useValue", "ngContentSelectors", "CdkTable_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵconditional", "styles", "descendants", "array", "concat", "section", "uppercaseSection", "toUpperCase", "current", "parentNode", "CdkTextColumn", "_options", "_syncColumnDefName", "_createDefaultHeaderText", "defaultDataAccessor", "defaultHeaderTextTransform", "CdkTextColumn_Factory", "viewQuery", "CdkTextColumn_Query", "ɵɵviewQuery", "CdkTextColumn_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "static", "EXPORTED_DECLARATIONS", "CdkTableModule", "CdkTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/table.mjs"], "sourcesContent": ["import { i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { D as DataSource } from './data-source-D34wiQZj.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, booleanAttribute, Input, ContentChild, ElementRef, IterableDiffers, ViewContainerRef, Component, ChangeDetectionStrategy, ViewEncapsulation, afterNextRender, ChangeDetectorRef, DOCUMENT, EventEmitter, Injector, HostAttributeToken, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, isObservable, of } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { b as _VIEW_REPEATER_STRATEGY, _ as _RecycleViewRepeaterStrategy, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';\nimport { D as Directionality } from './directionality-CChdj3az.mjs';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport '@angular/common';\nimport './element-x4z00URv.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nconst CDK_TABLE = new InjectionToken('CDK_TABLE');\n/** Injection token that can be used to specify the text column options. */\nconst TEXT_COLUMN_OPTIONS = new InjectionToken('text-column-options');\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass CdkCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkCellDef, isStandalone: true, selector: \"[cdkCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass CdkHeaderCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkHeaderCellDef, isStandalone: true, selector: \"[cdkHeaderCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass CdkFooterCellDef {\n    /** @docs-private */\n    template = inject(TemplateRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterCellDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkFooterCellDef, isStandalone: true, selector: \"[cdkFooterCellDef]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterCellDef]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\nclass CdkColumnDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    _name;\n    /** Whether the cell is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    /**\n     * Whether this column should be sticky positioned on the end of the row. Should make sure\n     * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n     * has been changed.\n     */\n    get stickyEnd() {\n        return this._stickyEnd;\n    }\n    set stickyEnd(value) {\n        if (value !== this._stickyEnd) {\n            this._stickyEnd = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _stickyEnd = false;\n    /** @docs-private */\n    cell;\n    /** @docs-private */\n    headerCell;\n    /** @docs-private */\n    footerCell;\n    /**\n     * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n     * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n     * do not match are replaced by the '-' character.\n     */\n    cssClassFriendlyName;\n    /**\n     * Class name for cells in this column.\n     * @docs-private\n     */\n    _columnCssClassName;\n    constructor() { }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    /**\n     * Overridable method that sets the css classes that will be added to every cell in this\n     * column.\n     * In the future, columnCssClassName will change from type string[] to string and this\n     * will set a single string value.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n    }\n    /**\n     * This has been extracted to a util because of TS 4 and VE.\n     * View Engine doesn't support property rename inheritance.\n     * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n     * @docs-private\n     */\n    _setNameInput(value) {\n        // If the directive is set without a name (updated programmatically), then this setter will\n        // trigger with an empty string and should not overwrite the programmatically set value.\n        if (value) {\n            this._name = value;\n            this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n            this._updateColumnCssClassName();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkColumnDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkColumnDef, isStandalone: true, selector: \"[cdkColumnDef]\", inputs: { name: [\"cdkColumnDef\", \"name\"], sticky: [\"sticky\", \"sticky\", booleanAttribute], stickyEnd: [\"stickyEnd\", \"stickyEnd\", booleanAttribute] }, providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }], queries: [{ propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true }, { propertyName: \"footerCell\", first: true, predicate: CdkFooterCellDef, descendants: true }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkColumnDef]',\n                    providers: [{ provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { name: [{\n                type: Input,\n                args: ['cdkColumnDef']\n            }], sticky: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stickyEnd: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cell: [{\n                type: ContentChild,\n                args: [CdkCellDef]\n            }], headerCell: [{\n                type: ContentChild,\n                args: [CdkHeaderCellDef]\n            }], footerCell: [{\n                type: ContentChild,\n                args: [CdkFooterCellDef]\n            }] } });\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nclass BaseCdkCell {\n    constructor(columnDef, elementRef) {\n        elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n    }\n}\n/** Header cell template container that adds the right classes and role. */\nclass CdkHeaderCell extends BaseCdkCell {\n    constructor() {\n        super(inject(CdkColumnDef), inject(ElementRef));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkHeaderCell, isStandalone: true, selector: \"cdk-header-cell, th[cdk-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"cdk-header-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-header-cell, th[cdk-header-cell]',\n                    host: {\n                        'class': 'cdk-header-cell',\n                        'role': 'columnheader',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n/** Footer cell template container that adds the right classes and role. */\nclass CdkFooterCell extends BaseCdkCell {\n    constructor() {\n        const columnDef = inject(CdkColumnDef);\n        const elementRef = inject(ElementRef);\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkFooterCell, isStandalone: true, selector: \"cdk-footer-cell, td[cdk-footer-cell]\", host: { classAttribute: \"cdk-footer-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n                    host: {\n                        'class': 'cdk-footer-cell',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n/** Cell template container that adds the right classes and role. */\nclass CdkCell extends BaseCdkCell {\n    constructor() {\n        const columnDef = inject(CdkColumnDef);\n        const elementRef = inject(ElementRef);\n        super(columnDef, elementRef);\n        const role = columnDef._table?._getCellRole();\n        if (role) {\n            elementRef.nativeElement.setAttribute('role', role);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCell, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkCell, isStandalone: true, selector: \"cdk-cell, td[cdk-cell]\", host: { classAttribute: \"cdk-cell\" }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-cell, td[cdk-cell]',\n                    host: {\n                        'class': 'cdk-cell',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nconst CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\nclass BaseRowDef {\n    template = inject(TemplateRef);\n    _differs = inject(IterableDiffers);\n    /** The columns to be displayed on this row. */\n    columns;\n    /** Differ used to check if any changes were made to the columns. */\n    _columnsDiffer;\n    constructor() { }\n    ngOnChanges(changes) {\n        // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n        // of the columns property or an empty array if none is provided.\n        if (!this._columnsDiffer) {\n            const columns = (changes['columns'] && changes['columns'].currentValue) || [];\n            this._columnsDiffer = this._differs.find(columns).create();\n            this._columnsDiffer.diff(columns);\n        }\n    }\n    /**\n     * Returns the difference between the current columns and the columns from the last diff, or null\n     * if there is no difference.\n     */\n    getColumnsDiff() {\n        return this._columnsDiffer.diff(this.columns);\n    }\n    /** Gets this row def's relevant cell template from the provided column def. */\n    extractCellTemplate(column) {\n        if (this instanceof CdkHeaderRowDef) {\n            return column.headerCell.template;\n        }\n        if (this instanceof CdkFooterRowDef) {\n            return column.footerCell.template;\n        }\n        else {\n            return column.cell.template;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BaseRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: BaseRowDef, isStandalone: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: BaseRowDef, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [] });\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass CdkHeaderRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    constructor() {\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkHeaderRowDef, isStandalone: true, selector: \"[cdkHeaderRowDef]\", inputs: { columns: [\"cdkHeaderRowDef\", \"columns\"], sticky: [\"cdkHeaderRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkHeaderRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkHeaderRowDef' }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkHeaderRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass CdkFooterRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    _hasStickyChanged = false;\n    /** Whether the row is sticky. */\n    get sticky() {\n        return this._sticky;\n    }\n    set sticky(value) {\n        if (value !== this._sticky) {\n            this._sticky = value;\n            this._hasStickyChanged = true;\n        }\n    }\n    _sticky = false;\n    constructor() {\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n    // Explicitly define it so that the method is called as part of the Angular lifecycle.\n    ngOnChanges(changes) {\n        super.ngOnChanges(changes);\n    }\n    /** Whether the sticky state has changed. */\n    hasStickyChanged() {\n        const hasStickyChanged = this._hasStickyChanged;\n        this.resetStickyChanged();\n        return hasStickyChanged;\n    }\n    /** Resets the sticky changed state. */\n    resetStickyChanged() {\n        this._hasStickyChanged = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"20.0.0\", type: CdkFooterRowDef, isStandalone: true, selector: \"[cdkFooterRowDef]\", inputs: { columns: [\"cdkFooterRowDef\", \"columns\"], sticky: [\"cdkFooterRowDefSticky\", \"sticky\", booleanAttribute] }, usesInheritance: true, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkFooterRowDef]',\n                    inputs: [{ name: 'columns', alias: 'cdkFooterRowDef' }],\n                }]\n        }], ctorParameters: () => [], propDecorators: { sticky: [{\n                type: Input,\n                args: [{ alias: 'cdkFooterRowDefSticky', transform: booleanAttribute }]\n            }] } });\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass CdkRowDef extends BaseRowDef {\n    _table = inject(CDK_TABLE, { optional: true });\n    /**\n     * Function that should return true if this row template should be used for the provided index\n     * and row data. If left undefined, this row will be considered the default row template to use\n     * when no other when functions return true for the data.\n     * For every row, there must be at least one when function that passes or an undefined to default.\n     */\n    when;\n    constructor() {\n        // TODO(andrewseguin): Add an input for providing a switch function to determine\n        //   if this template should be used.\n        super(inject(TemplateRef), inject(IterableDiffers));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRowDef, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkRowDef, isStandalone: true, selector: \"[cdkRowDef]\", inputs: { columns: [\"cdkRowDefColumns\", \"columns\"], when: [\"cdkRowDefWhen\", \"when\"] }, usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkRowDef]',\n                    inputs: [\n                        { name: 'columns', alias: 'cdkRowDefColumns' },\n                        { name: 'when', alias: 'cdkRowDefWhen' },\n                    ],\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\nclass CdkCellOutlet {\n    _viewContainer = inject(ViewContainerRef);\n    /** The ordered list of cells to render within this outlet's view container */\n    cells;\n    /** The data context to be provided to each cell */\n    context;\n    /**\n     * Static property containing the latest constructed instance of this class.\n     * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n     * createEmbeddedView. After one of these components are created, this property will provide\n     * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n     * construct the cells with the provided context.\n     */\n    static mostRecentCellOutlet = null;\n    constructor() {\n        CdkCellOutlet.mostRecentCellOutlet = this;\n    }\n    ngOnDestroy() {\n        // If this was the last outlet being rendered in the view, remove the reference\n        // from the static property after it has been destroyed to avoid leaking memory.\n        if (CdkCellOutlet.mostRecentCellOutlet === this) {\n            CdkCellOutlet.mostRecentCellOutlet = null;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCellOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkCellOutlet, isStandalone: true, selector: \"[cdkCellOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkCellOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCellOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass CdkHeaderRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkHeaderRow, isStandalone: true, selector: \"cdk-header-row, tr[cdk-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-header-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-header-row, tr[cdk-header-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass CdkFooterRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkFooterRow, isStandalone: true, selector: \"cdk-footer-row, tr[cdk-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-footer-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-footer-row, tr[cdk-footer-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-footer-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass CdkRow {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkRow, isStandalone: true, selector: \"cdk-row, tr[cdk-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"cdk-row\" }, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-row, tr[cdk-row]',\n                    template: CDK_ROW_TEMPLATE,\n                    host: {\n                        'class': 'cdk-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass CdkNoDataRow {\n    templateRef = inject(TemplateRef);\n    _contentClassName = 'cdk-no-data-row';\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkNoDataRow, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkNoDataRow, isStandalone: true, selector: \"ng-template[cdkNoDataRow]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkNoDataRow]',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nconst STICKY_DIRECTIONS = ['top', 'bottom', 'left', 'right'];\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nclass StickyStyler {\n    _isNativeHtmlTable;\n    _stickCellCss;\n    _isBrowser;\n    _needsPositionStickyOnElement;\n    direction;\n    _positionListener;\n    _tableInjector;\n    _elemSizeCache = new WeakMap();\n    _resizeObserver = globalThis?.ResizeObserver\n        ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries))\n        : null;\n    _updatedStickyColumnsParamsToReplay = [];\n    _stickyColumnsReplayTimeout = null;\n    _cachedCellWidths = [];\n    _borderCellCss;\n    _destroyed = false;\n    /**\n     * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n     *     that uses the native `<table>` element.\n     * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n     *     sticky positioning applied.\n     * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n     *     by reversing left/right positions.\n     * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n     * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n     *     using inline styles. If false, it is assumed that position: sticky is included in\n     *     the component stylesheet for _stickCellCss.\n     * @param _positionListener A listener that is notified of changes to sticky rows/columns\n     *     and their dimensions.\n     * @param _tableInjector The table's Injector.\n     */\n    constructor(_isNativeHtmlTable, _stickCellCss, _isBrowser = true, _needsPositionStickyOnElement = true, direction, _positionListener, _tableInjector) {\n        this._isNativeHtmlTable = _isNativeHtmlTable;\n        this._stickCellCss = _stickCellCss;\n        this._isBrowser = _isBrowser;\n        this._needsPositionStickyOnElement = _needsPositionStickyOnElement;\n        this.direction = direction;\n        this._positionListener = _positionListener;\n        this._tableInjector = _tableInjector;\n        this._borderCellCss = {\n            'top': `${_stickCellCss}-border-elem-top`,\n            'bottom': `${_stickCellCss}-border-elem-bottom`,\n            'left': `${_stickCellCss}-border-elem-left`,\n            'right': `${_stickCellCss}-border-elem-right`,\n        };\n    }\n    /**\n     * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n     * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n     * @param rows The list of rows that should be cleared from sticking in the provided directions\n     * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n     */\n    clearStickyPositioning(rows, stickyDirections) {\n        if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n            this._removeFromStickyColumnReplayQueue(rows);\n        }\n        const elementsToClear = [];\n        for (const row of rows) {\n            // If the row isn't an element (e.g. if it's an `ng-container`),\n            // it won't have inline styles or `children` so we skip it.\n            if (row.nodeType !== row.ELEMENT_NODE) {\n                continue;\n            }\n            elementsToClear.push(row, ...Array.from(row.children));\n        }\n        // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n        afterNextRender({\n            write: () => {\n                for (const element of elementsToClear) {\n                    this._removeStickyStyle(element, stickyDirections);\n                }\n            },\n        }, {\n            injector: this._tableInjector,\n        });\n    }\n    /**\n     * Applies sticky left and right positions to the cells of each row according to the sticky\n     * states of the rendered column definitions.\n     * @param rows The rows that should have its set of cells stuck according to the sticky states.\n     * @param stickyStartStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the start of the row.\n     * @param stickyEndStates A list of boolean states where each state represents whether the cell\n     *     in this index position should be stuck to the end of the row.\n     * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n     *     column cell. If `false` cached widths will be used instead.\n     * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n     */\n    updateStickyColumns(rows, stickyStartStates, stickyEndStates, recalculateCellWidths = true, replay = true) {\n        // Don't cache any state if none of the columns are sticky.\n        if (!rows.length ||\n            !this._isBrowser ||\n            !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))) {\n            this._positionListener?.stickyColumnsUpdated({ sizes: [] });\n            this._positionListener?.stickyEndColumnsUpdated({ sizes: [] });\n            return;\n        }\n        // Coalesce with sticky row updates (and potentially other changes like column resize).\n        const firstRow = rows[0];\n        const numCells = firstRow.children.length;\n        const isRtl = this.direction === 'rtl';\n        const start = isRtl ? 'right' : 'left';\n        const end = isRtl ? 'left' : 'right';\n        const lastStickyStart = stickyStartStates.lastIndexOf(true);\n        const firstStickyEnd = stickyEndStates.indexOf(true);\n        let cellWidths;\n        let startPositions;\n        let endPositions;\n        if (replay) {\n            this._updateStickyColumnReplayQueue({\n                rows: [...rows],\n                stickyStartStates: [...stickyStartStates],\n                stickyEndStates: [...stickyEndStates],\n            });\n        }\n        afterNextRender({\n            earlyRead: () => {\n                cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n                startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n                endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n            },\n            write: () => {\n                for (const row of rows) {\n                    for (let i = 0; i < numCells; i++) {\n                        const cell = row.children[i];\n                        if (stickyStartStates[i]) {\n                            this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n                        }\n                        if (stickyEndStates[i]) {\n                            this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n                        }\n                    }\n                }\n                if (this._positionListener && cellWidths.some(w => !!w)) {\n                    this._positionListener.stickyColumnsUpdated({\n                        sizes: lastStickyStart === -1\n                            ? []\n                            : cellWidths\n                                .slice(0, lastStickyStart + 1)\n                                .map((width, index) => (stickyStartStates[index] ? width : null)),\n                    });\n                    this._positionListener.stickyEndColumnsUpdated({\n                        sizes: firstStickyEnd === -1\n                            ? []\n                            : cellWidths\n                                .slice(firstStickyEnd)\n                                .map((width, index) => stickyEndStates[index + firstStickyEnd] ? width : null)\n                                .reverse(),\n                    });\n                }\n            },\n        }, {\n            injector: this._tableInjector,\n        });\n    }\n    /**\n     * Applies sticky positioning to the row's cells if using the native table layout, and to the\n     * row itself otherwise.\n     * @param rowsToStick The list of rows that should be stuck according to their corresponding\n     *     sticky state and to the provided top or bottom position.\n     * @param stickyStates A list of boolean states where each state represents whether the row\n     *     should be stuck in the particular top or bottom position.\n     * @param position The position direction in which the row should be stuck if that row should be\n     *     sticky.\n     *\n     */\n    stickRows(rowsToStick, stickyStates, position) {\n        // Since we can't measure the rows on the server, we can't stick the rows properly.\n        if (!this._isBrowser) {\n            return;\n        }\n        // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n        // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n        // sticky states need to be reversed as well.\n        const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n        const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n        // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n        const stickyOffsets = [];\n        const stickyCellHeights = [];\n        const elementsToStick = [];\n        // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n        // (and potentially other changes like column resize).\n        afterNextRender({\n            earlyRead: () => {\n                for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n                    if (!states[rowIndex]) {\n                        continue;\n                    }\n                    stickyOffsets[rowIndex] = stickyOffset;\n                    const row = rows[rowIndex];\n                    elementsToStick[rowIndex] = this._isNativeHtmlTable\n                        ? Array.from(row.children)\n                        : [row];\n                    const height = this._retrieveElementSize(row).height;\n                    stickyOffset += height;\n                    stickyCellHeights[rowIndex] = height;\n                }\n            },\n            write: () => {\n                const borderedRowIndex = states.lastIndexOf(true);\n                for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n                    if (!states[rowIndex]) {\n                        continue;\n                    }\n                    const offset = stickyOffsets[rowIndex];\n                    const isBorderedRowIndex = rowIndex === borderedRowIndex;\n                    for (const element of elementsToStick[rowIndex]) {\n                        this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n                    }\n                }\n                if (position === 'top') {\n                    this._positionListener?.stickyHeaderRowsUpdated({\n                        sizes: stickyCellHeights,\n                        offsets: stickyOffsets,\n                        elements: elementsToStick,\n                    });\n                }\n                else {\n                    this._positionListener?.stickyFooterRowsUpdated({\n                        sizes: stickyCellHeights,\n                        offsets: stickyOffsets,\n                        elements: elementsToStick,\n                    });\n                }\n            },\n        }, {\n            injector: this._tableInjector,\n        });\n    }\n    /**\n     * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n     * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n     * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n     * the tfoot element.\n     */\n    updateStickyFooterContainer(tableElement, stickyStates) {\n        if (!this._isNativeHtmlTable) {\n            return;\n        }\n        // Coalesce with other sticky updates (and potentially other changes like column resize).\n        afterNextRender({\n            write: () => {\n                const tfoot = tableElement.querySelector('tfoot');\n                if (tfoot) {\n                    if (stickyStates.some(state => !state)) {\n                        this._removeStickyStyle(tfoot, ['bottom']);\n                    }\n                    else {\n                        this._addStickyStyle(tfoot, 'bottom', 0, false);\n                    }\n                }\n            },\n        }, {\n            injector: this._tableInjector,\n        });\n    }\n    /** Triggered by the table's OnDestroy hook. */\n    destroy() {\n        if (this._stickyColumnsReplayTimeout) {\n            clearTimeout(this._stickyColumnsReplayTimeout);\n        }\n        this._resizeObserver?.disconnect();\n        this._destroyed = true;\n    }\n    /**\n     * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n     * the zIndex, removing each of the provided sticky directions, and removing the\n     * sticky position if there are no more directions.\n     */\n    _removeStickyStyle(element, stickyDirections) {\n        if (!element.classList.contains(this._stickCellCss)) {\n            return;\n        }\n        for (const dir of stickyDirections) {\n            element.style[dir] = '';\n            element.classList.remove(this._borderCellCss[dir]);\n        }\n        // If the element no longer has any more sticky directions, remove sticky positioning and\n        // the sticky CSS class.\n        // Short-circuit checking element.style[dir] for stickyDirections as they\n        // were already removed above.\n        const hasDirection = STICKY_DIRECTIONS.some(dir => stickyDirections.indexOf(dir) === -1 && element.style[dir]);\n        if (hasDirection) {\n            element.style.zIndex = this._getCalculatedZIndex(element);\n        }\n        else {\n            // When not hasDirection, _getCalculatedZIndex will always return ''.\n            element.style.zIndex = '';\n            if (this._needsPositionStickyOnElement) {\n                element.style.position = '';\n            }\n            element.classList.remove(this._stickCellCss);\n        }\n    }\n    /**\n     * Adds the sticky styling to the element by adding the sticky style class, changing position\n     * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n     * direction and value.\n     */\n    _addStickyStyle(element, dir, dirValue, isBorderElement) {\n        element.classList.add(this._stickCellCss);\n        if (isBorderElement) {\n            element.classList.add(this._borderCellCss[dir]);\n        }\n        element.style[dir] = `${dirValue}px`;\n        element.style.zIndex = this._getCalculatedZIndex(element);\n        if (this._needsPositionStickyOnElement) {\n            element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n        }\n    }\n    /**\n     * Calculate what the z-index should be for the element, depending on what directions (top,\n     * bottom, left, right) have been set. It should be true that elements with a top direction\n     * should have the highest index since these are elements like a table header. If any of those\n     * elements are also sticky in another direction, then they should appear above other elements\n     * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n     * (e.g. footer rows) should then be next in the ordering such that they are below the header\n     * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n     * should minimally increment so that they are above non-sticky elements but below top and bottom\n     * elements.\n     */\n    _getCalculatedZIndex(element) {\n        const zIndexIncrements = {\n            top: 100,\n            bottom: 10,\n            left: 1,\n            right: 1,\n        };\n        let zIndex = 0;\n        // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n        // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n        // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n        for (const dir of STICKY_DIRECTIONS) {\n            if (element.style[dir]) {\n                zIndex += zIndexIncrements[dir];\n            }\n        }\n        return zIndex ? `${zIndex}` : '';\n    }\n    /** Gets the widths for each cell in the provided row. */\n    _getCellWidths(row, recalculateCellWidths = true) {\n        if (!recalculateCellWidths && this._cachedCellWidths.length) {\n            return this._cachedCellWidths;\n        }\n        const cellWidths = [];\n        const firstRowCells = row.children;\n        for (let i = 0; i < firstRowCells.length; i++) {\n            const cell = firstRowCells[i];\n            cellWidths.push(this._retrieveElementSize(cell).width);\n        }\n        this._cachedCellWidths = cellWidths;\n        return cellWidths;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyStartColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = 0; i < widths.length; i++) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Determines the left and right positions of each sticky column cell, which will be the\n     * accumulation of all sticky column cell widths to the left and right, respectively.\n     * Non-sticky cells do not need to have a value set since their positions will not be applied.\n     */\n    _getStickyEndColumnPositions(widths, stickyStates) {\n        const positions = [];\n        let nextPosition = 0;\n        for (let i = widths.length; i > 0; i--) {\n            if (stickyStates[i]) {\n                positions[i] = nextPosition;\n                nextPosition += widths[i];\n            }\n        }\n        return positions;\n    }\n    /**\n     * Retreives the most recently observed size of the specified element from the cache, or\n     * meaures it directly if not yet cached.\n     */\n    _retrieveElementSize(element) {\n        const cachedSize = this._elemSizeCache.get(element);\n        if (cachedSize) {\n            return cachedSize;\n        }\n        const clientRect = element.getBoundingClientRect();\n        const size = { width: clientRect.width, height: clientRect.height };\n        if (!this._resizeObserver) {\n            return size;\n        }\n        this._elemSizeCache.set(element, size);\n        this._resizeObserver.observe(element, { box: 'border-box' });\n        return size;\n    }\n    /**\n     * Conditionally enqueue the requested sticky update and clear previously queued updates\n     * for the same rows.\n     */\n    _updateStickyColumnReplayQueue(params) {\n        this._removeFromStickyColumnReplayQueue(params.rows);\n        // No need to replay if a flush is pending.\n        if (!this._stickyColumnsReplayTimeout) {\n            this._updatedStickyColumnsParamsToReplay.push(params);\n        }\n    }\n    /** Remove updates for the specified rows from the queue. */\n    _removeFromStickyColumnReplayQueue(rows) {\n        const rowsSet = new Set(rows);\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n            update.rows = update.rows.filter(row => !rowsSet.has(row));\n        }\n        this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(update => !!update.rows.length);\n    }\n    /** Update _elemSizeCache with the observed sizes. */\n    _updateCachedSizes(entries) {\n        let needsColumnUpdate = false;\n        for (const entry of entries) {\n            const newEntry = entry.borderBoxSize?.length\n                ? {\n                    width: entry.borderBoxSize[0].inlineSize,\n                    height: entry.borderBoxSize[0].blockSize,\n                }\n                : {\n                    width: entry.contentRect.width,\n                    height: entry.contentRect.height,\n                };\n            if (newEntry.width !== this._elemSizeCache.get(entry.target)?.width &&\n                isCell(entry.target)) {\n                needsColumnUpdate = true;\n            }\n            this._elemSizeCache.set(entry.target, newEntry);\n        }\n        if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n            if (this._stickyColumnsReplayTimeout) {\n                clearTimeout(this._stickyColumnsReplayTimeout);\n            }\n            this._stickyColumnsReplayTimeout = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                for (const update of this._updatedStickyColumnsParamsToReplay) {\n                    this.updateStickyColumns(update.rows, update.stickyStartStates, update.stickyEndStates, true, false);\n                }\n                this._updatedStickyColumnsParamsToReplay = [];\n                this._stickyColumnsReplayTimeout = null;\n            }, 0);\n        }\n    }\n}\nfunction isCell(element) {\n    return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass => element.classList.contains(klass));\n}\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nfunction getTableUnknownColumnError(id) {\n    return Error(`Could not find column with id \"${id}\".`);\n}\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nfunction getTableDuplicateColumnNameError(name) {\n    return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nfunction getTableMultipleDefaultRowDefsError() {\n    return Error(`There can only be one default row without a when predicate function.`);\n}\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nfunction getTableMissingMatchingRowDefError(data) {\n    return Error(`Could not find a matching row definition for the` +\n        `provided row data: ${JSON.stringify(data)}`);\n}\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nfunction getTableMissingRowDefsError() {\n    return Error('Missing definitions for header, footer, and row; ' +\n        'cannot determine which columns should be rendered.');\n}\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nfunction getTableUnknownDataSourceError() {\n    return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nfunction getTableTextColumnMissingParentTableError() {\n    return Error(`Text column could not find a parent table for registration.`);\n}\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nfunction getTableTextColumnMissingNameError() {\n    return Error(`Table text column must have a name.`);\n}\n\n/** The injection token used to specify the StickyPositioningListener. */\nconst STICKY_POSITIONING_LISTENER = new InjectionToken('CDK_SPL');\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass CdkRecycleRows {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkRecycleRows, isStandalone: true, selector: \"cdk-table[recycleRows], table[cdk-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\nclass DataRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._rowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DataRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: DataRowOutlet, isStandalone: true, selector: \"[rowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: DataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[rowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\nclass HeaderRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._headerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: HeaderRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: HeaderRowOutlet, isStandalone: true, selector: \"[headerRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: HeaderRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[headerRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\nclass FooterRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._footerRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FooterRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: FooterRowOutlet, isStandalone: true, selector: \"[footerRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FooterRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[footerRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\nclass NoDataRowOutlet {\n    viewContainer = inject(ViewContainerRef);\n    elementRef = inject(ElementRef);\n    constructor() {\n        const table = inject(CDK_TABLE);\n        table._noDataRowOutlet = this;\n        table._outletAssigned();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NoDataRowOutlet, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"20.0.0\", type: NoDataRowOutlet, isStandalone: true, selector: \"[noDataRowOutlet]\", ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: NoDataRowOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[noDataRowOutlet]',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\nclass CdkTable {\n    _differs = inject(IterableDiffers);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _dir = inject(Directionality, { optional: true });\n    _platform = inject(Platform);\n    _viewRepeater = inject(_VIEW_REPEATER_STRATEGY);\n    _viewportRuler = inject(ViewportRuler);\n    _stickyPositioningListener = inject(STICKY_POSITIONING_LISTENER, { optional: true, skipSelf: true });\n    _document = inject(DOCUMENT);\n    /** Latest data provided by the data source. */\n    _data;\n    /** Subject that emits when the component has been destroyed. */\n    _onDestroy = new Subject();\n    /** List of the rendered rows as identified by their `RenderRow` object. */\n    _renderRows;\n    /** Subscription that listens for the data provided by the data source. */\n    _renderChangeSubscription;\n    /**\n     * Map of all the user's defined columns (header, data, and footer cell template) identified by\n     * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n     * any custom column definitions added to `_customColumnDefs`.\n     */\n    _columnDefsByName = new Map();\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n     */\n    _rowDefs;\n    /**\n     * Set of all header row definitions that can be used by this table. Populated by the rows\n     * gathered by using `ContentChildren` as well as any custom row definitions added to\n     * `_customHeaderRowDefs`.\n     */\n    _headerRowDefs;\n    /**\n     * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n     * using `ContentChildren` as well as any custom row definitions added to\n     * `_customFooterRowDefs`.\n     */\n    _footerRowDefs;\n    /** Differ used to find the changes in the data provided by the data source. */\n    _dataDiffer;\n    /** Stores the row definition that does not have a when predicate. */\n    _defaultRowDef;\n    /**\n     * Column definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * column definitions as *its* content child.\n     */\n    _customColumnDefs = new Set();\n    /**\n     * Data row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in data rows as *its* content child.\n     */\n    _customRowDefs = new Set();\n    /**\n     * Header row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n     * built-in header rows as *its* content child.\n     */\n    _customHeaderRowDefs = new Set();\n    /**\n     * Footer row definitions that were defined outside of the direct content children of the table.\n     * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n     * built-in footer row as *its* content child.\n     */\n    _customFooterRowDefs = new Set();\n    /** No data row that was defined outside of the direct content children of the table. */\n    _customNoDataRow;\n    /**\n     * Whether the header row definition has been changed. Triggers an update to the header row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _headerRowDefChanged = true;\n    /**\n     * Whether the footer row definition has been changed. Triggers an update to the footer row after\n     * content is checked. Initialized as true so that the table renders the initial set of rows.\n     */\n    _footerRowDefChanged = true;\n    /**\n     * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n     * change.\n     */\n    _stickyColumnStylesNeedReset = true;\n    /**\n     * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n     * `false`, cached values will be used instead. This is only applicable to tables with\n     * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n     */\n    _forceRecalculateCellWidths = true;\n    /**\n     * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n     * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n     * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n     * and row template matches, which allows the `IterableDiffer` to check rows by reference\n     * and understand which rows are added/moved/removed.\n     *\n     * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n     * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n     * contains an array of created pairs. The array is necessary to handle cases where the data\n     * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n     * stored.\n     */\n    _cachedRenderRowsMap = new Map();\n    /** Whether the table is applied to a native `<table>`. */\n    _isNativeHtmlTable;\n    /**\n     * Utility class that is responsible for applying the appropriate sticky positioning styles to\n     * the table's rows and cells.\n     */\n    _stickyStyler;\n    /**\n     * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n     * table subclasses.\n     */\n    stickyCssClass = 'cdk-table-sticky';\n    /**\n     * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n     * the position is set in a selector associated with the value of stickyCssClass. May be\n     * overridden by table subclasses\n     */\n    needsPositionStickyOnElement = true;\n    /** Whether the component is being rendered on the server. */\n    _isServer;\n    /** Whether the no data row is currently showing anything. */\n    _isShowingNoDataRow = false;\n    /** Whether the table has rendered out all the outlets for the first time. */\n    _hasAllOutlets = false;\n    /** Whether the table is done initializing. */\n    _hasInitialized = false;\n    /** Aria role to apply to the table's cells based on the table's own role. */\n    _getCellRole() {\n        // Perform this lazily in case the table's role was updated by a directive after construction.\n        if (this._cellRoleInternal === undefined) {\n            // Note that we set `role=\"cell\"` even on native `td` elements,\n            // because some browsers seem to require it. See #29784.\n            const tableRole = this._elementRef.nativeElement.getAttribute('role');\n            return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n        }\n        return this._cellRoleInternal;\n    }\n    _cellRoleInternal = undefined;\n    /**\n     * Tracking function that will be used to check the differences in data changes. Used similarly\n     * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n     * relative to the function to know if a row should be added/removed/moved.\n     * Accepts a function that takes two parameters, `index` and `item`.\n     */\n    get trackBy() {\n        return this._trackByFn;\n    }\n    set trackBy(fn) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n            console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n        }\n        this._trackByFn = fn;\n    }\n    _trackByFn;\n    /**\n     * The table's source of data, which can be provided in three ways (in order of complexity):\n     *   - Simple data array (each object represents one table row)\n     *   - Stream that emits a data array each time the array changes\n     *   - `DataSource` object that implements the connect/disconnect interface.\n     *\n     * If a data array is provided, the table must be notified when the array's objects are\n     * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n     * render the diff since the last table render. If the data array reference is changed, the table\n     * will automatically trigger an update to the rows.\n     *\n     * When providing an Observable stream, the table will trigger an update automatically when the\n     * stream emits a new array of data.\n     *\n     * Finally, when providing a `DataSource` object, the table will use the Observable stream\n     * provided by the connect function and trigger updates when that stream emits new data array\n     * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n     * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n     * subscriptions registered during the connect process).\n     */\n    get dataSource() {\n        return this._dataSource;\n    }\n    set dataSource(dataSource) {\n        if (this._dataSource !== dataSource) {\n            this._switchDataSource(dataSource);\n        }\n    }\n    _dataSource;\n    /**\n     * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n     * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n     * dataobject will render the first row that evaluates its when predicate to true, in the order\n     * defined in the table, or otherwise the default row which does not have a when predicate.\n     */\n    get multiTemplateDataRows() {\n        return this._multiTemplateDataRows;\n    }\n    set multiTemplateDataRows(value) {\n        this._multiTemplateDataRows = value;\n        // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n        // this setter will be invoked before the row outlet has been defined hence the null check.\n        if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n            this._forceRenderDataRows();\n            this.updateStickyColumnStyles();\n        }\n    }\n    _multiTemplateDataRows = false;\n    /**\n     * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n     * and optimize rendering sticky styles for native tables. No-op for flex tables.\n     */\n    get fixedLayout() {\n        return this._fixedLayout;\n    }\n    set fixedLayout(value) {\n        this._fixedLayout = value;\n        // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n        this._forceRecalculateCellWidths = true;\n        this._stickyColumnStylesNeedReset = true;\n    }\n    _fixedLayout = false;\n    /**\n     * Emits when the table completes rendering a set of data rows based on the latest data from the\n     * data source, even if the set of rows is empty.\n     */\n    contentChanged = new EventEmitter();\n    // TODO(andrewseguin): Remove max value as the end index\n    //   and instead calculate the view on init and scroll.\n    /**\n     * Stream containing the latest information on what rows are being displayed on screen.\n     * Can be used by the data source to as a heuristic of what data should be provided.\n     *\n     * @docs-private\n     */\n    viewChange = new BehaviorSubject({\n        start: 0,\n        end: Number.MAX_VALUE,\n    });\n    // Outlets in the table's template where the header, data rows, and footer will be inserted.\n    _rowOutlet;\n    _headerRowOutlet;\n    _footerRowOutlet;\n    _noDataRowOutlet;\n    /**\n     * The column definitions provided by the user that contain what the header, data, and footer\n     * cells should render for each column.\n     */\n    _contentColumnDefs;\n    /** Set of data row definitions that were provided to the table as content children. */\n    _contentRowDefs;\n    /** Set of header row definitions that were provided to the table as content children. */\n    _contentHeaderRowDefs;\n    /** Set of footer row definitions that were provided to the table as content children. */\n    _contentFooterRowDefs;\n    /** Row definition that will only be rendered if there's no data in the table. */\n    _noDataRow;\n    _injector = inject(Injector);\n    constructor() {\n        const role = inject(new HostAttributeToken('role'), { optional: true });\n        if (!role) {\n            this._elementRef.nativeElement.setAttribute('role', 'table');\n        }\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n        // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n        // the user has provided a custom trackBy, return the result of that function as evaluated\n        // with the values of the `RenderRow`'s data and index.\n        this._dataDiffer = this._differs.find([]).create((_i, dataRow) => {\n            return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n        });\n    }\n    ngOnInit() {\n        this._setupStickyStyler();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(() => {\n            this._forceRecalculateCellWidths = true;\n        });\n    }\n    ngAfterContentInit() {\n        this._hasInitialized = true;\n    }\n    ngAfterContentChecked() {\n        // Only start re-rendering in `ngAfterContentChecked` after the first render.\n        if (this._canRender()) {\n            this._render();\n        }\n    }\n    ngOnDestroy() {\n        this._stickyStyler?.destroy();\n        [\n            this._rowOutlet?.viewContainer,\n            this._headerRowOutlet?.viewContainer,\n            this._footerRowOutlet?.viewContainer,\n            this._cachedRenderRowsMap,\n            this._customColumnDefs,\n            this._customRowDefs,\n            this._customHeaderRowDefs,\n            this._customFooterRowDefs,\n            this._columnDefsByName,\n        ].forEach((def) => {\n            def?.clear();\n        });\n        this._headerRowDefs = [];\n        this._footerRowDefs = [];\n        this._defaultRowDef = null;\n        this._onDestroy.next();\n        this._onDestroy.complete();\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n    }\n    /**\n     * Renders rows based on the table's latest set of data, which was either provided directly as an\n     * input or retrieved through an Observable stream (directly or from a DataSource).\n     * Checks for differences in the data since the last diff to perform only the necessary\n     * changes (add/remove/move rows).\n     *\n     * If the table's data source is a DataSource or Observable, this will be invoked automatically\n     * each time the provided Observable stream emits a new data array. Otherwise if your data is\n     * an array, this function will need to be called to render any changes.\n     */\n    renderRows() {\n        this._renderRows = this._getAllRenderRows();\n        const changes = this._dataDiffer.diff(this._renderRows);\n        if (!changes) {\n            this._updateNoDataRow();\n            this.contentChanged.next();\n            return;\n        }\n        const viewContainer = this._rowOutlet.viewContainer;\n        this._viewRepeater.applyChanges(changes, viewContainer, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record.item, currentIndex), record => record.item.data, (change) => {\n            if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n                this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n            }\n        });\n        // Update the meta context of a row's context data (index, count, first, last, ...)\n        this._updateRowIndexContext();\n        // Update rows that did not get added/removed/moved but may have had their identity changed,\n        // e.g. if trackBy matched data on some property but the actual data reference changed.\n        changes.forEachIdentityChange((record) => {\n            const rowView = viewContainer.get(record.currentIndex);\n            rowView.context.$implicit = record.item.data;\n        });\n        this._updateNoDataRow();\n        this.contentChanged.next();\n        this.updateStickyColumnStyles();\n    }\n    /** Adds a column definition that was not included as part of the content children. */\n    addColumnDef(columnDef) {\n        this._customColumnDefs.add(columnDef);\n    }\n    /** Removes a column definition that was not included as part of the content children. */\n    removeColumnDef(columnDef) {\n        this._customColumnDefs.delete(columnDef);\n    }\n    /** Adds a row definition that was not included as part of the content children. */\n    addRowDef(rowDef) {\n        this._customRowDefs.add(rowDef);\n    }\n    /** Removes a row definition that was not included as part of the content children. */\n    removeRowDef(rowDef) {\n        this._customRowDefs.delete(rowDef);\n    }\n    /** Adds a header row definition that was not included as part of the content children. */\n    addHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.add(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Removes a header row definition that was not included as part of the content children. */\n    removeHeaderRowDef(headerRowDef) {\n        this._customHeaderRowDefs.delete(headerRowDef);\n        this._headerRowDefChanged = true;\n    }\n    /** Adds a footer row definition that was not included as part of the content children. */\n    addFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.add(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Removes a footer row definition that was not included as part of the content children. */\n    removeFooterRowDef(footerRowDef) {\n        this._customFooterRowDefs.delete(footerRowDef);\n        this._footerRowDefChanged = true;\n    }\n    /** Sets a no data row definition that was not included as a part of the content children. */\n    setNoDataRow(noDataRow) {\n        this._customNoDataRow = noDataRow;\n    }\n    /**\n     * Updates the header sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n     * automatically called when the header row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyHeaderRowStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        // Hide the thead element if there are no header rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const thead = closestTableSection(this._headerRowOutlet, 'thead');\n            if (thead) {\n                thead.style.display = headerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._headerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n        this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._headerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n     * automatically called when the footer row changes its displayed set of columns, or if its\n     * sticky input changes. May be called manually for cases where the cell content changes outside\n     * of these events.\n     */\n    updateStickyFooterRowStyles() {\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n        // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n        // required child `row`.\n        if (this._isNativeHtmlTable) {\n            const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n            if (tfoot) {\n                tfoot.style.display = footerRows.length ? '' : 'none';\n            }\n        }\n        const stickyStates = this._footerRowDefs.map(def => def.sticky);\n        this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n        this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n        this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n        // Reset the dirty state of the sticky input change since it has been used.\n        this._footerRowDefs.forEach(def => def.resetStickyChanged());\n    }\n    /**\n     * Updates the column sticky styles. First resets all applied styles with respect to the cells\n     * sticking to the left and right. Then sticky styles are added for the left and right according\n     * to the column definitions for each cell in each row. This is automatically called when\n     * the data source provides a new set of data or when a column definition changes its sticky\n     * input. May be called manually for cases where the cell content changes outside of these events.\n     */\n    updateStickyColumnStyles() {\n        const headerRows = this._getRenderedRows(this._headerRowOutlet);\n        const dataRows = this._getRenderedRows(this._rowOutlet);\n        const footerRows = this._getRenderedRows(this._footerRowOutlet);\n        // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n        // In a table using a fixed layout, row content won't affect column width, so sticky styles\n        // don't need to be cleared unless either the sticky column config changes or one of the row\n        // defs change.\n        if ((this._isNativeHtmlTable && !this._fixedLayout) || this._stickyColumnStylesNeedReset) {\n            // Clear the left and right positioning from all columns in the table across all rows since\n            // sticky columns span across all table sections (header, data, footer)\n            this._stickyStyler.clearStickyPositioning([...headerRows, ...dataRows, ...footerRows], ['left', 'right']);\n            this._stickyColumnStylesNeedReset = false;\n        }\n        // Update the sticky styles for each header row depending on the def's sticky state\n        headerRows.forEach((headerRow, i) => {\n            this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n        });\n        // Update the sticky styles for each data row depending on its def's sticky state\n        this._rowDefs.forEach(rowDef => {\n            // Collect all the rows rendered with this row definition.\n            const rows = [];\n            for (let i = 0; i < dataRows.length; i++) {\n                if (this._renderRows[i].rowDef === rowDef) {\n                    rows.push(dataRows[i]);\n                }\n            }\n            this._addStickyColumnStyles(rows, rowDef);\n        });\n        // Update the sticky styles for each footer row depending on the def's sticky state\n        footerRows.forEach((footerRow, i) => {\n            this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n        });\n        // Reset the dirty state of the sticky input change since it has been used.\n        Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n    }\n    /** Invoked whenever an outlet is created and has been assigned to the table. */\n    _outletAssigned() {\n        // Trigger the first render once all outlets have been assigned. We do it this way, as\n        // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n        // the next change detection will happen.\n        // Also we can't use queries to resolve the outlets, because they're wrapped in a\n        // conditional, so we have to rely on them being assigned via DI.\n        if (!this._hasAllOutlets &&\n            this._rowOutlet &&\n            this._headerRowOutlet &&\n            this._footerRowOutlet &&\n            this._noDataRowOutlet) {\n            this._hasAllOutlets = true;\n            // In some setups this may fire before `ngAfterContentInit`\n            // so we need a check here. See #28538.\n            if (this._canRender()) {\n                this._render();\n            }\n        }\n    }\n    /** Whether the table has all the information to start rendering. */\n    _canRender() {\n        return this._hasAllOutlets && this._hasInitialized;\n    }\n    /** Renders the table if its state has changed. */\n    _render() {\n        // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n        this._cacheRowDefs();\n        this._cacheColumnDefs();\n        // Make sure that the user has at least added header, footer, or data row def.\n        if (!this._headerRowDefs.length &&\n            !this._footerRowDefs.length &&\n            !this._rowDefs.length &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingRowDefsError();\n        }\n        // Render updates if the list of columns have been changed for the header, row, or footer defs.\n        const columnsChanged = this._renderUpdatedColumns();\n        const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n        // Ensure sticky column styles are reset if set to `true` elsewhere.\n        this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n        this._forceRecalculateCellWidths = rowDefsChanged;\n        // If the header row definition has been changed, trigger a render to the header row.\n        if (this._headerRowDefChanged) {\n            this._forceRenderHeaderRows();\n            this._headerRowDefChanged = false;\n        }\n        // If the footer row definition has been changed, trigger a render to the footer row.\n        if (this._footerRowDefChanged) {\n            this._forceRenderFooterRows();\n            this._footerRowDefChanged = false;\n        }\n        // If there is a data source and row definitions, connect to the data source unless a\n        // connection has already been made.\n        if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n            this._observeRenderChanges();\n        }\n        else if (this._stickyColumnStylesNeedReset) {\n            // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n            // called when it row data arrives. Otherwise, we need to call it proactively.\n            this.updateStickyColumnStyles();\n        }\n        this._checkStickyStates();\n    }\n    /**\n     * Get the list of RenderRow objects to render according to the current list of data and defined\n     * row definitions. If the previous list already contained a particular pair, it should be reused\n     * so that the differ equates their references.\n     */\n    _getAllRenderRows() {\n        const renderRows = [];\n        // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n        // new cache while unused ones can be picked up by garbage collection.\n        const prevCachedRenderRows = this._cachedRenderRowsMap;\n        this._cachedRenderRowsMap = new Map();\n        if (!this._data) {\n            return renderRows;\n        }\n        // For each data object, get the list of rows that should be rendered, represented by the\n        // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n        for (let i = 0; i < this._data.length; i++) {\n            let data = this._data[i];\n            const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n            if (!this._cachedRenderRowsMap.has(data)) {\n                this._cachedRenderRowsMap.set(data, new WeakMap());\n            }\n            for (let j = 0; j < renderRowsForData.length; j++) {\n                let renderRow = renderRowsForData[j];\n                const cache = this._cachedRenderRowsMap.get(renderRow.data);\n                if (cache.has(renderRow.rowDef)) {\n                    cache.get(renderRow.rowDef).push(renderRow);\n                }\n                else {\n                    cache.set(renderRow.rowDef, [renderRow]);\n                }\n                renderRows.push(renderRow);\n            }\n        }\n        return renderRows;\n    }\n    /**\n     * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n     * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n     * `(T, CdkRowDef)` pair.\n     */\n    _getRenderRowsForData(data, dataIndex, cache) {\n        const rowDefs = this._getRowDefs(data, dataIndex);\n        return rowDefs.map(rowDef => {\n            const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef) : [];\n            if (cachedRenderRows.length) {\n                const dataRow = cachedRenderRows.shift();\n                dataRow.dataIndex = dataIndex;\n                return dataRow;\n            }\n            else {\n                return { data, rowDef, dataIndex };\n            }\n        });\n    }\n    /** Update the map containing the content's column definitions. */\n    _cacheColumnDefs() {\n        this._columnDefsByName.clear();\n        const columnDefs = mergeArrayAndSet(this._getOwnDefs(this._contentColumnDefs), this._customColumnDefs);\n        columnDefs.forEach(columnDef => {\n            if (this._columnDefsByName.has(columnDef.name) &&\n                (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableDuplicateColumnNameError(columnDef.name);\n            }\n            this._columnDefsByName.set(columnDef.name, columnDef);\n        });\n    }\n    /** Update the list of all available row definitions that can be used. */\n    _cacheRowDefs() {\n        this._headerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentHeaderRowDefs), this._customHeaderRowDefs);\n        this._footerRowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentFooterRowDefs), this._customFooterRowDefs);\n        this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n        // After all row definitions are determined, find the row definition to be considered default.\n        const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n        if (!this.multiTemplateDataRows &&\n            defaultRowDefs.length > 1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMultipleDefaultRowDefsError();\n        }\n        this._defaultRowDef = defaultRowDefs[0];\n    }\n    /**\n     * Check if the header, data, or footer rows have changed what columns they want to display or\n     * whether the sticky states have changed for the header or footer. If there is a diff, then\n     * re-render that section.\n     */\n    _renderUpdatedColumns() {\n        const columnsDiffReducer = (acc, def) => {\n            // The differ should be run for every column, even if `acc` is already\n            // true (see #29922)\n            const diff = !!def.getColumnsDiff();\n            return acc || diff;\n        };\n        // Force re-render data rows if the list of column definitions have changed.\n        const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n        if (dataColumnsChanged) {\n            this._forceRenderDataRows();\n        }\n        // Force re-render header/footer rows if the list of column definitions have changed.\n        const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n        if (headerColumnsChanged) {\n            this._forceRenderHeaderRows();\n        }\n        const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n        if (footerColumnsChanged) {\n            this._forceRenderFooterRows();\n        }\n        return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n    }\n    /**\n     * Switch to the provided data source by resetting the data and unsubscribing from the current\n     * render change subscription if one exists. If the data source is null, interpret this by\n     * clearing the row outlet. Otherwise start listening for new data.\n     */\n    _switchDataSource(dataSource) {\n        this._data = [];\n        if (isDataSource(this.dataSource)) {\n            this.dataSource.disconnect(this);\n        }\n        // Stop listening for data from the previous data source.\n        if (this._renderChangeSubscription) {\n            this._renderChangeSubscription.unsubscribe();\n            this._renderChangeSubscription = null;\n        }\n        if (!dataSource) {\n            if (this._dataDiffer) {\n                this._dataDiffer.diff([]);\n            }\n            if (this._rowOutlet) {\n                this._rowOutlet.viewContainer.clear();\n            }\n        }\n        this._dataSource = dataSource;\n    }\n    /** Set up a subscription for the data provided by the data source. */\n    _observeRenderChanges() {\n        // If no data source has been set, there is nothing to observe for changes.\n        if (!this.dataSource) {\n            return;\n        }\n        let dataStream;\n        if (isDataSource(this.dataSource)) {\n            dataStream = this.dataSource.connect(this);\n        }\n        else if (isObservable(this.dataSource)) {\n            dataStream = this.dataSource;\n        }\n        else if (Array.isArray(this.dataSource)) {\n            dataStream = of(this.dataSource);\n        }\n        if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableUnknownDataSourceError();\n        }\n        this._renderChangeSubscription = dataStream\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(data => {\n            this._data = data || [];\n            this.renderRows();\n        });\n    }\n    /**\n     * Clears any existing content in the header row outlet and creates a new embedded view\n     * in the outlet using the header row definition.\n     */\n    _forceRenderHeaderRows() {\n        // Clear the header row outlet if any content exists.\n        if (this._headerRowOutlet.viewContainer.length > 0) {\n            this._headerRowOutlet.viewContainer.clear();\n        }\n        this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n        this.updateStickyHeaderRowStyles();\n    }\n    /**\n     * Clears any existing content in the footer row outlet and creates a new embedded view\n     * in the outlet using the footer row definition.\n     */\n    _forceRenderFooterRows() {\n        // Clear the footer row outlet if any content exists.\n        if (this._footerRowOutlet.viewContainer.length > 0) {\n            this._footerRowOutlet.viewContainer.clear();\n        }\n        this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n        this.updateStickyFooterRowStyles();\n    }\n    /** Adds the sticky column styles for the rows according to the columns' stick states. */\n    _addStickyColumnStyles(rows, rowDef) {\n        const columnDefs = Array.from(rowDef?.columns || []).map(columnName => {\n            const columnDef = this._columnDefsByName.get(columnName);\n            if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnName);\n            }\n            return columnDef;\n        });\n        const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n        const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n        this._stickyStyler.updateStickyColumns(rows, stickyStartStates, stickyEndStates, !this._fixedLayout || this._forceRecalculateCellWidths);\n    }\n    /** Gets the list of rows that have been rendered in the row outlet. */\n    _getRenderedRows(rowOutlet) {\n        const renderedRows = [];\n        for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n            const viewRef = rowOutlet.viewContainer.get(i);\n            renderedRows.push(viewRef.rootNodes[0]);\n        }\n        return renderedRows;\n    }\n    /**\n     * Get the matching row definitions that should be used for this row data. If there is only\n     * one row definition, it is returned. Otherwise, find the row definitions that has a when\n     * predicate that returns true with the data. If none return true, return the default row\n     * definition.\n     */\n    _getRowDefs(data, dataIndex) {\n        if (this._rowDefs.length == 1) {\n            return [this._rowDefs[0]];\n        }\n        let rowDefs = [];\n        if (this.multiTemplateDataRows) {\n            rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n        }\n        else {\n            let rowDef = this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n            if (rowDef) {\n                rowDefs.push(rowDef);\n            }\n        }\n        if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableMissingMatchingRowDefError(data);\n        }\n        return rowDefs;\n    }\n    _getEmbeddedViewArgs(renderRow, index) {\n        const rowDef = renderRow.rowDef;\n        const context = { $implicit: renderRow.data };\n        return {\n            templateRef: rowDef.template,\n            context,\n            index,\n        };\n    }\n    /**\n     * Creates a new row template in the outlet and fills it with the set of cell templates.\n     * Optionally takes a context to provide to the row and cells, as well as an optional index\n     * of where to place the new row template in the outlet.\n     */\n    _renderRow(outlet, rowDef, index, context = {}) {\n        // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n        const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n        this._renderCellTemplateForItem(rowDef, context);\n        return view;\n    }\n    _renderCellTemplateForItem(rowDef, context) {\n        for (let cellTemplate of this._getCellTemplates(rowDef)) {\n            if (CdkCellOutlet.mostRecentCellOutlet) {\n                CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Updates the index-related context for each row to reflect any changes in the index of the rows,\n     * e.g. first/last/even/odd.\n     */\n    _updateRowIndexContext() {\n        const viewContainer = this._rowOutlet.viewContainer;\n        for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n            const viewRef = viewContainer.get(renderIndex);\n            const context = viewRef.context;\n            context.count = count;\n            context.first = renderIndex === 0;\n            context.last = renderIndex === count - 1;\n            context.even = renderIndex % 2 === 0;\n            context.odd = !context.even;\n            if (this.multiTemplateDataRows) {\n                context.dataIndex = this._renderRows[renderIndex].dataIndex;\n                context.renderIndex = renderIndex;\n            }\n            else {\n                context.index = this._renderRows[renderIndex].dataIndex;\n            }\n        }\n    }\n    /** Gets the column definitions for the provided row def. */\n    _getCellTemplates(rowDef) {\n        if (!rowDef || !rowDef.columns) {\n            return [];\n        }\n        return Array.from(rowDef.columns, columnId => {\n            const column = this._columnDefsByName.get(columnId);\n            if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getTableUnknownColumnError(columnId);\n            }\n            return rowDef.extractCellTemplate(column);\n        });\n    }\n    /**\n     * Forces a re-render of the data rows. Should be called in cases where there has been an input\n     * change that affects the evaluation of which rows should be rendered, e.g. toggling\n     * `multiTemplateDataRows` or adding/removing row definitions.\n     */\n    _forceRenderDataRows() {\n        this._dataDiffer.diff([]);\n        this._rowOutlet.viewContainer.clear();\n        this.renderRows();\n    }\n    /**\n     * Checks if there has been a change in sticky states since last check and applies the correct\n     * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n     * during a change detection and after the inputs are settled (after content check).\n     */\n    _checkStickyStates() {\n        const stickyCheckReducer = (acc, d) => {\n            return acc || d.hasStickyChanged();\n        };\n        // Note that the check needs to occur for every definition since it notifies the definition\n        // that it can reset its dirty state. Using another operator like `some` may short-circuit\n        // remaining definitions and leave them in an unchecked state.\n        if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyHeaderRowStyles();\n        }\n        if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n            this.updateStickyFooterRowStyles();\n        }\n        if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n            this._stickyColumnStylesNeedReset = true;\n            this.updateStickyColumnStyles();\n        }\n    }\n    /**\n     * Creates the sticky styler that will be used for sticky rows and columns. Listens\n     * for directionality changes and provides the latest direction to the styler. Re-applies column\n     * stickiness when directionality changes.\n     */\n    _setupStickyStyler() {\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._stickyStyler = new StickyStyler(this._isNativeHtmlTable, this.stickyCssClass, this._platform.isBrowser, this.needsPositionStickyOnElement, direction, this._stickyPositioningListener, this._injector);\n        (this._dir ? this._dir.change : of())\n            .pipe(takeUntil(this._onDestroy))\n            .subscribe(value => {\n            this._stickyStyler.direction = value;\n            this.updateStickyColumnStyles();\n        });\n    }\n    /** Filters definitions that belong to this table from a QueryList. */\n    _getOwnDefs(items) {\n        return items.filter(item => !item._table || item._table === this);\n    }\n    /** Creates or removes the no data row, depending on whether any data is being shown. */\n    _updateNoDataRow() {\n        const noDataRow = this._customNoDataRow || this._noDataRow;\n        if (!noDataRow) {\n            return;\n        }\n        const shouldShow = this._rowOutlet.viewContainer.length === 0;\n        if (shouldShow === this._isShowingNoDataRow) {\n            return;\n        }\n        const container = this._noDataRowOutlet.viewContainer;\n        if (shouldShow) {\n            const view = container.createEmbeddedView(noDataRow.templateRef);\n            const rootNode = view.rootNodes[0];\n            // Only add the attributes if we have a single root node since it's hard\n            // to figure out which one to add it to when there are multiple.\n            if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n                rootNode.setAttribute('role', 'row');\n                rootNode.classList.add(noDataRow._contentClassName);\n            }\n        }\n        else {\n            container.clear();\n        }\n        this._isShowingNoDataRow = shouldShow;\n        this._changeDetectorRef.markForCheck();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTable, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"20.0.0\", type: CdkTable, isStandalone: true, selector: \"cdk-table, table[cdk-table]\", inputs: { trackBy: \"trackBy\", dataSource: \"dataSource\", multiTemplateDataRows: [\"multiTemplateDataRows\", \"multiTemplateDataRows\", booleanAttribute], fixedLayout: [\"fixedLayout\", \"fixedLayout\", booleanAttribute] }, outputs: { contentChanged: \"contentChanged\" }, host: { properties: { \"class.cdk-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"cdk-table\" }, providers: [\n            { provide: CDK_TABLE, useExisting: CdkTable },\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], queries: [{ propertyName: \"_noDataRow\", first: true, predicate: CdkNoDataRow, descendants: true }, { propertyName: \"_contentColumnDefs\", predicate: CdkColumnDef, descendants: true }, { propertyName: \"_contentRowDefs\", predicate: CdkRowDef, descendants: true }, { propertyName: \"_contentHeaderRowDefs\", predicate: CdkHeaderRowDef, descendants: true }, { propertyName: \"_contentFooterRowDefs\", predicate: CdkFooterRowDef, descendants: true }], exportAs: [\"cdkTable\"], ngImport: i0, template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, isInline: true, styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"], dependencies: [{ kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-table, table[cdk-table]', exportAs: 'cdkTable', template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, host: {\n                        'class': 'cdk-table',\n                        '[class.cdk-table-fixed-layout]': 'fixedLayout',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, providers: [\n                        { provide: CDK_TABLE, useExisting: CdkTable },\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet], styles: [\".cdk-table-fixed-layout{table-layout:fixed}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { trackBy: [{\n                type: Input\n            }], dataSource: [{\n                type: Input\n            }], multiTemplateDataRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fixedLayout: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], contentChanged: [{\n                type: Output\n            }], _contentColumnDefs: [{\n                type: ContentChildren,\n                args: [CdkColumnDef, { descendants: true }]\n            }], _contentRowDefs: [{\n                type: ContentChildren,\n                args: [CdkRowDef, { descendants: true }]\n            }], _contentHeaderRowDefs: [{\n                type: ContentChildren,\n                args: [CdkHeaderRowDef, {\n                        descendants: true,\n                    }]\n            }], _contentFooterRowDefs: [{\n                type: ContentChildren,\n                args: [CdkFooterRowDef, {\n                        descendants: true,\n                    }]\n            }], _noDataRow: [{\n                type: ContentChild,\n                args: [CdkNoDataRow]\n            }] } });\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet(array, set) {\n    return array.concat(Array.from(set));\n}\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet, section) {\n    const uppercaseSection = section.toUpperCase();\n    let current = outlet.viewContainer.element.nativeElement;\n    while (current) {\n        // 1 is an element node.\n        const nodeName = current.nodeType === 1 ? current.nodeName : null;\n        if (nodeName === uppercaseSection) {\n            return current;\n        }\n        else if (nodeName === 'TABLE') {\n            // Stop traversing past the `table` node.\n            break;\n        }\n        current = current.parentNode;\n    }\n    return null;\n}\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass CdkTextColumn {\n    _table = inject(CdkTable, { optional: true });\n    _options = inject(TEXT_COLUMN_OPTIONS, { optional: true });\n    /** Column name that should be used to reference this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._name = name;\n        // With Ivy, inputs can be initialized before static query results are\n        // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n        this._syncColumnDefName();\n    }\n    _name;\n    /**\n     * Text label that should be used for the column header. If this property is not\n     * set, the header text will default to the column name with its first letter capitalized.\n     */\n    headerText;\n    /**\n     * Accessor function to retrieve the data rendered for each cell. If this\n     * property is not set, the data cells will render the value found in the data's property matching\n     * the column's name. For example, if the column is named `id`, then the rendered value will be\n     * value defined by the data's `id` property.\n     */\n    dataAccessor;\n    /** Alignment of the cell values. */\n    justify = 'start';\n    /** @docs-private */\n    columnDef;\n    /**\n     * The column cell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    cell;\n    /**\n     * The column headerCell is provided to the column during `ngOnInit` with a static query.\n     * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n     * column definition was provided in the same view as the table, which is not the case with this\n     * component.\n     * @docs-private\n     */\n    headerCell;\n    constructor() {\n        this._options = this._options || {};\n    }\n    ngOnInit() {\n        this._syncColumnDefName();\n        if (this.headerText === undefined) {\n            this.headerText = this._createDefaultHeaderText();\n        }\n        if (!this.dataAccessor) {\n            this.dataAccessor =\n                this._options.defaultDataAccessor || ((data, name) => data[name]);\n        }\n        if (this._table) {\n            // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n            // since the columnDef will not pick up its content by the time the table finishes checking\n            // its content and initializing the rows.\n            this.columnDef.cell = this.cell;\n            this.columnDef.headerCell = this.headerCell;\n            this._table.addColumnDef(this.columnDef);\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getTableTextColumnMissingParentTableError();\n        }\n    }\n    ngOnDestroy() {\n        if (this._table) {\n            this._table.removeColumnDef(this.columnDef);\n        }\n    }\n    /**\n     * Creates a default header text. Use the options' header text transformation function if one\n     * has been provided. Otherwise simply capitalize the column name.\n     */\n    _createDefaultHeaderText() {\n        const name = this.name;\n        if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getTableTextColumnMissingNameError();\n        }\n        if (this._options && this._options.defaultHeaderTextTransform) {\n            return this._options.defaultHeaderTextTransform(name);\n        }\n        return name[0].toUpperCase() + name.slice(1);\n    }\n    /** Synchronizes the column definition name with the text column name. */\n    _syncColumnDefName() {\n        if (this.columnDef) {\n            this.columnDef.name = this.name;\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTextColumn, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"20.0.0\", type: CdkTextColumn, isStandalone: true, selector: \"cdk-text-column\", inputs: { name: \"name\", headerText: \"headerText\", dataAccessor: \"dataAccessor\", justify: \"justify\" }, viewQueries: [{ propertyName: \"columnDef\", first: true, predicate: CdkColumnDef, descendants: true, static: true }, { propertyName: \"cell\", first: true, predicate: CdkCellDef, descendants: true, static: true }, { propertyName: \"headerCell\", first: true, predicate: CdkHeaderCellDef, descendants: true, static: true }], ngImport: i0, template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: CdkColumnDef, selector: \"[cdkColumnDef]\", inputs: [\"cdkColumnDef\", \"sticky\", \"stickyEnd\"] }, { kind: \"directive\", type: CdkHeaderCellDef, selector: \"[cdkHeaderCellDef]\" }, { kind: \"directive\", type: CdkHeaderCell, selector: \"cdk-header-cell, th[cdk-header-cell]\" }, { kind: \"directive\", type: CdkCellDef, selector: \"[cdkCellDef]\" }, { kind: \"directive\", type: CdkCell, selector: \"cdk-cell, td[cdk-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-text-column',\n                    template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n                }]\n        }], ctorParameters: () => [], propDecorators: { name: [{\n                type: Input\n            }], headerText: [{\n                type: Input\n            }], dataAccessor: [{\n                type: Input\n            }], justify: [{\n                type: Input\n            }], columnDef: [{\n                type: ViewChild,\n                args: [CdkColumnDef, { static: true }]\n            }], cell: [{\n                type: ViewChild,\n                args: [CdkCellDef, { static: true }]\n            }], headerCell: [{\n                type: ViewChild,\n                args: [CdkHeaderCellDef, { static: true }]\n            }] } });\n\nconst EXPORTED_DECLARATIONS = [\n    CdkTable,\n    CdkRowDef,\n    CdkCellDef,\n    CdkCellOutlet,\n    CdkHeaderCellDef,\n    CdkFooterCellDef,\n    CdkColumnDef,\n    CdkCell,\n    CdkRow,\n    CdkHeaderCell,\n    CdkFooterCell,\n    CdkHeaderRow,\n    CdkHeaderRowDef,\n    CdkFooterRow,\n    CdkFooterRowDef,\n    DataRowOutlet,\n    HeaderRowOutlet,\n    FooterRowOutlet,\n    CdkTextColumn,\n    CdkNoDataRow,\n    CdkRecycleRows,\n    NoDataRowOutlet,\n];\nclass CdkTableModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule, CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet], exports: [CdkTable,\n            CdkRowDef,\n            CdkCellDef,\n            CdkCellOutlet,\n            CdkHeaderCellDef,\n            CdkFooterCellDef,\n            CdkColumnDef,\n            CdkCell,\n            CdkRow,\n            CdkHeaderCell,\n            CdkFooterCell,\n            CdkHeaderRow,\n            CdkHeaderRowDef,\n            CdkFooterRow,\n            CdkFooterRowDef,\n            DataRowOutlet,\n            HeaderRowOutlet,\n            FooterRowOutlet,\n            CdkTextColumn,\n            CdkNoDataRow,\n            CdkRecycleRows,\n            NoDataRowOutlet] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTableModule, imports: [ScrollingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: CdkTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: EXPORTED_DECLARATIONS,\n                    imports: [ScrollingModule, ...EXPORTED_DECLARATIONS],\n                }]\n        }] });\n\nexport { BaseCdkCell, BaseRowDef, CDK_ROW_TEMPLATE, CDK_TABLE, CdkCell, CdkCellDef, CdkCellOutlet, CdkColumnDef, CdkFooterCell, CdkFooterCellDef, CdkFooterRow, CdkFooterRowDef, CdkHeaderCell, CdkHeaderCellDef, CdkHeaderRow, CdkHeaderRowDef, CdkNoDataRow, CdkRecycleRows, CdkRow, CdkRowDef, CdkTable, CdkTableModule, CdkTextColumn, DataRowOutlet, FooterRowOutlet, HeaderRowOutlet, NoDataRowOutlet, STICKY_POSITIONING_LISTENER, TEXT_COLUMN_OPTIONS };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,4BAA4B;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgC8BE,EAAE,CAAAC,YAAA,KAgkE7E,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhkE0EE,EAAE,CAAAG,cAAA,cAokEnE,CAAC;IApkEgEH,EAAE,CAAAI,kBAAA,KAqkEzD,CAAC;IArkEsDJ,EAAE,CAAAK,YAAA,CAskElF,CAAC;IAtkE+EL,EAAE,CAAAG,cAAA,cAukEnE,CAAC;IAvkEgEH,EAAE,CAAAI,kBAAA,KAwkE/D,CAAC,KACK,CAAC;IAzkEsDJ,EAAE,CAAAK,YAAA,CA0kElF,CAAC;IA1kE+EL,EAAE,CAAAG,cAAA,cA2kEnE,CAAC;IA3kEgEH,EAAE,CAAAI,kBAAA,KA4kEzD,CAAC;IA5kEsDJ,EAAE,CAAAK,YAAA,CA6kElF,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7kE+EE,EAAE,CAAAI,kBAAA,KA+kE3D,CAAC,KACP,CAAC,KACK,CAAC,KACD,CAAC;EAAA;AAAA;AAAA,SAAAG,4BAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAllEwDE,EAAE,CAAAG,cAAA,WAmyEvB,CAAC;IAnyEoBH,EAAE,CAAAQ,MAAA,EAqyE1F,CAAC;IAryEuFR,EAAE,CAAAK,YAAA,CAqyErF,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAW,MAAA,GAryEkFT,EAAE,CAAAU,aAAA;IAAFV,EAAE,CAAAW,WAAA,eAAAF,MAAA,CAAAG,OAmyExB,CAAC;IAnyEqBZ,EAAE,CAAAa,SAAA,CAqyE1F,CAAC;IAryEuFb,EAAE,CAAAc,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KAqyE1F,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAryEuFE,EAAE,CAAAG,cAAA,WAsyEzB,CAAC;IAtyEsBH,EAAE,CAAAQ,MAAA,EAwyE1F,CAAC;IAxyEuFR,EAAE,CAAAK,YAAA,CAwyErF,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAmB,OAAA,GAAAlB,GAAA,CAAAmB,SAAA;IAAA,MAAAT,MAAA,GAxyEkFT,EAAE,CAAAU,aAAA;IAAFV,EAAE,CAAAW,WAAA,eAAAF,MAAA,CAAAG,OAsyE1B,CAAC;IAtyEuBZ,EAAE,CAAAa,SAAA,CAwyE1F,CAAC;IAxyEuFb,EAAE,CAAAc,kBAAA,MAAAL,MAAA,CAAAU,YAAA,CAAAF,OAAA,EAAAR,MAAA,CAAAW,IAAA,MAwyE1F,CAAC;EAAA;AAAA;AAv0EN,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,OAAO,KAAKtB,EAAE,MAAM,eAAe;AACnC,SAASuB,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnW,SAASC,OAAO,EAAEC,eAAe,EAAEC,YAAY,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,sBAAsB,QAAQ,+CAA+C;AAC5J,SAASH,CAAC,IAAII,4BAA4B,QAAQ,+CAA+C;AACjG,SAASpC,CAAC,IAAIqC,cAAc,QAAQ,+BAA+B;AACnE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,aAAa,EAAEC,eAAe,QAAQ,iBAAiB;AAChE,OAAO,iBAAiB;AACxB,OAAO,wBAAwB;AAC/B,OAAO,0BAA0B;AACjC,OAAO,YAAY;;AAEnB;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAIxC,cAAc,CAAC,WAAW,CAAC;AACjD;AACA,MAAMyC,mBAAmB,GAAG,IAAIzC,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAM0C,UAAU,CAAC;EACb;EACAC,QAAQ,GAAG1C,MAAM,CAACC,WAAW,CAAC;EAC9B0C,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFL,UAAU;EAAA;EAC7G,OAAOM,IAAI,kBAD8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EACJR,UAAU;IAAAS,SAAA;IAAAC,UAAA;EAAA;AACrG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F5E,EAAE,CAAA6E,iBAAA,CAGJZ,UAAU,EAAc,CAAC;IACxGQ,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;EACAd,QAAQ,GAAG1C,MAAM,CAACC,WAAW,CAAC;EAC9B0C,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAa,yBAAAX,iBAAA;IAAA,YAAAA,iBAAA,IAAwFU,gBAAgB;EAAA;EACnH,OAAOT,IAAI,kBAlB8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAkBJO,gBAAgB;IAAAN,SAAA;IAAAC,UAAA;EAAA;AAC3G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApB6F5E,EAAE,CAAA6E,iBAAA,CAoBJG,gBAAgB,EAAc,CAAC;IAC9GP,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,CAAC;EACnB;EACAhB,QAAQ,GAAG1C,MAAM,CAACC,WAAW,CAAC;EAC9B0C,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAe,yBAAAb,iBAAA;IAAA,YAAAA,iBAAA,IAAwFY,gBAAgB;EAAA;EACnH,OAAOX,IAAI,kBAnC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAmCJS,gBAAgB;IAAAR,SAAA;IAAAC,UAAA;EAAA;AAC3G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArC6F5E,EAAE,CAAA6E,iBAAA,CAqCJK,gBAAgB,EAAc,CAAC;IAC9GT,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMK,YAAY,CAAC;EACfC,MAAM,GAAG7D,MAAM,CAACuC,SAAS,EAAE;IAAEuB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC9CC,iBAAiB,GAAG,KAAK;EACzB;EACA,IAAInE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoE,KAAK;EACrB;EACA,IAAIpE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACqE,aAAa,CAACrE,IAAI,CAAC;EAC5B;EACAoE,KAAK;EACL;EACA,IAAIE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACL,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAI,OAAO,GAAG,KAAK;EACf;AACJ;AACA;AACA;AACA;EACI,IAAIE,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACD,KAAK,EAAE;IACjB,IAAIA,KAAK,KAAK,IAAI,CAACE,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAGF,KAAK;MACvB,IAAI,CAACL,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAO,UAAU,GAAG,KAAK;EAClB;EACAC,IAAI;EACJ;EACAC,UAAU;EACV;EACAC,UAAU;EACV;AACJ;AACA;AACA;AACA;EACIC,oBAAoB;EACpB;AACJ;AACA;AACA;EACIC,mBAAmB;EACnBhC,WAAWA,CAAA,EAAG,CAAE;EAChB;EACAiC,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACb,iBAAiB;IAC/C,IAAI,CAACc,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACd,iBAAiB,GAAG,KAAK;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIe,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAACH,mBAAmB,GAAG,CAAC,cAAc,IAAI,CAACD,oBAAoB,EAAE,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIT,aAAaA,CAACG,KAAK,EAAE;IACjB;IACA;IACA,IAAIA,KAAK,EAAE;MACP,IAAI,CAACJ,KAAK,GAAGI,KAAK;MAClB,IAAI,CAACM,oBAAoB,GAAGN,KAAK,CAACW,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;MAC/D,IAAI,CAACD,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACA,OAAOlC,IAAI,YAAAoC,qBAAAlC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFc,YAAY;EAAA;EAC/G,OAAOb,IAAI,kBA1I8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA0IJW,YAAY;IAAAV,SAAA;IAAA+B,cAAA,WAAAC,4BAAA5G,EAAA,EAAAC,GAAA,EAAA4G,QAAA;MAAA,IAAA7G,EAAA;QA1IVE,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EA0I4V1C,UAAU;QA1IxWjE,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EA0Imb3B,gBAAgB;QA1IrchF,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EA0IghBzB,gBAAgB;MAAA;MAAA,IAAApF,EAAA;QAAA,IAAA+G,EAAA;QA1IliB7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAgG,IAAA,GAAAc,EAAA,CAAAG,KAAA;QAAFhH,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAiG,UAAA,GAAAa,EAAA,CAAAG,KAAA;QAAFhH,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAkG,UAAA,GAAAY,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,MAAA;MAAA7F,IAAA;MAAAsE,MAAA,0BA0IiI/D,gBAAgB;MAAAkE,SAAA,gCAAyClE,gBAAgB;IAAA;IAAAgD,UAAA;IAAAuC,QAAA,GA1I5MlH,EAAE,CAAAmH,kBAAA,CA0I0N,CAAC;MAAEC,OAAO,EAAE,4BAA4B;MAAEC,WAAW,EAAEjC;IAAa,CAAC,CAAC,GA1IlSpF,EAAE,CAAAsH,wBAAA;EAAA;AA2I/F;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KA5I6F5E,EAAE,CAAA6E,iBAAA,CA4IJO,YAAY,EAAc,CAAC;IAC1GX,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BwC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE,4BAA4B;QAAEC,WAAW,EAAEjC;MAAa,CAAC;IACpF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEhE,IAAI,EAAE,CAAC;MAC/CqD,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEY,MAAM,EAAE,CAAC;MACTjB,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAE0C,SAAS,EAAE7F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkE,SAAS,EAAE,CAAC;MACZpB,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAE0C,SAAS,EAAE7F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoE,IAAI,EAAE,CAAC;MACPtB,IAAI,EAAE5C,YAAY;MAClBiD,IAAI,EAAE,CAACb,UAAU;IACrB,CAAC,CAAC;IAAE+B,UAAU,EAAE,CAAC;MACbvB,IAAI,EAAE5C,YAAY;MAClBiD,IAAI,EAAE,CAACE,gBAAgB;IAC3B,CAAC,CAAC;IAAEiB,UAAU,EAAE,CAAC;MACbxB,IAAI,EAAE5C,YAAY;MAClBiD,IAAI,EAAE,CAACI,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMuC,WAAW,CAAC;EACdtD,WAAWA,CAACuD,SAAS,EAAEC,UAAU,EAAE;IAC/BA,UAAU,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGJ,SAAS,CAACvB,mBAAmB,CAAC;EAC5E;AACJ;AACA;AACA,MAAM4B,aAAa,SAASN,WAAW,CAAC;EACpCtD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC3C,MAAM,CAAC4D,YAAY,CAAC,EAAE5D,MAAM,CAACM,UAAU,CAAC,CAAC;EACnD;EACA,OAAOsC,IAAI,YAAA4D,sBAAA1D,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyD,aAAa;EAAA;EAChH,OAAOxD,IAAI,kBAjL8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAiLJsD,aAAa;IAAArD,SAAA;IAAAuD,SAAA,WAAsG,cAAc;IAAAtD,UAAA;IAAAuC,QAAA,GAjL/HlH,EAAE,CAAAkI,0BAAA;EAAA;AAkL/F;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAnL6F5E,EAAE,CAAA6E,iBAAA,CAmLJkD,aAAa,EAAc,CAAC;IAC3GtD,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDoD,IAAI,EAAE;QACF,OAAO,EAAE,iBAAiB;QAC1B,MAAM,EAAE;MACZ;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMC,aAAa,SAASX,WAAW,CAAC;EACpCtD,WAAWA,CAAA,EAAG;IACV,MAAMuD,SAAS,GAAGlG,MAAM,CAAC4D,YAAY,CAAC;IACtC,MAAMuC,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;IACrC,KAAK,CAAC4F,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMU,IAAI,GAAGX,SAAS,CAACrC,MAAM,EAAEiD,YAAY,CAAC,CAAC;IAC7C,IAAID,IAAI,EAAE;MACNV,UAAU,CAACC,aAAa,CAACW,YAAY,CAAC,MAAM,EAAEF,IAAI,CAAC;IACvD;EACJ;EACA,OAAOjE,IAAI,YAAAoE,sBAAAlE,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8D,aAAa;EAAA;EAChH,OAAO7D,IAAI,kBAzM8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAyMJ2D,aAAa;IAAA1D,SAAA;IAAAuD,SAAA;IAAAtD,UAAA;IAAAuC,QAAA,GAzMXlH,EAAE,CAAAkI,0BAAA;EAAA;AA0M/F;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KA3M6F5E,EAAE,CAAA6E,iBAAA,CA2MJuD,aAAa,EAAc,CAAC;IAC3G3D,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDoD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMM,OAAO,SAAShB,WAAW,CAAC;EAC9BtD,WAAWA,CAAA,EAAG;IACV,MAAMuD,SAAS,GAAGlG,MAAM,CAAC4D,YAAY,CAAC;IACtC,MAAMuC,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;IACrC,KAAK,CAAC4F,SAAS,EAAEC,UAAU,CAAC;IAC5B,MAAMU,IAAI,GAAGX,SAAS,CAACrC,MAAM,EAAEiD,YAAY,CAAC,CAAC;IAC7C,IAAID,IAAI,EAAE;MACNV,UAAU,CAACC,aAAa,CAACW,YAAY,CAAC,MAAM,EAAEF,IAAI,CAAC;IACvD;EACJ;EACA,OAAOjE,IAAI,YAAAsE,gBAAApE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmE,OAAO;EAAA;EAC1G,OAAOlE,IAAI,kBAhO8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAgOJgE,OAAO;IAAA/D,SAAA;IAAAuD,SAAA;IAAAtD,UAAA;IAAAuC,QAAA,GAhOLlH,EAAE,CAAAkI,0BAAA;EAAA;AAiO/F;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAlO6F5E,EAAE,CAAA6E,iBAAA,CAkOJ4D,OAAO,EAAc,CAAC;IACrGhE,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCoD,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMQ,gBAAgB,GAAG,6CAA6C;AACtE;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb1E,QAAQ,GAAG1C,MAAM,CAACC,WAAW,CAAC;EAC9BoH,QAAQ,GAAGrH,MAAM,CAACO,eAAe,CAAC;EAClC;EACA+G,OAAO;EACP;EACAC,cAAc;EACd5E,WAAWA,CAAA,EAAG,CAAE;EAChB6E,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;MACtB,MAAMD,OAAO,GAAIG,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,SAAS,CAAC,CAACC,YAAY,IAAK,EAAE;MAC7E,IAAI,CAACH,cAAc,GAAG,IAAI,CAACF,QAAQ,CAACM,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAAC,CAAC;MAC1D,IAAI,CAACL,cAAc,CAACM,IAAI,CAACP,OAAO,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACIQ,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACP,cAAc,CAACM,IAAI,CAAC,IAAI,CAACP,OAAO,CAAC;EACjD;EACA;EACAS,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,IAAI,YAAYC,eAAe,EAAE;MACjC,OAAOD,MAAM,CAACxD,UAAU,CAAC9B,QAAQ;IACrC;IACA,IAAI,IAAI,YAAYwF,eAAe,EAAE;MACjC,OAAOF,MAAM,CAACvD,UAAU,CAAC/B,QAAQ;IACrC,CAAC,MACI;MACD,OAAOsF,MAAM,CAACzD,IAAI,CAAC7B,QAAQ;IAC/B;EACJ;EACA,OAAOE,IAAI,YAAAuF,mBAAArF,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsE,UAAU;EAAA;EAC7G,OAAOrE,IAAI,kBA1R8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA0RJmE,UAAU;IAAAjE,UAAA;IAAAuC,QAAA,GA1RRlH,EAAE,CAAA4J,oBAAA;EAAA;AA2R/F;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KA5R6F5E,EAAE,CAAA6E,iBAAA,CA4RJ+D,UAAU,EAAc,CAAC;IACxGnE,IAAI,EAAE/C;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM+H,eAAe,SAASb,UAAU,CAAC;EACrCvD,MAAM,GAAG7D,MAAM,CAACuC,SAAS,EAAE;IAAEuB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC9CC,iBAAiB,GAAG,KAAK;EACzB;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACL,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAI,OAAO,GAAG,KAAK;EACfxB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC3C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACO,eAAe,CAAC,CAAC;EACvD;EACA;EACA;EACAiH,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACA7C,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACb,iBAAiB;IAC/C,IAAI,CAACc,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACd,iBAAiB,GAAG,KAAK;EAClC;EACA,OAAOnB,IAAI,YAAAyF,wBAAAvF,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmF,eAAe;EAAA;EAClH,OAAOlF,IAAI,kBApU8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAoUJgF,eAAe;IAAA/E,SAAA;IAAAuC,MAAA;MAAA6B,OAAA;MAAApD,MAAA,yCAAoJ/D,gBAAgB;IAAA;IAAAgD,UAAA;IAAAuC,QAAA,GApUjLlH,EAAE,CAAAsH,wBAAA,EAAFtH,EAAE,CAAAkI,0BAAA,EAAFlI,EAAE,CAAA4J,oBAAA;EAAA;AAqU/F;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KAtU6F5E,EAAE,CAAA6E,iBAAA,CAsUJ4E,eAAe,EAAc,CAAC;IAC7GhF,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BkC,MAAM,EAAE,CAAC;QAAE7F,IAAI,EAAE,SAAS;QAAE0I,KAAK,EAAE;MAAkB,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpE,MAAM,EAAE,CAAC;MACjDjB,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAEgF,KAAK,EAAE,uBAAuB;QAAEtC,SAAS,EAAE7F;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM+H,eAAe,SAASd,UAAU,CAAC;EACrCvD,MAAM,GAAG7D,MAAM,CAACuC,SAAS,EAAE;IAAEuB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC9CC,iBAAiB,GAAG,KAAK;EACzB;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO;EACvB;EACA,IAAID,MAAMA,CAACE,KAAK,EAAE;IACd,IAAIA,KAAK,KAAK,IAAI,CAACD,OAAO,EAAE;MACxB,IAAI,CAACA,OAAO,GAAGC,KAAK;MACpB,IAAI,CAACL,iBAAiB,GAAG,IAAI;IACjC;EACJ;EACAI,OAAO,GAAG,KAAK;EACfxB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC3C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACO,eAAe,CAAC,CAAC;EACvD;EACA;EACA;EACAiH,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,OAAO,CAAC;EAC9B;EACA;EACA7C,gBAAgBA,CAAA,EAAG;IACf,MAAMA,gBAAgB,GAAG,IAAI,CAACb,iBAAiB;IAC/C,IAAI,CAACc,kBAAkB,CAAC,CAAC;IACzB,OAAOD,gBAAgB;EAC3B;EACA;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACd,iBAAiB,GAAG,KAAK;EAClC;EACA,OAAOnB,IAAI,YAAA2F,wBAAAzF,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoF,eAAe;EAAA;EAClH,OAAOnF,IAAI,kBArX8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAqXJiF,eAAe;IAAAhF,SAAA;IAAAuC,MAAA;MAAA6B,OAAA;MAAApD,MAAA,yCAAoJ/D,gBAAgB;IAAA;IAAAgD,UAAA;IAAAuC,QAAA,GArXjLlH,EAAE,CAAAsH,wBAAA,EAAFtH,EAAE,CAAAkI,0BAAA,EAAFlI,EAAE,CAAA4J,oBAAA;EAAA;AAsX/F;AACA;EAAA,QAAAhF,SAAA,oBAAAA,SAAA,KAvX6F5E,EAAE,CAAA6E,iBAAA,CAuXJ6E,eAAe,EAAc,CAAC;IAC7GjF,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BkC,MAAM,EAAE,CAAC;QAAE7F,IAAI,EAAE,SAAS;QAAE0I,KAAK,EAAE;MAAkB,CAAC;IAC1D,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEpE,MAAM,EAAE,CAAC;MACjDjB,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAEgF,KAAK,EAAE,uBAAuB;QAAEtC,SAAS,EAAE7F;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMqI,SAAS,SAASpB,UAAU,CAAC;EAC/BvD,MAAM,GAAG7D,MAAM,CAACuC,SAAS,EAAE;IAAEuB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC9C;AACJ;AACA;AACA;AACA;AACA;EACI2E,IAAI;EACJ9F,WAAWA,CAAA,EAAG;IACV;IACA;IACA,KAAK,CAAC3C,MAAM,CAACC,WAAW,CAAC,EAAED,MAAM,CAACO,eAAe,CAAC,CAAC;EACvD;EACA,OAAOqC,IAAI,YAAA8F,kBAAA5F,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0F,SAAS;EAAA;EAC5G,OAAOzF,IAAI,kBArZ8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAqZJuF,SAAS;IAAAtF,SAAA;IAAAuC,MAAA;MAAA6B,OAAA;MAAAmB,IAAA;IAAA;IAAAtF,UAAA;IAAAuC,QAAA,GArZPlH,EAAE,CAAAkI,0BAAA;EAAA;AAsZ/F;AACA;EAAA,QAAAtD,SAAA,oBAAAA,SAAA,KAvZ6F5E,EAAE,CAAA6E,iBAAA,CAuZJmF,SAAS,EAAc,CAAC;IACvGvF,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBkC,MAAM,EAAE,CACJ;QAAE7F,IAAI,EAAE,SAAS;QAAE0I,KAAK,EAAE;MAAmB,CAAC,EAC9C;QAAE1I,IAAI,EAAE,MAAM;QAAE0I,KAAK,EAAE;MAAgB,CAAC;IAEhD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMK,aAAa,CAAC;EAChBC,cAAc,GAAG5I,MAAM,CAACQ,gBAAgB,CAAC;EACzC;EACAqI,KAAK;EACL;EACAC,OAAO;EACP;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,oBAAoB,GAAG,IAAI;EAClCpG,WAAWA,CAAA,EAAG;IACVgG,aAAa,CAACI,oBAAoB,GAAG,IAAI;EAC7C;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAIL,aAAa,CAACI,oBAAoB,KAAK,IAAI,EAAE;MAC7CJ,aAAa,CAACI,oBAAoB,GAAG,IAAI;IAC7C;EACJ;EACA,OAAOnG,IAAI,YAAAqG,sBAAAnG,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6F,aAAa;EAAA;EAChH,OAAO5F,IAAI,kBA9b8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA8bJ0F,aAAa;IAAAzF,SAAA;IAAAC,UAAA;EAAA;AACxG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhc6F5E,EAAE,CAAA6E,iBAAA,CAgcJsF,aAAa,EAAc,CAAC;IAC3G1F,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAM2F,YAAY,CAAC;EACf,OAAOtG,IAAI,YAAAuG,qBAAArG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoG,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBAzc8E5K,EAAE,CAAA6K,iBAAA;IAAApG,IAAA,EAycJiG,YAAY;IAAAhG,SAAA;IAAAuD,SAAA,WAAoG,KAAK;IAAAtD,UAAA;IAAAuC,QAAA,GAzcnHlH,EAAE,CAAA8K,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/G,QAAA,WAAAgH,sBAAApL,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAAI,kBAAA,KAyc4N,CAAC;MAAA;IAAA;IAAA+K,YAAA,GAA6DhB,aAAa;IAAAiB,aAAA;EAAA;AACtY;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KA3c6F5E,EAAE,CAAA6E,iBAAA,CA2cJ6F,YAAY,EAAc,CAAC;IAC1GjG,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9Cb,QAAQ,EAAEyE,gBAAgB;MAC1BR,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAkD,eAAe,EAAEnJ,uBAAuB,CAACoJ,OAAO;MAChDF,aAAa,EAAEjJ,iBAAiB,CAACoJ,IAAI;MACrCC,OAAO,EAAE,CAACrB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMsB,YAAY,CAAC;EACf,OAAOrH,IAAI,YAAAsH,qBAAApH,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmH,YAAY;EAAA;EAC/G,OAAOb,IAAI,kBA9d8E5K,EAAE,CAAA6K,iBAAA;IAAApG,IAAA,EA8dJgH,YAAY;IAAA/G,SAAA;IAAAuD,SAAA,WAAoG,KAAK;IAAAtD,UAAA;IAAAuC,QAAA,GA9dnHlH,EAAE,CAAA8K,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/G,QAAA,WAAAyH,sBAAA7L,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAAI,kBAAA,KA8d4N,CAAC;MAAA;IAAA;IAAA+K,YAAA,GAA6DhB,aAAa;IAAAiB,aAAA;EAAA;AACtY;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KAhe6F5E,EAAE,CAAA6E,iBAAA,CAgeJ4G,YAAY,EAAc,CAAC;IAC1GhH,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9Cb,QAAQ,EAAEyE,gBAAgB;MAC1BR,IAAI,EAAE;QACF,OAAO,EAAE,gBAAgB;QACzB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAkD,eAAe,EAAEnJ,uBAAuB,CAACoJ,OAAO;MAChDF,aAAa,EAAEjJ,iBAAiB,CAACoJ,IAAI;MACrCC,OAAO,EAAE,CAACrB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMyB,MAAM,CAAC;EACT,OAAOxH,IAAI,YAAAyH,eAAAvH,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsH,MAAM;EAAA;EACzG,OAAOhB,IAAI,kBAnf8E5K,EAAE,CAAA6K,iBAAA;IAAApG,IAAA,EAmfJmH,MAAM;IAAAlH,SAAA;IAAAuD,SAAA,WAAsF,KAAK;IAAAtD,UAAA;IAAAuC,QAAA,GAnf/FlH,EAAE,CAAA8K,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/G,QAAA,WAAA4H,gBAAAhM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAAI,kBAAA,KAmfiM,CAAC;MAAA;IAAA;IAAA+K,YAAA,GAA6DhB,aAAa;IAAAiB,aAAA;EAAA;AAC3W;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KArf6F5E,EAAE,CAAA6E,iBAAA,CAqfJ+G,MAAM,EAAc,CAAC;IACpGnH,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCb,QAAQ,EAAEyE,gBAAgB;MAC1BR,IAAI,EAAE;QACF,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAkD,eAAe,EAAEnJ,uBAAuB,CAACoJ,OAAO;MAChDF,aAAa,EAAEjJ,iBAAiB,CAACoJ,IAAI;MACrCC,OAAO,EAAE,CAACrB,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM4B,YAAY,CAAC;EACfC,WAAW,GAAGxK,MAAM,CAACC,WAAW,CAAC;EACjCwK,iBAAiB,GAAG,iBAAiB;EACrC9H,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAA8H,qBAAA5H,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyH,YAAY;EAAA;EAC/G,OAAOxH,IAAI,kBA3gB8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA2gBJsH,YAAY;IAAArH,SAAA;IAAAC,UAAA;EAAA;AACvG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7gB6F5E,EAAE,CAAA6E,iBAAA,CA6gBJkH,YAAY,EAAc,CAAC;IAC1GtH,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoH,iBAAiB,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,kBAAkB;EAClBC,aAAa;EACbC,UAAU;EACVC,6BAA6B;EAC7BC,SAAS;EACTC,iBAAiB;EACjBC,cAAc;EACdC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC9BC,eAAe,GAAGC,UAAU,EAAEC,cAAc,GACtC,IAAID,UAAU,CAACC,cAAc,CAACC,OAAO,IAAI,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC,CAAC,GAC1E,IAAI;EACVE,mCAAmC,GAAG,EAAE;EACxCC,2BAA2B,GAAG,IAAI;EAClCC,iBAAiB,GAAG,EAAE;EACtBC,cAAc;EACdC,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpJ,WAAWA,CAACkI,kBAAkB,EAAEC,aAAa,EAAEC,UAAU,GAAG,IAAI,EAAEC,6BAA6B,GAAG,IAAI,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,cAAc,EAAE;IAClJ,IAAI,CAACN,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACW,cAAc,GAAG;MAClB,KAAK,EAAE,GAAGhB,aAAa,kBAAkB;MACzC,QAAQ,EAAE,GAAGA,aAAa,qBAAqB;MAC/C,MAAM,EAAE,GAAGA,aAAa,mBAAmB;MAC3C,OAAO,EAAE,GAAGA,aAAa;IAC7B,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkB,sBAAsBA,CAACC,IAAI,EAAEC,gBAAgB,EAAE;IAC3C,IAAIA,gBAAgB,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAID,gBAAgB,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,IAAI,CAACC,kCAAkC,CAACH,IAAI,CAAC;IACjD;IACA,MAAMI,eAAe,GAAG,EAAE;IAC1B,KAAK,MAAMC,GAAG,IAAIL,IAAI,EAAE;MACpB;MACA;MACA,IAAIK,GAAG,CAACC,QAAQ,KAAKD,GAAG,CAACE,YAAY,EAAE;QACnC;MACJ;MACAH,eAAe,CAACI,IAAI,CAACH,GAAG,EAAE,GAAGI,KAAK,CAACC,IAAI,CAACL,GAAG,CAACM,QAAQ,CAAC,CAAC;IAC1D;IACA;IACAhM,eAAe,CAAC;MACZiM,KAAK,EAAEA,CAAA,KAAM;QACT,KAAK,MAAMC,OAAO,IAAIT,eAAe,EAAE;UACnC,IAAI,CAACU,kBAAkB,CAACD,OAAO,EAAEZ,gBAAgB,CAAC;QACtD;MACJ;IACJ,CAAC,EAAE;MACCc,QAAQ,EAAE,IAAI,CAAC7B;IACnB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8B,mBAAmBA,CAAChB,IAAI,EAAEiB,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAE;IACvG;IACA,IAAI,CAACpB,IAAI,CAACqB,MAAM,IACZ,CAAC,IAAI,CAACvC,UAAU,IAChB,EAAEmC,iBAAiB,CAACK,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,IAAIL,eAAe,CAACI,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC,CAAC,EAAE;MACnF,IAAI,CAACtC,iBAAiB,EAAEuC,oBAAoB,CAAC;QAAEC,KAAK,EAAE;MAAG,CAAC,CAAC;MAC3D,IAAI,CAACxC,iBAAiB,EAAEyC,uBAAuB,CAAC;QAAED,KAAK,EAAE;MAAG,CAAC,CAAC;MAC9D;IACJ;IACA;IACA,MAAME,QAAQ,GAAG3B,IAAI,CAAC,CAAC,CAAC;IACxB,MAAM4B,QAAQ,GAAGD,QAAQ,CAAChB,QAAQ,CAACU,MAAM;IACzC,MAAMQ,KAAK,GAAG,IAAI,CAAC7C,SAAS,KAAK,KAAK;IACtC,MAAM8C,KAAK,GAAGD,KAAK,GAAG,OAAO,GAAG,MAAM;IACtC,MAAME,GAAG,GAAGF,KAAK,GAAG,MAAM,GAAG,OAAO;IACpC,MAAMG,eAAe,GAAGf,iBAAiB,CAACgB,WAAW,CAAC,IAAI,CAAC;IAC3D,MAAMC,cAAc,GAAGhB,eAAe,CAACiB,OAAO,CAAC,IAAI,CAAC;IACpD,IAAIC,UAAU;IACd,IAAIC,cAAc;IAClB,IAAIC,YAAY;IAChB,IAAIlB,MAAM,EAAE;MACR,IAAI,CAACmB,8BAA8B,CAAC;QAChCvC,IAAI,EAAE,CAAC,GAAGA,IAAI,CAAC;QACfiB,iBAAiB,EAAE,CAAC,GAAGA,iBAAiB,CAAC;QACzCC,eAAe,EAAE,CAAC,GAAGA,eAAe;MACxC,CAAC,CAAC;IACN;IACAvM,eAAe,CAAC;MACZ6N,SAAS,EAAEA,CAAA,KAAM;QACbJ,UAAU,GAAG,IAAI,CAACK,cAAc,CAACd,QAAQ,EAAER,qBAAqB,CAAC;QACjEkB,cAAc,GAAG,IAAI,CAACK,8BAA8B,CAACN,UAAU,EAAEnB,iBAAiB,CAAC;QACnFqB,YAAY,GAAG,IAAI,CAACK,4BAA4B,CAACP,UAAU,EAAElB,eAAe,CAAC;MACjF,CAAC;MACDN,KAAK,EAAEA,CAAA,KAAM;QACT,KAAK,MAAMP,GAAG,IAAIL,IAAI,EAAE;UACpB,KAAK,IAAIhO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4P,QAAQ,EAAE5P,CAAC,EAAE,EAAE;YAC/B,MAAMsG,IAAI,GAAG+H,GAAG,CAACM,QAAQ,CAAC3O,CAAC,CAAC;YAC5B,IAAIiP,iBAAiB,CAACjP,CAAC,CAAC,EAAE;cACtB,IAAI,CAAC4Q,eAAe,CAACtK,IAAI,EAAEwJ,KAAK,EAAEO,cAAc,CAACrQ,CAAC,CAAC,EAAEA,CAAC,KAAKgQ,eAAe,CAAC;YAC/E;YACA,IAAId,eAAe,CAAClP,CAAC,CAAC,EAAE;cACpB,IAAI,CAAC4Q,eAAe,CAACtK,IAAI,EAAEyJ,GAAG,EAAEO,YAAY,CAACtQ,CAAC,CAAC,EAAEA,CAAC,KAAKkQ,cAAc,CAAC;YAC1E;UACJ;QACJ;QACA,IAAI,IAAI,CAACjD,iBAAiB,IAAImD,UAAU,CAACd,IAAI,CAACuB,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,EAAE;UACrD,IAAI,CAAC5D,iBAAiB,CAACuC,oBAAoB,CAAC;YACxCC,KAAK,EAAEO,eAAe,KAAK,CAAC,CAAC,GACvB,EAAE,GACFI,UAAU,CACPU,KAAK,CAAC,CAAC,EAAEd,eAAe,GAAG,CAAC,CAAC,CAC7Be,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAMhC,iBAAiB,CAACgC,KAAK,CAAC,GAAGD,KAAK,GAAG,IAAK;UAC5E,CAAC,CAAC;UACF,IAAI,CAAC/D,iBAAiB,CAACyC,uBAAuB,CAAC;YAC3CD,KAAK,EAAES,cAAc,KAAK,CAAC,CAAC,GACtB,EAAE,GACFE,UAAU,CACPU,KAAK,CAACZ,cAAc,CAAC,CACrBa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK/B,eAAe,CAAC+B,KAAK,GAAGf,cAAc,CAAC,GAAGc,KAAK,GAAG,IAAI,CAAC,CAC7EE,OAAO,CAAC;UACrB,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,EAAE;MACCnC,QAAQ,EAAE,IAAI,CAAC7B;IACnB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiE,SAASA,CAACC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IAC3C;IACA,IAAI,CAAC,IAAI,CAACxE,UAAU,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,MAAMkB,IAAI,GAAGsD,QAAQ,KAAK,QAAQ,GAAGF,WAAW,CAACN,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGE,WAAW;IAChF,MAAMG,MAAM,GAAGD,QAAQ,KAAK,QAAQ,GAAGD,YAAY,CAACP,KAAK,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGG,YAAY;IACpF;IACA,MAAMG,aAAa,GAAG,EAAE;IACxB,MAAMC,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,eAAe,GAAG,EAAE;IAC1B;IACA;IACA/O,eAAe,CAAC;MACZ6N,SAAS,EAAEA,CAAA,KAAM;QACb,KAAK,IAAImB,QAAQ,GAAG,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAED,QAAQ,GAAG3D,IAAI,CAACqB,MAAM,EAAEsC,QAAQ,EAAE,EAAE;UACzE,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;YACnB;UACJ;UACAH,aAAa,CAACG,QAAQ,CAAC,GAAGC,YAAY;UACtC,MAAMvD,GAAG,GAAGL,IAAI,CAAC2D,QAAQ,CAAC;UAC1BD,eAAe,CAACC,QAAQ,CAAC,GAAG,IAAI,CAAC/E,kBAAkB,GAC7C6B,KAAK,CAACC,IAAI,CAACL,GAAG,CAACM,QAAQ,CAAC,GACxB,CAACN,GAAG,CAAC;UACX,MAAMwD,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACzD,GAAG,CAAC,CAACwD,MAAM;UACpDD,YAAY,IAAIC,MAAM;UACtBJ,iBAAiB,CAACE,QAAQ,CAAC,GAAGE,MAAM;QACxC;MACJ,CAAC;MACDjD,KAAK,EAAEA,CAAA,KAAM;QACT,MAAMmD,gBAAgB,GAAGR,MAAM,CAACtB,WAAW,CAAC,IAAI,CAAC;QACjD,KAAK,IAAI0B,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG3D,IAAI,CAACqB,MAAM,EAAEsC,QAAQ,EAAE,EAAE;UACvD,IAAI,CAACJ,MAAM,CAACI,QAAQ,CAAC,EAAE;YACnB;UACJ;UACA,MAAMK,MAAM,GAAGR,aAAa,CAACG,QAAQ,CAAC;UACtC,MAAMM,kBAAkB,GAAGN,QAAQ,KAAKI,gBAAgB;UACxD,KAAK,MAAMlD,OAAO,IAAI6C,eAAe,CAACC,QAAQ,CAAC,EAAE;YAC7C,IAAI,CAACf,eAAe,CAAC/B,OAAO,EAAEyC,QAAQ,EAAEU,MAAM,EAAEC,kBAAkB,CAAC;UACvE;QACJ;QACA,IAAIX,QAAQ,KAAK,KAAK,EAAE;UACpB,IAAI,CAACrE,iBAAiB,EAAEiF,uBAAuB,CAAC;YAC5CzC,KAAK,EAAEgC,iBAAiB;YACxBU,OAAO,EAAEX,aAAa;YACtBY,QAAQ,EAAEV;UACd,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAI,CAACzE,iBAAiB,EAAEoF,uBAAuB,CAAC;YAC5C5C,KAAK,EAAEgC,iBAAiB;YACxBU,OAAO,EAAEX,aAAa;YACtBY,QAAQ,EAAEV;UACd,CAAC,CAAC;QACN;MACJ;IACJ,CAAC,EAAE;MACC3C,QAAQ,EAAE,IAAI,CAAC7B;IACnB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoF,2BAA2BA,CAACC,YAAY,EAAElB,YAAY,EAAE;IACpD,IAAI,CAAC,IAAI,CAACzE,kBAAkB,EAAE;MAC1B;IACJ;IACA;IACAjK,eAAe,CAAC;MACZiM,KAAK,EAAEA,CAAA,KAAM;QACT,MAAM4D,KAAK,GAAGD,YAAY,CAACE,aAAa,CAAC,OAAO,CAAC;QACjD,IAAID,KAAK,EAAE;UACP,IAAInB,YAAY,CAAC/B,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAAC,EAAE;YACpC,IAAI,CAACT,kBAAkB,CAAC0D,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;UAC9C,CAAC,MACI;YACD,IAAI,CAAC5B,eAAe,CAAC4B,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC;UACnD;QACJ;MACJ;IACJ,CAAC,EAAE;MACCzD,QAAQ,EAAE,IAAI,CAAC7B;IACnB,CAAC,CAAC;EACN;EACA;EACAwF,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC/E,2BAA2B,EAAE;MAClCgF,YAAY,CAAC,IAAI,CAAChF,2BAA2B,CAAC;IAClD;IACA,IAAI,CAACN,eAAe,EAAEuF,UAAU,CAAC,CAAC;IAClC,IAAI,CAAC9E,UAAU,GAAG,IAAI;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIgB,kBAAkBA,CAACD,OAAO,EAAEZ,gBAAgB,EAAE;IAC1C,IAAI,CAACY,OAAO,CAACzG,SAAS,CAACyK,QAAQ,CAAC,IAAI,CAAChG,aAAa,CAAC,EAAE;MACjD;IACJ;IACA,KAAK,MAAMiG,GAAG,IAAI7E,gBAAgB,EAAE;MAChCY,OAAO,CAACkE,KAAK,CAACD,GAAG,CAAC,GAAG,EAAE;MACvBjE,OAAO,CAACzG,SAAS,CAAC4K,MAAM,CAAC,IAAI,CAACnF,cAAc,CAACiF,GAAG,CAAC,CAAC;IACtD;IACA;IACA;IACA;IACA;IACA,MAAMG,YAAY,GAAGvG,iBAAiB,CAAC4C,IAAI,CAACwD,GAAG,IAAI7E,gBAAgB,CAACkC,OAAO,CAAC2C,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIjE,OAAO,CAACkE,KAAK,CAACD,GAAG,CAAC,CAAC;IAC9G,IAAIG,YAAY,EAAE;MACdpE,OAAO,CAACkE,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACtE,OAAO,CAAC;IAC7D,CAAC,MACI;MACD;MACAA,OAAO,CAACkE,KAAK,CAACG,MAAM,GAAG,EAAE;MACzB,IAAI,IAAI,CAACnG,6BAA6B,EAAE;QACpC8B,OAAO,CAACkE,KAAK,CAACzB,QAAQ,GAAG,EAAE;MAC/B;MACAzC,OAAO,CAACzG,SAAS,CAAC4K,MAAM,CAAC,IAAI,CAACnG,aAAa,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI+D,eAAeA,CAAC/B,OAAO,EAAEiE,GAAG,EAAEM,QAAQ,EAAEC,eAAe,EAAE;IACrDxE,OAAO,CAACzG,SAAS,CAACC,GAAG,CAAC,IAAI,CAACwE,aAAa,CAAC;IACzC,IAAIwG,eAAe,EAAE;MACjBxE,OAAO,CAACzG,SAAS,CAACC,GAAG,CAAC,IAAI,CAACwF,cAAc,CAACiF,GAAG,CAAC,CAAC;IACnD;IACAjE,OAAO,CAACkE,KAAK,CAACD,GAAG,CAAC,GAAG,GAAGM,QAAQ,IAAI;IACpCvE,OAAO,CAACkE,KAAK,CAACG,MAAM,GAAG,IAAI,CAACC,oBAAoB,CAACtE,OAAO,CAAC;IACzD,IAAI,IAAI,CAAC9B,6BAA6B,EAAE;MACpC8B,OAAO,CAACkE,KAAK,CAACO,OAAO,IAAI,8CAA8C;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,oBAAoBA,CAACtE,OAAO,EAAE;IAC1B,MAAM0E,gBAAgB,GAAG;MACrBC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACX,CAAC;IACD,IAAIT,MAAM,GAAG,CAAC;IACd;IACA;IACA;IACA,KAAK,MAAMJ,GAAG,IAAIpG,iBAAiB,EAAE;MACjC,IAAImC,OAAO,CAACkE,KAAK,CAACD,GAAG,CAAC,EAAE;QACpBI,MAAM,IAAIK,gBAAgB,CAACT,GAAG,CAAC;MACnC;IACJ;IACA,OAAOI,MAAM,GAAG,GAAGA,MAAM,EAAE,GAAG,EAAE;EACpC;EACA;EACAzC,cAAcA,CAACpC,GAAG,EAAEc,qBAAqB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAACvB,iBAAiB,CAACyB,MAAM,EAAE;MACzD,OAAO,IAAI,CAACzB,iBAAiB;IACjC;IACA,MAAMwC,UAAU,GAAG,EAAE;IACrB,MAAMwD,aAAa,GAAGvF,GAAG,CAACM,QAAQ;IAClC,KAAK,IAAI3O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4T,aAAa,CAACvE,MAAM,EAAErP,CAAC,EAAE,EAAE;MAC3C,MAAMsG,IAAI,GAAGsN,aAAa,CAAC5T,CAAC,CAAC;MAC7BoQ,UAAU,CAAC5B,IAAI,CAAC,IAAI,CAACsD,oBAAoB,CAACxL,IAAI,CAAC,CAAC0K,KAAK,CAAC;IAC1D;IACA,IAAI,CAACpD,iBAAiB,GAAGwC,UAAU;IACnC,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIM,8BAA8BA,CAACmD,MAAM,EAAExC,YAAY,EAAE;IACjD,MAAMyC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI/T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6T,MAAM,CAACxE,MAAM,EAAErP,CAAC,EAAE,EAAE;MACpC,IAAIqR,YAAY,CAACrR,CAAC,CAAC,EAAE;QACjB8T,SAAS,CAAC9T,CAAC,CAAC,GAAG+T,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAAC7T,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO8T,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;EACInD,4BAA4BA,CAACkD,MAAM,EAAExC,YAAY,EAAE;IAC/C,MAAMyC,SAAS,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI/T,CAAC,GAAG6T,MAAM,CAACxE,MAAM,EAAErP,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,IAAIqR,YAAY,CAACrR,CAAC,CAAC,EAAE;QACjB8T,SAAS,CAAC9T,CAAC,CAAC,GAAG+T,YAAY;QAC3BA,YAAY,IAAIF,MAAM,CAAC7T,CAAC,CAAC;MAC7B;IACJ;IACA,OAAO8T,SAAS;EACpB;EACA;AACJ;AACA;AACA;EACIhC,oBAAoBA,CAACjD,OAAO,EAAE;IAC1B,MAAMmF,UAAU,GAAG,IAAI,CAAC7G,cAAc,CAAC8G,GAAG,CAACpF,OAAO,CAAC;IACnD,IAAImF,UAAU,EAAE;MACZ,OAAOA,UAAU;IACrB;IACA,MAAME,UAAU,GAAGrF,OAAO,CAACsF,qBAAqB,CAAC,CAAC;IAClD,MAAMC,IAAI,GAAG;MAAEpD,KAAK,EAAEkD,UAAU,CAAClD,KAAK;MAAEa,MAAM,EAAEqC,UAAU,CAACrC;IAAO,CAAC;IACnE,IAAI,CAAC,IAAI,CAACxE,eAAe,EAAE;MACvB,OAAO+G,IAAI;IACf;IACA,IAAI,CAACjH,cAAc,CAACkH,GAAG,CAACxF,OAAO,EAAEuF,IAAI,CAAC;IACtC,IAAI,CAAC/G,eAAe,CAACiH,OAAO,CAACzF,OAAO,EAAE;MAAE0F,GAAG,EAAE;IAAa,CAAC,CAAC;IAC5D,OAAOH,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI7D,8BAA8BA,CAACiE,MAAM,EAAE;IACnC,IAAI,CAACrG,kCAAkC,CAACqG,MAAM,CAACxG,IAAI,CAAC;IACpD;IACA,IAAI,CAAC,IAAI,CAACL,2BAA2B,EAAE;MACnC,IAAI,CAACD,mCAAmC,CAACc,IAAI,CAACgG,MAAM,CAAC;IACzD;EACJ;EACA;EACArG,kCAAkCA,CAACH,IAAI,EAAE;IACrC,MAAMyG,OAAO,GAAG,IAAIC,GAAG,CAAC1G,IAAI,CAAC;IAC7B,KAAK,MAAM2G,MAAM,IAAI,IAAI,CAACjH,mCAAmC,EAAE;MAC3DiH,MAAM,CAAC3G,IAAI,GAAG2G,MAAM,CAAC3G,IAAI,CAAC4G,MAAM,CAACvG,GAAG,IAAI,CAACoG,OAAO,CAACI,GAAG,CAACxG,GAAG,CAAC,CAAC;IAC9D;IACA,IAAI,CAACX,mCAAmC,GAAG,IAAI,CAACA,mCAAmC,CAACkH,MAAM,CAACD,MAAM,IAAI,CAAC,CAACA,MAAM,CAAC3G,IAAI,CAACqB,MAAM,CAAC;EAC9H;EACA;EACA5B,kBAAkBA,CAACD,OAAO,EAAE;IACxB,IAAIsH,iBAAiB,GAAG,KAAK;IAC7B,KAAK,MAAMC,KAAK,IAAIvH,OAAO,EAAE;MACzB,MAAMwH,QAAQ,GAAGD,KAAK,CAACE,aAAa,EAAE5F,MAAM,GACtC;QACE2B,KAAK,EAAE+D,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAACC,UAAU;QACxCrD,MAAM,EAAEkD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAACE;MACnC,CAAC,GACC;QACEnE,KAAK,EAAE+D,KAAK,CAACK,WAAW,CAACpE,KAAK;QAC9Ba,MAAM,EAAEkD,KAAK,CAACK,WAAW,CAACvD;MAC9B,CAAC;MACL,IAAImD,QAAQ,CAAChE,KAAK,KAAK,IAAI,CAAC7D,cAAc,CAAC8G,GAAG,CAACc,KAAK,CAACM,MAAM,CAAC,EAAErE,KAAK,IAC/DsE,MAAM,CAACP,KAAK,CAACM,MAAM,CAAC,EAAE;QACtBP,iBAAiB,GAAG,IAAI;MAC5B;MACA,IAAI,CAAC3H,cAAc,CAACkH,GAAG,CAACU,KAAK,CAACM,MAAM,EAAEL,QAAQ,CAAC;IACnD;IACA,IAAIF,iBAAiB,IAAI,IAAI,CAACpH,mCAAmC,CAAC2B,MAAM,EAAE;MACtE,IAAI,IAAI,CAAC1B,2BAA2B,EAAE;QAClCgF,YAAY,CAAC,IAAI,CAAChF,2BAA2B,CAAC;MAClD;MACA,IAAI,CAACA,2BAA2B,GAAG4H,UAAU,CAAC,MAAM;QAChD,IAAI,IAAI,CAACzH,UAAU,EAAE;UACjB;QACJ;QACA,KAAK,MAAM6G,MAAM,IAAI,IAAI,CAACjH,mCAAmC,EAAE;UAC3D,IAAI,CAACsB,mBAAmB,CAAC2F,MAAM,CAAC3G,IAAI,EAAE2G,MAAM,CAAC1F,iBAAiB,EAAE0F,MAAM,CAACzF,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC;QACxG;QACA,IAAI,CAACxB,mCAAmC,GAAG,EAAE;QAC7C,IAAI,CAACC,2BAA2B,GAAG,IAAI;MAC3C,CAAC,EAAE,CAAC,CAAC;IACT;EACJ;AACJ;AACA,SAAS2H,MAAMA,CAACzG,OAAO,EAAE;EACrB,OAAO,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACS,IAAI,CAACkG,KAAK,IAAI3G,OAAO,CAACzG,SAAS,CAACyK,QAAQ,CAAC2C,KAAK,CAAC,CAAC;AAC9G;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACC,EAAE,EAAE;EACpC,OAAOC,KAAK,CAAC,kCAAkCD,EAAE,IAAI,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,SAASE,gCAAgCA,CAACjU,IAAI,EAAE;EAC5C,OAAOgU,KAAK,CAAC,+CAA+ChU,IAAI,IAAI,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,SAASkU,mCAAmCA,CAAA,EAAG;EAC3C,OAAOF,KAAK,CAAC,sEAAsE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,SAASG,kCAAkCA,CAACC,IAAI,EAAE;EAC9C,OAAOJ,KAAK,CAAC,kDAAkD,GAC3D,sBAAsBK,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAE,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,OAAOP,KAAK,CAAC,mDAAmD,GAC5D,oDAAoD,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASQ,8BAA8BA,CAAA,EAAG;EACtC,OAAOR,KAAK,CAAC,wEAAwE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA,SAASS,yCAAyCA,CAAA,EAAG;EACjD,OAAOT,KAAK,CAAC,6DAA6D,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,SAASU,kCAAkCA,CAAA,EAAG;EAC1C,OAAOV,KAAK,CAAC,qCAAqC,CAAC;AACvD;;AAEA;AACA,MAAMW,2BAA2B,GAAG,IAAIxU,cAAc,CAAC,SAAS,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMyU,cAAc,CAAC;EACjB,OAAO5R,IAAI,YAAA6R,uBAAA3R,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0R,cAAc;EAAA;EACjH,OAAOzR,IAAI,kBArjC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAqjCJuR,cAAc;IAAAtR,SAAA;IAAAC,UAAA;IAAAuC,QAAA,GArjCZlH,EAAE,CAAAmH,kBAAA,CAqjC8G,CAAC;MAAEC,OAAO,EAAEhE,uBAAuB;MAAE8S,QAAQ,EAAE5S;IAA6B,CAAC,CAAC;EAAA;AAC3R;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KAvjC6F5E,EAAE,CAAA6E,iBAAA,CAujCJmR,cAAc,EAAc,CAAC;IAC5GvR,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEwC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEhE,uBAAuB;QAAE8S,QAAQ,EAAE5S;MAA6B,CAAC;IAC5F,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM6S,aAAa,CAAC;EAChBC,aAAa,GAAG5U,MAAM,CAACQ,gBAAgB,CAAC;EACxC2F,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;EAC/BqC,WAAWA,CAAA,EAAG;IACV,MAAMkS,KAAK,GAAG7U,MAAM,CAACuC,SAAS,CAAC;IAC/BsS,KAAK,CAACC,UAAU,GAAG,IAAI;IACvBD,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA,OAAOnS,IAAI,YAAAoS,sBAAAlS,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6R,aAAa;EAAA;EAChH,OAAO5R,IAAI,kBA3kC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA2kCJ0R,aAAa;IAAAzR,SAAA;IAAAC,UAAA;EAAA;AACxG;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7kC6F5E,EAAE,CAAA6E,iBAAA,CA6kCJsR,aAAa,EAAc,CAAC;IAC3G1R,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM0R,eAAe,CAAC;EAClBL,aAAa,GAAG5U,MAAM,CAACQ,gBAAgB,CAAC;EACxC2F,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;EAC/BqC,WAAWA,CAAA,EAAG;IACV,MAAMkS,KAAK,GAAG7U,MAAM,CAACuC,SAAS,CAAC;IAC/BsS,KAAK,CAACK,gBAAgB,GAAG,IAAI;IAC7BL,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA,OAAOnS,IAAI,YAAAuS,wBAAArS,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmS,eAAe;EAAA;EAClH,OAAOlS,IAAI,kBAhmC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAgmCJgS,eAAe;IAAA/R,SAAA;IAAAC,UAAA;EAAA;AAC1G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlmC6F5E,EAAE,CAAA6E,iBAAA,CAkmCJ4R,eAAe,EAAc,CAAC;IAC7GhS,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM6R,eAAe,CAAC;EAClBR,aAAa,GAAG5U,MAAM,CAACQ,gBAAgB,CAAC;EACxC2F,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;EAC/BqC,WAAWA,CAAA,EAAG;IACV,MAAMkS,KAAK,GAAG7U,MAAM,CAACuC,SAAS,CAAC;IAC/BsS,KAAK,CAACQ,gBAAgB,GAAG,IAAI;IAC7BR,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA,OAAOnS,IAAI,YAAA0S,wBAAAxS,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsS,eAAe;EAAA;EAClH,OAAOrS,IAAI,kBArnC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EAqnCJmS,eAAe;IAAAlS,SAAA;IAAAC,UAAA;EAAA;AAC1G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvnC6F5E,EAAE,CAAA6E,iBAAA,CAunCJ+R,eAAe,EAAc,CAAC;IAC7GnS,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,MAAMgS,eAAe,CAAC;EAClBX,aAAa,GAAG5U,MAAM,CAACQ,gBAAgB,CAAC;EACxC2F,UAAU,GAAGnG,MAAM,CAACM,UAAU,CAAC;EAC/BqC,WAAWA,CAAA,EAAG;IACV,MAAMkS,KAAK,GAAG7U,MAAM,CAACuC,SAAS,CAAC;IAC/BsS,KAAK,CAACW,gBAAgB,GAAG,IAAI;IAC7BX,KAAK,CAACE,eAAe,CAAC,CAAC;EAC3B;EACA,OAAOnS,IAAI,YAAA6S,wBAAA3S,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyS,eAAe;EAAA;EAClH,OAAOxS,IAAI,kBA3oC8EvE,EAAE,CAAAwE,iBAAA;IAAAC,IAAA,EA2oCJsS,eAAe;IAAArS,SAAA;IAAAC,UAAA;EAAA;AAC1G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7oC6F5E,EAAE,CAAA6E,iBAAA,CA6oCJkS,eAAe,EAAc,CAAC;IAC7GtS,IAAI,EAAE/C,SAAS;IACfoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmS,QAAQ,CAAC;EACXrO,QAAQ,GAAGrH,MAAM,CAACO,eAAe,CAAC;EAClCoV,kBAAkB,GAAG3V,MAAM,CAACa,iBAAiB,CAAC;EAC9C+U,WAAW,GAAG5V,MAAM,CAACM,UAAU,CAAC;EAChCuV,IAAI,GAAG7V,MAAM,CAACkC,cAAc,EAAE;IAAE4B,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjDgS,SAAS,GAAG9V,MAAM,CAACoC,QAAQ,CAAC;EAC5B2T,aAAa,GAAG/V,MAAM,CAAC4B,uBAAuB,CAAC;EAC/CoU,cAAc,GAAGhW,MAAM,CAACqC,aAAa,CAAC;EACtC4T,0BAA0B,GAAGjW,MAAM,CAACuU,2BAA2B,EAAE;IAAEzQ,QAAQ,EAAE,IAAI;IAAEoS,QAAQ,EAAE;EAAK,CAAC,CAAC;EACpGC,SAAS,GAAGnW,MAAM,CAACc,QAAQ,CAAC;EAC5B;EACAsV,KAAK;EACL;EACAC,UAAU,GAAG,IAAI/U,OAAO,CAAC,CAAC;EAC1B;EACAgV,WAAW;EACX;EACAC,yBAAyB;EACzB;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,cAAc;EACd;EACAC,WAAW;EACX;EACAC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAIpE,GAAG,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACIqE,cAAc,GAAG,IAAIrE,GAAG,CAAC,CAAC;EAC1B;AACJ;AACA;AACA;AACA;EACIsE,oBAAoB,GAAG,IAAItE,GAAG,CAAC,CAAC;EAChC;AACJ;AACA;AACA;AACA;EACIuE,oBAAoB,GAAG,IAAIvE,GAAG,CAAC,CAAC;EAChC;EACAwE,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,oBAAoB,GAAG,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,oBAAoB,GAAG,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,4BAA4B,GAAG,IAAI;EACnC;AACJ;AACA;AACA;AACA;EACIC,2BAA2B,GAAG,IAAI;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoB,GAAG,IAAIf,GAAG,CAAC,CAAC;EAChC;EACA5L,kBAAkB;EAClB;AACJ;AACA;AACA;EACI4M,aAAa;EACb;AACJ;AACA;AACA;EACIC,cAAc,GAAG,kBAAkB;EACnC;AACJ;AACA;AACA;AACA;EACIC,4BAA4B,GAAG,IAAI;EACnC;EACAC,SAAS;EACT;EACAC,mBAAmB,GAAG,KAAK;EAC3B;EACAC,cAAc,GAAG,KAAK;EACtB;EACAC,eAAe,GAAG,KAAK;EACvB;EACAjR,YAAYA,CAAA,EAAG;IACX;IACA,IAAI,IAAI,CAACkR,iBAAiB,KAAKC,SAAS,EAAE;MACtC;MACA;MACA,MAAMC,SAAS,GAAG,IAAI,CAACtC,WAAW,CAACxP,aAAa,CAAC+R,YAAY,CAAC,MAAM,CAAC;MACrE,OAAOD,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;IACjF;IACA,OAAO,IAAI,CAACF,iBAAiB;EACjC;EACAA,iBAAiB,GAAGC,SAAS;EAC7B;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,OAAOA,CAACE,EAAE,EAAE;IACZ,IAAI,CAAC,OAAOlV,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKkV,EAAE,IAAI,IAAI,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC3FC,OAAO,CAACC,IAAI,CAAC,4CAA4CvE,IAAI,CAACC,SAAS,CAACoE,EAAE,CAAC,GAAG,CAAC;IACnF;IACA,IAAI,CAACD,UAAU,GAAGC,EAAE;EACxB;EACAD,UAAU;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAII,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,IAAI,CAACC,WAAW,KAAKD,UAAU,EAAE;MACjC,IAAI,CAACE,iBAAiB,CAACF,UAAU,CAAC;IACtC;EACJ;EACAC,WAAW;EACX;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,sBAAsB;EACtC;EACA,IAAID,qBAAqBA,CAACxU,KAAK,EAAE;IAC7B,IAAI,CAACyU,sBAAsB,GAAGzU,KAAK;IACnC;IACA;IACA,IAAI,IAAI,CAAC0Q,UAAU,IAAI,IAAI,CAACA,UAAU,CAACF,aAAa,CAACtH,MAAM,EAAE;MACzD,IAAI,CAACwL,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACAF,sBAAsB,GAAG,KAAK;EAC9B;AACJ;AACA;AACA;EACI,IAAIG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAC5U,KAAK,EAAE;IACnB,IAAI,CAAC6U,YAAY,GAAG7U,KAAK;IACzB;IACA,IAAI,CAACmT,2BAA2B,GAAG,IAAI;IACvC,IAAI,CAACD,4BAA4B,GAAG,IAAI;EAC5C;EACA2B,YAAY,GAAG,KAAK;EACpB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAInY,YAAY,CAAC,CAAC;EACnC;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoY,UAAU,GAAG,IAAI5X,eAAe,CAAC;IAC7BwM,KAAK,EAAE,CAAC;IACRC,GAAG,EAAEoL,MAAM,CAACC;EAChB,CAAC,CAAC;EACF;EACAvE,UAAU;EACVI,gBAAgB;EAChBG,gBAAgB;EAChBG,gBAAgB;EAChB;AACJ;AACA;AACA;EACI8D,kBAAkB;EAClB;EACAC,eAAe;EACf;EACAC,qBAAqB;EACrB;EACAC,qBAAqB;EACrB;EACAC,UAAU;EACVC,SAAS,GAAG3Z,MAAM,CAACgB,QAAQ,CAAC;EAC5B2B,WAAWA,CAAA,EAAG;IACV,MAAMkE,IAAI,GAAG7G,MAAM,CAAC,IAAIiB,kBAAkB,CAAC,MAAM,CAAC,EAAE;MAAE6C,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAAC+C,IAAI,EAAE;MACP,IAAI,CAAC+O,WAAW,CAACxP,aAAa,CAACW,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;IAChE;IACA,IAAI,CAAC6Q,SAAS,GAAG,CAAC,IAAI,CAAC9B,SAAS,CAAC8D,SAAS;IAC1C,IAAI,CAAC/O,kBAAkB,GAAG,IAAI,CAAC+K,WAAW,CAACxP,aAAa,CAACyT,QAAQ,KAAK,OAAO;IAC7E;IACA;IACA;IACA,IAAI,CAAChD,WAAW,GAAG,IAAI,CAACxP,QAAQ,CAACM,IAAI,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACkS,EAAE,EAAEC,OAAO,KAAK;MAC9D,OAAO,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC2B,OAAO,CAACC,SAAS,EAAED,OAAO,CAAC/F,IAAI,CAAC,GAAG+F,OAAO;IACjF,CAAC,CAAC;EACN;EACAE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAClE,cAAc,CACdmE,MAAM,CAAC,CAAC,CACRC,IAAI,CAAC1Y,SAAS,CAAC,IAAI,CAAC2U,UAAU,CAAC,CAAC,CAChCgE,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC9C,2BAA2B,GAAG,IAAI;IAC3C,CAAC,CAAC;EACN;EACA+C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvC,eAAe,GAAG,IAAI;EAC/B;EACAwC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB;EACJ;EACAzR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyO,aAAa,EAAE9G,OAAO,CAAC,CAAC;IAC7B,CACI,IAAI,CAACmE,UAAU,EAAEF,aAAa,EAC9B,IAAI,CAACM,gBAAgB,EAAEN,aAAa,EACpC,IAAI,CAACS,gBAAgB,EAAET,aAAa,EACpC,IAAI,CAAC4C,oBAAoB,EACzB,IAAI,CAACT,iBAAiB,EACtB,IAAI,CAACC,cAAc,EACnB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACC,oBAAoB,EACzB,IAAI,CAACV,iBAAiB,CACzB,CAACkE,OAAO,CAAEC,GAAG,IAAK;MACfA,GAAG,EAAEC,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAI,CAACjE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACT,UAAU,CAACwE,IAAI,CAAC,CAAC;IACtB,IAAI,CAACxE,UAAU,CAACyE,QAAQ,CAAC,CAAC;IAC1B,IAAI5c,YAAY,CAAC,IAAI,CAACua,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAAC5H,UAAU,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkK,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzE,WAAW,GAAG,IAAI,CAAC0E,iBAAiB,CAAC,CAAC;IAC3C,MAAMvT,OAAO,GAAG,IAAI,CAACoP,WAAW,CAAChP,IAAI,CAAC,IAAI,CAACyO,WAAW,CAAC;IACvD,IAAI,CAAC7O,OAAO,EAAE;MACV,IAAI,CAACwT,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC/B,cAAc,CAAC2B,IAAI,CAAC,CAAC;MAC1B;IACJ;IACA,MAAMjG,aAAa,GAAG,IAAI,CAACE,UAAU,CAACF,aAAa;IACnD,IAAI,CAACmB,aAAa,CAACmF,YAAY,CAACzT,OAAO,EAAEmN,aAAa,EAAE,CAACuG,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,CAACI,IAAI,EAAEF,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAACI,IAAI,CAACvH,IAAI,EAAGmG,MAAM,IAAK;MACpM,IAAIA,MAAM,CAACqB,SAAS,KAAKxZ,sBAAsB,CAACyZ,QAAQ,IAAItB,MAAM,CAACrR,OAAO,EAAE;QACxE,IAAI,CAAC4S,0BAA0B,CAACvB,MAAM,CAACgB,MAAM,CAACI,IAAI,CAACI,MAAM,EAAExB,MAAM,CAACrR,OAAO,CAAC;MAC9E;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAAC8S,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACAnU,OAAO,CAACoU,qBAAqB,CAAEV,MAAM,IAAK;MACtC,MAAMW,OAAO,GAAGlH,aAAa,CAAC1C,GAAG,CAACiJ,MAAM,CAACE,YAAY,CAAC;MACtDS,OAAO,CAAChT,OAAO,CAACpJ,SAAS,GAAGyb,MAAM,CAACI,IAAI,CAACvH,IAAI;IAChD,CAAC,CAAC;IACF,IAAI,CAACiH,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC/B,cAAc,CAAC2B,IAAI,CAAC,CAAC;IAC1B,IAAI,CAAC9B,wBAAwB,CAAC,CAAC;EACnC;EACA;EACAgD,YAAYA,CAAC7V,SAAS,EAAE;IACpB,IAAI,CAAC6Q,iBAAiB,CAACzQ,GAAG,CAACJ,SAAS,CAAC;EACzC;EACA;EACA8V,eAAeA,CAAC9V,SAAS,EAAE;IACvB,IAAI,CAAC6Q,iBAAiB,CAACkF,MAAM,CAAC/V,SAAS,CAAC;EAC5C;EACA;EACAgW,SAASA,CAACP,MAAM,EAAE;IACd,IAAI,CAAC3E,cAAc,CAAC1Q,GAAG,CAACqV,MAAM,CAAC;EACnC;EACA;EACAQ,YAAYA,CAACR,MAAM,EAAE;IACjB,IAAI,CAAC3E,cAAc,CAACiF,MAAM,CAACN,MAAM,CAAC;EACtC;EACA;EACAS,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACpF,oBAAoB,CAAC3Q,GAAG,CAAC+V,YAAY,CAAC;IAC3C,IAAI,CAACjF,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAkF,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAACpF,oBAAoB,CAACgF,MAAM,CAACI,YAAY,CAAC;IAC9C,IAAI,CAACjF,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAmF,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACtF,oBAAoB,CAAC5Q,GAAG,CAACkW,YAAY,CAAC;IAC3C,IAAI,CAACnF,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAoF,kBAAkBA,CAACD,YAAY,EAAE;IAC7B,IAAI,CAACtF,oBAAoB,CAAC+E,MAAM,CAACO,YAAY,CAAC;IAC9C,IAAI,CAACnF,oBAAoB,GAAG,IAAI;EACpC;EACA;EACAqF,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACxF,gBAAgB,GAAGwF,SAAS;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC5H,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAACrK,kBAAkB,EAAE;MACzB,MAAMkS,KAAK,GAAGC,mBAAmB,CAAC,IAAI,CAAC9H,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAI6H,KAAK,EAAE;QACPA,KAAK,CAAC/L,KAAK,CAACiM,OAAO,GAAGJ,UAAU,CAACvP,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMgC,YAAY,GAAG,IAAI,CAACqH,cAAc,CAAC3H,GAAG,CAAC2L,GAAG,IAAIA,GAAG,CAACzW,MAAM,CAAC;IAC/D,IAAI,CAACuT,aAAa,CAACzL,sBAAsB,CAAC6Q,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9D,IAAI,CAACpF,aAAa,CAACrI,SAAS,CAACyN,UAAU,EAAEvN,YAAY,EAAE,KAAK,CAAC;IAC7D;IACA,IAAI,CAACqH,cAAc,CAAC+D,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC9V,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqY,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAACzH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA,IAAI,IAAI,CAACxK,kBAAkB,EAAE;MACzB,MAAM4F,KAAK,GAAGuM,mBAAmB,CAAC,IAAI,CAAC3H,gBAAgB,EAAE,OAAO,CAAC;MACjE,IAAI5E,KAAK,EAAE;QACPA,KAAK,CAACO,KAAK,CAACiM,OAAO,GAAGE,UAAU,CAAC7P,MAAM,GAAG,EAAE,GAAG,MAAM;MACzD;IACJ;IACA,MAAMgC,YAAY,GAAG,IAAI,CAACsH,cAAc,CAAC5H,GAAG,CAAC2L,GAAG,IAAIA,GAAG,CAACzW,MAAM,CAAC;IAC/D,IAAI,CAACuT,aAAa,CAACzL,sBAAsB,CAACmR,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;IACjE,IAAI,CAAC1F,aAAa,CAACrI,SAAS,CAAC+N,UAAU,EAAE7N,YAAY,EAAE,QAAQ,CAAC;IAChE,IAAI,CAACmI,aAAa,CAAClH,2BAA2B,CAAC,IAAI,CAACqF,WAAW,CAACxP,aAAa,EAAEkJ,YAAY,CAAC;IAC5F;IACA,IAAI,CAACsH,cAAc,CAAC8D,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC9V,kBAAkB,CAAC,CAAC,CAAC;EAChE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIkU,wBAAwBA,CAAA,EAAG;IACvB,MAAM8D,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC5H,gBAAgB,CAAC;IAC/D,MAAMkI,QAAQ,GAAG,IAAI,CAACN,gBAAgB,CAAC,IAAI,CAAChI,UAAU,CAAC;IACvD,MAAMqI,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAAC,IAAI,CAACzH,gBAAgB,CAAC;IAC/D;IACA;IACA;IACA;IACA,IAAK,IAAI,CAACxK,kBAAkB,IAAI,CAAC,IAAI,CAACoO,YAAY,IAAK,IAAI,CAAC3B,4BAA4B,EAAE;MACtF;MACA;MACA,IAAI,CAACG,aAAa,CAACzL,sBAAsB,CAAC,CAAC,GAAG6Q,UAAU,EAAE,GAAGO,QAAQ,EAAE,GAAGD,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;MACzG,IAAI,CAAC7F,4BAA4B,GAAG,KAAK;IAC7C;IACA;IACAuF,UAAU,CAACnC,OAAO,CAAC,CAAC2C,SAAS,EAAEpf,CAAC,KAAK;MACjC,IAAI,CAACqf,sBAAsB,CAAC,CAACD,SAAS,CAAC,EAAE,IAAI,CAAC1G,cAAc,CAAC1Y,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACA,IAAI,CAACyY,QAAQ,CAACgE,OAAO,CAACiB,MAAM,IAAI;MAC5B;MACA,MAAM1P,IAAI,GAAG,EAAE;MACf,KAAK,IAAIhO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmf,QAAQ,CAAC9P,MAAM,EAAErP,CAAC,EAAE,EAAE;QACtC,IAAI,IAAI,CAACqY,WAAW,CAACrY,CAAC,CAAC,CAAC0d,MAAM,KAAKA,MAAM,EAAE;UACvC1P,IAAI,CAACQ,IAAI,CAAC2Q,QAAQ,CAACnf,CAAC,CAAC,CAAC;QAC1B;MACJ;MACA,IAAI,CAACqf,sBAAsB,CAACrR,IAAI,EAAE0P,MAAM,CAAC;IAC7C,CAAC,CAAC;IACF;IACAwB,UAAU,CAACzC,OAAO,CAAC,CAAC6C,SAAS,EAAEtf,CAAC,KAAK;MACjC,IAAI,CAACqf,sBAAsB,CAAC,CAACC,SAAS,CAAC,EAAE,IAAI,CAAC3G,cAAc,CAAC3Y,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IACF;IACAyO,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC6J,iBAAiB,CAACgH,MAAM,CAAC,CAAC,CAAC,CAAC9C,OAAO,CAACC,GAAG,IAAIA,GAAG,CAAC9V,kBAAkB,CAAC,CAAC,CAAC;EACxF;EACA;EACAkQ,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC+C,cAAc,IACpB,IAAI,CAAChD,UAAU,IACf,IAAI,CAACI,gBAAgB,IACrB,IAAI,CAACG,gBAAgB,IACrB,IAAI,CAACG,gBAAgB,EAAE;MACvB,IAAI,CAACsC,cAAc,GAAG,IAAI;MAC1B;MACA;MACA,IAAI,IAAI,CAAC0C,UAAU,CAAC,CAAC,EAAE;QACnB,IAAI,CAACC,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1C,cAAc,IAAI,IAAI,CAACC,eAAe;EACtD;EACA;EACA0C,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACgD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAAC,IAAI,CAAC/G,cAAc,CAACrJ,MAAM,IAC3B,CAAC,IAAI,CAACsJ,cAAc,CAACtJ,MAAM,IAC3B,CAAC,IAAI,CAACoJ,QAAQ,CAACpJ,MAAM,KACpB,OAAOlK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM+Q,2BAA2B,CAAC,CAAC;IACvC;IACA;IACA,MAAMwJ,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACnD,MAAMC,cAAc,GAAGF,cAAc,IAAI,IAAI,CAACvG,oBAAoB,IAAI,IAAI,CAACC,oBAAoB;IAC/F;IACA,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAACA,4BAA4B,IAAIuG,cAAc;IACvF,IAAI,CAACtG,2BAA2B,GAAGsG,cAAc;IACjD;IACA,IAAI,IAAI,CAACzG,oBAAoB,EAAE;MAC3B,IAAI,CAAC0G,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC1G,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAAC0G,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAAC1G,oBAAoB,GAAG,KAAK;IACrC;IACA;IACA;IACA,IAAI,IAAI,CAACoB,UAAU,IAAI,IAAI,CAAC/B,QAAQ,CAACpJ,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACiJ,yBAAyB,EAAE;MAChF,IAAI,CAACyH,qBAAqB,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,IAAI,CAAC1G,4BAA4B,EAAE;MACxC;MACA;MACA,IAAI,CAACyB,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAACkF,kBAAkB,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIjD,iBAAiBA,CAAA,EAAG;IAChB,MAAMD,UAAU,GAAG,EAAE;IACrB;IACA;IACA,MAAMmD,oBAAoB,GAAG,IAAI,CAAC1G,oBAAoB;IACtD,IAAI,CAACA,oBAAoB,GAAG,IAAIf,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,CAACL,KAAK,EAAE;MACb,OAAO2E,UAAU;IACrB;IACA;IACA;IACA,KAAK,IAAI9c,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACmY,KAAK,CAAC9I,MAAM,EAAErP,CAAC,EAAE,EAAE;MACxC,IAAI+V,IAAI,GAAG,IAAI,CAACoC,KAAK,CAACnY,CAAC,CAAC;MACxB,MAAMkgB,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAACpK,IAAI,EAAE/V,CAAC,EAAEigB,oBAAoB,CAAChM,GAAG,CAAC8B,IAAI,CAAC,CAAC;MAC7F,IAAI,CAAC,IAAI,CAACwD,oBAAoB,CAAC1E,GAAG,CAACkB,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwD,oBAAoB,CAAClF,GAAG,CAAC0B,IAAI,EAAE,IAAI3I,OAAO,CAAC,CAAC,CAAC;MACtD;MACA,KAAK,IAAIgT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAAC7Q,MAAM,EAAE+Q,CAAC,EAAE,EAAE;QAC/C,IAAIC,SAAS,GAAGH,iBAAiB,CAACE,CAAC,CAAC;QACpC,MAAME,KAAK,GAAG,IAAI,CAAC/G,oBAAoB,CAACtF,GAAG,CAACoM,SAAS,CAACtK,IAAI,CAAC;QAC3D,IAAIuK,KAAK,CAACzL,GAAG,CAACwL,SAAS,CAAC3C,MAAM,CAAC,EAAE;UAC7B4C,KAAK,CAACrM,GAAG,CAACoM,SAAS,CAAC3C,MAAM,CAAC,CAAClP,IAAI,CAAC6R,SAAS,CAAC;QAC/C,CAAC,MACI;UACDC,KAAK,CAACjM,GAAG,CAACgM,SAAS,CAAC3C,MAAM,EAAE,CAAC2C,SAAS,CAAC,CAAC;QAC5C;QACAvD,UAAU,CAACtO,IAAI,CAAC6R,SAAS,CAAC;MAC9B;IACJ;IACA,OAAOvD,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIqD,qBAAqBA,CAACpK,IAAI,EAAEgG,SAAS,EAAEuE,KAAK,EAAE;IAC1C,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACzK,IAAI,EAAEgG,SAAS,CAAC;IACjD,OAAOwE,OAAO,CAACxP,GAAG,CAAC2M,MAAM,IAAI;MACzB,MAAM+C,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAACzL,GAAG,CAAC6I,MAAM,CAAC,GAAG4C,KAAK,CAACrM,GAAG,CAACyJ,MAAM,CAAC,GAAG,EAAE;MAC5E,IAAI+C,gBAAgB,CAACpR,MAAM,EAAE;QACzB,MAAMyM,OAAO,GAAG2E,gBAAgB,CAACC,KAAK,CAAC,CAAC;QACxC5E,OAAO,CAACC,SAAS,GAAGA,SAAS;QAC7B,OAAOD,OAAO;MAClB,CAAC,MACI;QACD,OAAO;UAAE/F,IAAI;UAAE2H,MAAM;UAAE3B;QAAU,CAAC;MACtC;IACJ,CAAC,CAAC;EACN;EACA;EACA0D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAClH,iBAAiB,CAACoE,KAAK,CAAC,CAAC;IAC9B,MAAMgE,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACxF,kBAAkB,CAAC,EAAE,IAAI,CAACvC,iBAAiB,CAAC;IACtG6H,UAAU,CAAClE,OAAO,CAACxU,SAAS,IAAI;MAC5B,IAAI,IAAI,CAACsQ,iBAAiB,CAAC1D,GAAG,CAAC5M,SAAS,CAACtG,IAAI,CAAC,KACzC,OAAOwD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMyQ,gCAAgC,CAAC3N,SAAS,CAACtG,IAAI,CAAC;MAC1D;MACA,IAAI,CAAC4W,iBAAiB,CAAClE,GAAG,CAACpM,SAAS,CAACtG,IAAI,EAAEsG,SAAS,CAAC;IACzD,CAAC,CAAC;EACN;EACA;EACAuX,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC9G,cAAc,GAAGkI,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACtF,qBAAqB,CAAC,EAAE,IAAI,CAACvC,oBAAoB,CAAC;IAC/G,IAAI,CAACL,cAAc,GAAGiI,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACrF,qBAAqB,CAAC,EAAE,IAAI,CAACvC,oBAAoB,CAAC;IAC/G,IAAI,CAACR,QAAQ,GAAGmI,gBAAgB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACvF,eAAe,CAAC,EAAE,IAAI,CAACvC,cAAc,CAAC;IAC7F;IACA,MAAM+H,cAAc,GAAG,IAAI,CAACrI,QAAQ,CAAC7D,MAAM,CAAC8H,GAAG,IAAI,CAACA,GAAG,CAAClS,IAAI,CAAC;IAC7D,IAAI,CAAC,IAAI,CAACmQ,qBAAqB,IAC3BmG,cAAc,CAACzR,MAAM,GAAG,CAAC,KACxB,OAAOlK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAM0Q,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAI,CAACgD,cAAc,GAAGiI,cAAc,CAAC,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACInB,qBAAqBA,CAAA,EAAG;IACpB,MAAMoB,kBAAkB,GAAGA,CAACC,GAAG,EAAEtE,GAAG,KAAK;MACrC;MACA;MACA,MAAM9S,IAAI,GAAG,CAAC,CAAC8S,GAAG,CAAC7S,cAAc,CAAC,CAAC;MACnC,OAAOmX,GAAG,IAAIpX,IAAI;IACtB,CAAC;IACD;IACA,MAAMqX,kBAAkB,GAAG,IAAI,CAACxI,QAAQ,CAACyI,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAC1E,IAAIE,kBAAkB,EAAE;MACpB,IAAI,CAACpG,oBAAoB,CAAC,CAAC;IAC/B;IACA;IACA,MAAMsG,oBAAoB,GAAG,IAAI,CAACzI,cAAc,CAACwI,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAII,oBAAoB,EAAE;MACtB,IAAI,CAACtB,sBAAsB,CAAC,CAAC;IACjC;IACA,MAAMuB,oBAAoB,GAAG,IAAI,CAACzI,cAAc,CAACuI,MAAM,CAACH,kBAAkB,EAAE,KAAK,CAAC;IAClF,IAAIK,oBAAoB,EAAE;MACtB,IAAI,CAACtB,sBAAsB,CAAC,CAAC;IACjC;IACA,OAAOmB,kBAAkB,IAAIE,oBAAoB,IAAIC,oBAAoB;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACI1G,iBAAiBA,CAACF,UAAU,EAAE;IAC1B,IAAI,CAACrC,KAAK,GAAG,EAAE;IACf,IAAIlY,YAAY,CAAC,IAAI,CAACua,UAAU,CAAC,EAAE;MAC/B,IAAI,CAACA,UAAU,CAAC5H,UAAU,CAAC,IAAI,CAAC;IACpC;IACA;IACA,IAAI,IAAI,CAAC0F,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAAC+I,WAAW,CAAC,CAAC;MAC5C,IAAI,CAAC/I,yBAAyB,GAAG,IAAI;IACzC;IACA,IAAI,CAACkC,UAAU,EAAE;MACb,IAAI,IAAI,CAAC5B,WAAW,EAAE;QAClB,IAAI,CAACA,WAAW,CAAChP,IAAI,CAAC,EAAE,CAAC;MAC7B;MACA,IAAI,IAAI,CAACiN,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACF,aAAa,CAACgG,KAAK,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAAClC,WAAW,GAAGD,UAAU;EACjC;EACA;EACAuF,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAAC,IAAI,CAACvF,UAAU,EAAE;MAClB;IACJ;IACA,IAAI8G,UAAU;IACd,IAAIrhB,YAAY,CAAC,IAAI,CAACua,UAAU,CAAC,EAAE;MAC/B8G,UAAU,GAAG,IAAI,CAAC9G,UAAU,CAAC+G,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC,MACI,IAAIhe,YAAY,CAAC,IAAI,CAACiX,UAAU,CAAC,EAAE;MACpC8G,UAAU,GAAG,IAAI,CAAC9G,UAAU;IAChC,CAAC,MACI,IAAI/L,KAAK,CAAC+S,OAAO,CAAC,IAAI,CAAChH,UAAU,CAAC,EAAE;MACrC8G,UAAU,GAAG9d,EAAE,CAAC,IAAI,CAACgX,UAAU,CAAC;IACpC;IACA,IAAI8G,UAAU,KAAKtH,SAAS,KAAK,OAAO7U,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMgR,8BAA8B,CAAC,CAAC;IAC1C;IACA,IAAI,CAACmC,yBAAyB,GAAGgJ,UAAU,CACtCnF,IAAI,CAAC1Y,SAAS,CAAC,IAAI,CAAC2U,UAAU,CAAC,CAAC,CAChCgE,SAAS,CAACrG,IAAI,IAAI;MACnB,IAAI,CAACoC,KAAK,GAAGpC,IAAI,IAAI,EAAE;MACvB,IAAI,CAAC+G,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI+C,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAAC5I,gBAAgB,CAACN,aAAa,CAACtH,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAAC4H,gBAAgB,CAACN,aAAa,CAACgG,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAACjE,cAAc,CAAC+D,OAAO,CAAC,CAACC,GAAG,EAAE1c,CAAC,KAAK,IAAI,CAACyhB,UAAU,CAAC,IAAI,CAACxK,gBAAgB,EAAEyF,GAAG,EAAE1c,CAAC,CAAC,CAAC;IACvF,IAAI,CAAC2e,2BAA2B,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACImB,sBAAsBA,CAAA,EAAG;IACrB;IACA,IAAI,IAAI,CAAC1I,gBAAgB,CAACT,aAAa,CAACtH,MAAM,GAAG,CAAC,EAAE;MAChD,IAAI,CAAC+H,gBAAgB,CAACT,aAAa,CAACgG,KAAK,CAAC,CAAC;IAC/C;IACA,IAAI,CAAChE,cAAc,CAAC8D,OAAO,CAAC,CAACC,GAAG,EAAE1c,CAAC,KAAK,IAAI,CAACyhB,UAAU,CAAC,IAAI,CAACrK,gBAAgB,EAAEsF,GAAG,EAAE1c,CAAC,CAAC,CAAC;IACvF,IAAI,CAACif,2BAA2B,CAAC,CAAC;EACtC;EACA;EACAI,sBAAsBA,CAACrR,IAAI,EAAE0P,MAAM,EAAE;IACjC,MAAMiD,UAAU,GAAGlS,KAAK,CAACC,IAAI,CAACgP,MAAM,EAAErU,OAAO,IAAI,EAAE,CAAC,CAAC0H,GAAG,CAAC2Q,UAAU,IAAI;MACnE,MAAMzZ,SAAS,GAAG,IAAI,CAACsQ,iBAAiB,CAACtE,GAAG,CAACyN,UAAU,CAAC;MACxD,IAAI,CAACzZ,SAAS,KAAK,OAAO9C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/D,MAAMsQ,0BAA0B,CAACiM,UAAU,CAAC;MAChD;MACA,OAAOzZ,SAAS;IACpB,CAAC,CAAC;IACF,MAAMgH,iBAAiB,GAAG0R,UAAU,CAAC5P,GAAG,CAAC9I,SAAS,IAAIA,SAAS,CAAChC,MAAM,CAAC;IACvE,MAAMiJ,eAAe,GAAGyR,UAAU,CAAC5P,GAAG,CAAC9I,SAAS,IAAIA,SAAS,CAAC7B,SAAS,CAAC;IACxE,IAAI,CAACoT,aAAa,CAACxK,mBAAmB,CAAChB,IAAI,EAAEiB,iBAAiB,EAAEC,eAAe,EAAE,CAAC,IAAI,CAAC8L,YAAY,IAAI,IAAI,CAAC1B,2BAA2B,CAAC;EAC5I;EACA;EACAuF,gBAAgBA,CAAC8C,SAAS,EAAE;IACxB,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,IAAI5hB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2hB,SAAS,CAAChL,aAAa,CAACtH,MAAM,EAAErP,CAAC,EAAE,EAAE;MACrD,MAAM6hB,OAAO,GAAGF,SAAS,CAAChL,aAAa,CAAC1C,GAAG,CAACjU,CAAC,CAAC;MAC9C4hB,YAAY,CAACpT,IAAI,CAACqT,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpB,WAAWA,CAACzK,IAAI,EAAEgG,SAAS,EAAE;IACzB,IAAI,IAAI,CAACtD,QAAQ,CAACpJ,MAAM,IAAI,CAAC,EAAE;MAC3B,OAAO,CAAC,IAAI,CAACoJ,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA,IAAI8H,OAAO,GAAG,EAAE;IAChB,IAAI,IAAI,CAAC5F,qBAAqB,EAAE;MAC5B4F,OAAO,GAAG,IAAI,CAAC9H,QAAQ,CAAC7D,MAAM,CAAC8H,GAAG,IAAI,CAACA,GAAG,CAAClS,IAAI,IAAIkS,GAAG,CAAClS,IAAI,CAACuR,SAAS,EAAEhG,IAAI,CAAC,CAAC;IACjF,CAAC,MACI;MACD,IAAI2H,MAAM,GAAG,IAAI,CAACjF,QAAQ,CAAC/O,IAAI,CAACgT,GAAG,IAAIA,GAAG,CAAClS,IAAI,IAAIkS,GAAG,CAAClS,IAAI,CAACuR,SAAS,EAAEhG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC8C,cAAc;MACpG,IAAI6E,MAAM,EAAE;QACR6C,OAAO,CAAC/R,IAAI,CAACkP,MAAM,CAAC;MACxB;IACJ;IACA,IAAI,CAAC6C,OAAO,CAAClR,MAAM,KAAK,OAAOlK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACpE,MAAM2Q,kCAAkC,CAACC,IAAI,CAAC;IAClD;IACA,OAAOwK,OAAO;EAClB;EACAlD,oBAAoBA,CAACgD,SAAS,EAAEpP,KAAK,EAAE;IACnC,MAAMyM,MAAM,GAAG2C,SAAS,CAAC3C,MAAM;IAC/B,MAAM7S,OAAO,GAAG;MAAEpJ,SAAS,EAAE4e,SAAS,CAACtK;IAAK,CAAC;IAC7C,OAAO;MACHxJ,WAAW,EAAEmR,MAAM,CAACjZ,QAAQ;MAC5BoG,OAAO;MACPoG;IACJ,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIwQ,UAAUA,CAACM,MAAM,EAAErE,MAAM,EAAEzM,KAAK,EAAEpG,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C;IACA,MAAMmX,IAAI,GAAGD,MAAM,CAACpL,aAAa,CAACsL,kBAAkB,CAACvE,MAAM,CAACjZ,QAAQ,EAAEoG,OAAO,EAAEoG,KAAK,CAAC;IACrF,IAAI,CAACwM,0BAA0B,CAACC,MAAM,EAAE7S,OAAO,CAAC;IAChD,OAAOmX,IAAI;EACf;EACAvE,0BAA0BA,CAACC,MAAM,EAAE7S,OAAO,EAAE;IACxC,KAAK,IAAIqX,YAAY,IAAI,IAAI,CAACC,iBAAiB,CAACzE,MAAM,CAAC,EAAE;MACrD,IAAIhT,aAAa,CAACI,oBAAoB,EAAE;QACpCJ,aAAa,CAACI,oBAAoB,CAACH,cAAc,CAACsX,kBAAkB,CAACC,YAAY,EAAErX,OAAO,CAAC;MAC/F;IACJ;IACA,IAAI,CAAC6M,kBAAkB,CAAC0K,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIzE,sBAAsBA,CAAA,EAAG;IACrB,MAAMhH,aAAa,GAAG,IAAI,CAACE,UAAU,CAACF,aAAa;IACnD,KAAK,IAAI0L,WAAW,GAAG,CAAC,EAAEC,KAAK,GAAG3L,aAAa,CAACtH,MAAM,EAAEgT,WAAW,GAAGC,KAAK,EAAED,WAAW,EAAE,EAAE;MACxF,MAAMR,OAAO,GAAGlL,aAAa,CAAC1C,GAAG,CAACoO,WAAW,CAAC;MAC9C,MAAMxX,OAAO,GAAGgX,OAAO,CAAChX,OAAO;MAC/BA,OAAO,CAACyX,KAAK,GAAGA,KAAK;MACrBzX,OAAO,CAACtD,KAAK,GAAG8a,WAAW,KAAK,CAAC;MACjCxX,OAAO,CAAC0X,IAAI,GAAGF,WAAW,KAAKC,KAAK,GAAG,CAAC;MACxCzX,OAAO,CAAC2X,IAAI,GAAGH,WAAW,GAAG,CAAC,KAAK,CAAC;MACpCxX,OAAO,CAAC4X,GAAG,GAAG,CAAC5X,OAAO,CAAC2X,IAAI;MAC3B,IAAI,IAAI,CAAC7H,qBAAqB,EAAE;QAC5B9P,OAAO,CAACkR,SAAS,GAAG,IAAI,CAAC1D,WAAW,CAACgK,WAAW,CAAC,CAACtG,SAAS;QAC3DlR,OAAO,CAACwX,WAAW,GAAGA,WAAW;MACrC,CAAC,MACI;QACDxX,OAAO,CAACoG,KAAK,GAAG,IAAI,CAACoH,WAAW,CAACgK,WAAW,CAAC,CAACtG,SAAS;MAC3D;IACJ;EACJ;EACA;EACAoG,iBAAiBA,CAACzE,MAAM,EAAE;IACtB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACrU,OAAO,EAAE;MAC5B,OAAO,EAAE;IACb;IACA,OAAOoF,KAAK,CAACC,IAAI,CAACgP,MAAM,CAACrU,OAAO,EAAEqZ,QAAQ,IAAI;MAC1C,MAAM3Y,MAAM,GAAG,IAAI,CAACwO,iBAAiB,CAACtE,GAAG,CAACyO,QAAQ,CAAC;MACnD,IAAI,CAAC3Y,MAAM,KAAK,OAAO5E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC5D,MAAMsQ,0BAA0B,CAACiN,QAAQ,CAAC;MAC9C;MACA,OAAOhF,MAAM,CAAC5T,mBAAmB,CAACC,MAAM,CAAC;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI8Q,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACjC,WAAW,CAAChP,IAAI,CAAC,EAAE,CAAC;IACzB,IAAI,CAACiN,UAAU,CAACF,aAAa,CAACgG,KAAK,CAAC,CAAC;IACrC,IAAI,CAACG,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIkD,kBAAkBA,CAAA,EAAG;IACjB,MAAM2C,kBAAkB,GAAGA,CAAC3B,GAAG,EAAE4B,CAAC,KAAK;MACnC,OAAO5B,GAAG,IAAI4B,CAAC,CAACjc,gBAAgB,CAAC,CAAC;IACtC,CAAC;IACD;IACA;IACA;IACA,IAAI,IAAI,CAAC+R,cAAc,CAACwI,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAAChE,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAAChG,cAAc,CAACuI,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MACvD,IAAI,CAAC1D,2BAA2B,CAAC,CAAC;IACtC;IACA,IAAIxQ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC6J,iBAAiB,CAACgH,MAAM,CAAC,CAAC,CAAC,CAAC2B,MAAM,CAACyB,kBAAkB,EAAE,KAAK,CAAC,EAAE;MAC/E,IAAI,CAACtJ,4BAA4B,GAAG,IAAI;MACxC,IAAI,CAACyB,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACImB,kBAAkBA,CAAA,EAAG;IACjB,MAAMjP,SAAS,GAAG,IAAI,CAAC4K,IAAI,GAAG,IAAI,CAACA,IAAI,CAACzR,KAAK,GAAG,KAAK;IACrD,IAAI,CAACqT,aAAa,GAAG,IAAI7M,YAAY,CAAC,IAAI,CAACC,kBAAkB,EAAE,IAAI,CAAC6M,cAAc,EAAE,IAAI,CAAC5B,SAAS,CAAC8D,SAAS,EAAE,IAAI,CAACjC,4BAA4B,EAAE1M,SAAS,EAAE,IAAI,CAACgL,0BAA0B,EAAE,IAAI,CAAC0D,SAAS,CAAC;IAC5M,CAAC,IAAI,CAAC9D,IAAI,GAAG,IAAI,CAACA,IAAI,CAACsE,MAAM,GAAG1Y,EAAE,CAAC,CAAC,EAC/B2Y,IAAI,CAAC1Y,SAAS,CAAC,IAAI,CAAC2U,UAAU,CAAC,CAAC,CAChCgE,SAAS,CAACjW,KAAK,IAAI;MACpB,IAAI,CAACqT,aAAa,CAACxM,SAAS,GAAG7G,KAAK;MACpC,IAAI,CAAC2U,wBAAwB,CAAC,CAAC;IACnC,CAAC,CAAC;EACN;EACA;EACA+F,WAAWA,CAACgC,KAAK,EAAE;IACf,OAAOA,KAAK,CAACjO,MAAM,CAAC0I,IAAI,IAAI,CAACA,IAAI,CAAC1X,MAAM,IAAI0X,IAAI,CAAC1X,MAAM,KAAK,IAAI,CAAC;EACrE;EACA;EACAoX,gBAAgBA,CAAA,EAAG;IACf,MAAM0B,SAAS,GAAG,IAAI,CAACxF,gBAAgB,IAAI,IAAI,CAACuC,UAAU;IAC1D,IAAI,CAACiD,SAAS,EAAE;MACZ;IACJ;IACA,MAAMoE,UAAU,GAAG,IAAI,CAACjM,UAAU,CAACF,aAAa,CAACtH,MAAM,KAAK,CAAC;IAC7D,IAAIyT,UAAU,KAAK,IAAI,CAAClJ,mBAAmB,EAAE;MACzC;IACJ;IACA,MAAMmJ,SAAS,GAAG,IAAI,CAACxL,gBAAgB,CAACZ,aAAa;IACrD,IAAImM,UAAU,EAAE;MACZ,MAAMd,IAAI,GAAGe,SAAS,CAACd,kBAAkB,CAACvD,SAAS,CAACnS,WAAW,CAAC;MAChE,MAAMyW,QAAQ,GAAGhB,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;MAClC;MACA;MACA,IAAIE,IAAI,CAACF,SAAS,CAACzS,MAAM,KAAK,CAAC,IAAI2T,QAAQ,EAAE1U,QAAQ,KAAK,IAAI,CAAC4J,SAAS,CAAC3J,YAAY,EAAE;QACnFyU,QAAQ,CAACla,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;QACpCka,QAAQ,CAAC5a,SAAS,CAACC,GAAG,CAACqW,SAAS,CAAClS,iBAAiB,CAAC;MACvD;IACJ,CAAC,MACI;MACDuW,SAAS,CAACpG,KAAK,CAAC,CAAC;IACrB;IACA,IAAI,CAAC/C,mBAAmB,GAAGkJ,UAAU;IACrC,IAAI,CAACpL,kBAAkB,CAAC0K,YAAY,CAAC,CAAC;EAC1C;EACA,OAAOzd,IAAI,YAAAse,iBAAApe,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4S,QAAQ;EAAA;EAC3G,OAAOtM,IAAI,kBAljE8E5K,EAAE,CAAA6K,iBAAA;IAAApG,IAAA,EAkjEJyS,QAAQ;IAAAxS,SAAA;IAAA+B,cAAA,WAAAkc,wBAAA7iB,EAAA,EAAAC,GAAA,EAAA4G,QAAA;MAAA,IAAA7G,EAAA;QAljENE,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EAujEpBoF,YAAY;QAvjEM/L,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EAujEgEvB,YAAY;QAvjE9EpF,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EAujEiJqD,SAAS;QAvjE5JhK,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EAujEqO8C,eAAe;QAvjEtPzJ,EAAE,CAAA4G,cAAA,CAAAD,QAAA,EAujE+T+C,eAAe;MAAA;MAAA,IAAA5J,EAAA;QAAA,IAAA+G,EAAA;QAvjEhV7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAmb,UAAA,GAAArU,EAAA,CAAAG,KAAA;QAAFhH,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAA+a,kBAAA,GAAAjU,EAAA;QAAF7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAgb,eAAA,GAAAlU,EAAA;QAAF7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAib,qBAAA,GAAAnU,EAAA;QAAF7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAkb,qBAAA,GAAApU,EAAA;MAAA;IAAA;IAAAoB,SAAA;IAAA2a,QAAA;IAAAC,YAAA,WAAAC,sBAAAhjB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAA+iB,WAAA,2BAAAhjB,GAAA,CAAAya,WAkjEG,CAAC;MAAA;IAAA;IAAAvT,MAAA;MAAA2S,OAAA;MAAAK,UAAA;MAAAG,qBAAA,wDAAiMzY,gBAAgB;MAAA6Y,WAAA,oCAA+C7Y,gBAAgB;IAAA;IAAAqhB,OAAA;MAAAtI,cAAA;IAAA;IAAAuI,QAAA;IAAAte,UAAA;IAAAuC,QAAA,GAljEtRlH,EAAE,CAAAmH,kBAAA,CAkjEyb,CAC5gB;MAAEC,OAAO,EAAErD,SAAS;MAAEsD,WAAW,EAAE6P;IAAS,CAAC,EAC7C;MAAE9P,OAAO,EAAEhE,uBAAuB;MAAE8S,QAAQ,EAAEzS;IAA6B,CAAC;IAC5E;IACA;MAAE2D,OAAO,EAAE2O,2BAA2B;MAAEmN,QAAQ,EAAE;IAAK,CAAC,CAC3D,GAvjEoFljB,EAAE,CAAAsH,wBAAA,EAAFtH,EAAE,CAAA8K,mBAAA;IAAAqY,kBAAA,EAAAvjB,GAAA;IAAAmL,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/G,QAAA,WAAAkf,kBAAAtjB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAAqjB,eAAA,CAAA1jB,GAAA;QAAFK,EAAE,CAAAC,YAAA,EAwjE9D,CAAC;QAxjE2DD,EAAE,CAAAC,YAAA,KAyjExD,CAAC;QAzjEqDD,EAAE,CAAAsjB,UAAA,IAAAzjB,+BAAA,MA+jE3E,CAAC,IAAAK,+BAAA,MAIQ,CAAC,IAAAI,+BAAA,MAWlB,CAAC;MAAA;MAAA,IAAAR,EAAA;QA9kEgFE,EAAE,CAAAa,SAAA,EAikE3F,CAAC;QAjkEwFb,EAAE,CAAAujB,aAAA,CAAAxjB,GAAA,CAAAqZ,SAAA,SAikE3F,CAAC;QAjkEwFpZ,EAAE,CAAAa,SAAA,CAmlE3F,CAAC;QAnlEwFb,EAAE,CAAAujB,aAAA,CAAAxjB,GAAA,CAAAsM,kBAAA,QAmlE3F,CAAC;MAAA;IAAA;IAAAlB,YAAA,GACqHsL,eAAe,EAA8DN,aAAa,EAAwDY,eAAe,EAA8DH,eAAe;IAAA4M,MAAA;IAAApY,aAAA;EAAA;AACxW;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KAtlE6F5E,EAAE,CAAA6E,iBAAA,CAslEJqS,QAAQ,EAAc,CAAC;IACtGzS,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEke,QAAQ,EAAE,UAAU;MAAE/e,QAAQ,EAAE;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEiE,IAAI,EAAE;QACa,OAAO,EAAE,WAAW;QACpB,gCAAgC,EAAE;MACtC,CAAC;MAAEiD,aAAa,EAAEjJ,iBAAiB,CAACoJ,IAAI;MAAEF,eAAe,EAAEnJ,uBAAuB,CAACoJ,OAAO;MAAE/D,SAAS,EAAE,CACnG;QAAEH,OAAO,EAAErD,SAAS;QAAEsD,WAAW,EAAE6P;MAAS,CAAC,EAC7C;QAAE9P,OAAO,EAAEhE,uBAAuB;QAAE8S,QAAQ,EAAEzS;MAA6B,CAAC;MAC5E;MACA;QAAE2D,OAAO,EAAE2O,2BAA2B;QAAEmN,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAE1X,OAAO,EAAE,CAACiL,eAAe,EAAEN,aAAa,EAAEY,eAAe,EAAEH,eAAe,CAAC;MAAE4M,MAAM,EAAE,CAAC,+CAA+C;IAAE,CAAC;EACvJ,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5J,OAAO,EAAE,CAAC;MAClDnV,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEqY,UAAU,EAAE,CAAC;MACbxV,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEwY,qBAAqB,EAAE,CAAC;MACxB3V,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAE0C,SAAS,EAAE7F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6Y,WAAW,EAAE,CAAC;MACd/V,IAAI,EAAE7C,KAAK;MACXkD,IAAI,EAAE,CAAC;QAAE0C,SAAS,EAAE7F;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+Y,cAAc,EAAE,CAAC;MACjBjW,IAAI,EAAE/B;IACV,CAAC,CAAC;IAAEoY,kBAAkB,EAAE,CAAC;MACrBrW,IAAI,EAAE9B,eAAe;MACrBmC,IAAI,EAAE,CAACM,YAAY,EAAE;QAAEqe,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAE1I,eAAe,EAAE,CAAC;MAClBtW,IAAI,EAAE9B,eAAe;MACrBmC,IAAI,EAAE,CAACkF,SAAS,EAAE;QAAEyZ,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEzI,qBAAqB,EAAE,CAAC;MACxBvW,IAAI,EAAE9B,eAAe;MACrBmC,IAAI,EAAE,CAAC2E,eAAe,EAAE;QAChBga,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAExI,qBAAqB,EAAE,CAAC;MACxBxW,IAAI,EAAE9B,eAAe;MACrBmC,IAAI,EAAE,CAAC4E,eAAe,EAAE;QAChB+Z,WAAW,EAAE;MACjB,CAAC;IACT,CAAC,CAAC;IAAEvI,UAAU,EAAE,CAAC;MACbzW,IAAI,EAAE5C,YAAY;MAClBiD,IAAI,EAAE,CAACiH,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASsU,gBAAgBA,CAACqD,KAAK,EAAE5P,GAAG,EAAE;EAClC,OAAO4P,KAAK,CAACC,MAAM,CAACzV,KAAK,CAACC,IAAI,CAAC2F,GAAG,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS0K,mBAAmBA,CAACgD,MAAM,EAAEoC,OAAO,EAAE;EAC1C,MAAMC,gBAAgB,GAAGD,OAAO,CAACE,WAAW,CAAC,CAAC;EAC9C,IAAIC,OAAO,GAAGvC,MAAM,CAACpL,aAAa,CAAC9H,OAAO,CAAC1G,aAAa;EACxD,OAAOmc,OAAO,EAAE;IACZ;IACA,MAAM1I,QAAQ,GAAG0I,OAAO,CAAChW,QAAQ,KAAK,CAAC,GAAGgW,OAAO,CAAC1I,QAAQ,GAAG,IAAI;IACjE,IAAIA,QAAQ,KAAKwI,gBAAgB,EAAE;MAC/B,OAAOE,OAAO;IAClB,CAAC,MACI,IAAI1I,QAAQ,KAAK,OAAO,EAAE;MAC3B;MACA;IACJ;IACA0I,OAAO,GAAGA,OAAO,CAACC,UAAU;EAChC;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB5e,MAAM,GAAG7D,MAAM,CAAC0V,QAAQ,EAAE;IAAE5R,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC7C4e,QAAQ,GAAG1iB,MAAM,CAACwC,mBAAmB,EAAE;IAAEsB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC1D;EACA,IAAIlE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoE,KAAK;EACrB;EACA,IAAIpE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACoE,KAAK,GAAGpE,IAAI;IACjB;IACA;IACA,IAAI,CAAC+iB,kBAAkB,CAAC,CAAC;EAC7B;EACA3e,KAAK;EACL;AACJ;AACA;AACA;EACIzE,UAAU;EACV;AACJ;AACA;AACA;AACA;AACA;EACII,YAAY;EACZ;EACAP,OAAO,GAAG,OAAO;EACjB;EACA8G,SAAS;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3B,IAAI;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU;EACV7B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+f,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC;EACvC;EACAzI,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC0I,kBAAkB,CAAC,CAAC;IACzB,IAAI,IAAI,CAACpjB,UAAU,KAAK0Y,SAAS,EAAE;MAC/B,IAAI,CAAC1Y,UAAU,GAAG,IAAI,CAACqjB,wBAAwB,CAAC,CAAC;IACrD;IACA,IAAI,CAAC,IAAI,CAACjjB,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GACb,IAAI,CAAC+iB,QAAQ,CAACG,mBAAmB,KAAK,CAAC7O,IAAI,EAAEpU,IAAI,KAAKoU,IAAI,CAACpU,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,IAAI,CAACiE,MAAM,EAAE;MACb;MACA;MACA;MACA,IAAI,CAACqC,SAAS,CAAC3B,IAAI,GAAG,IAAI,CAACA,IAAI;MAC/B,IAAI,CAAC2B,SAAS,CAAC1B,UAAU,GAAG,IAAI,CAACA,UAAU;MAC3C,IAAI,CAACX,MAAM,CAACkY,YAAY,CAAC,IAAI,CAAC7V,SAAS,CAAC;IAC5C,CAAC,MACI,IAAI,OAAO9C,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMiR,yCAAyC,CAAC,CAAC;IACrD;EACJ;EACArL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnF,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACmY,eAAe,CAAC,IAAI,CAAC9V,SAAS,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;EACI0c,wBAAwBA,CAAA,EAAG;IACvB,MAAMhjB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,KAAK,OAAOwD,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1D,MAAMkR,kCAAkC,CAAC,CAAC;IAC9C;IACA,IAAI,IAAI,CAACoO,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACI,0BAA0B,EAAE;MAC3D,OAAO,IAAI,CAACJ,QAAQ,CAACI,0BAA0B,CAACljB,IAAI,CAAC;IACzD;IACA,OAAOA,IAAI,CAAC,CAAC,CAAC,CAAC0iB,WAAW,CAAC,CAAC,GAAG1iB,IAAI,CAACmP,KAAK,CAAC,CAAC,CAAC;EAChD;EACA;EACA4T,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACzc,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACtG,IAAI,GAAG,IAAI,CAACA,IAAI;IACnC;EACJ;EACA,OAAOgD,IAAI,YAAAmgB,sBAAAjgB,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2f,aAAa;EAAA;EAChH,OAAOrZ,IAAI,kBAjyE8E5K,EAAE,CAAA6K,iBAAA;IAAApG,IAAA,EAiyEJwf,aAAa;IAAAvf,SAAA;IAAA8f,SAAA,WAAAC,oBAAA3kB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAjyEXE,EAAE,CAAA0kB,WAAA,CAiyEqOtf,YAAY;QAjyEnPpF,EAAE,CAAA0kB,WAAA,CAiyEsUzgB,UAAU;QAjyElVjE,EAAE,CAAA0kB,WAAA,CAiyE2a1f,gBAAgB;MAAA;MAAA,IAAAlF,EAAA;QAAA,IAAA+G,EAAA;QAjyE7b7G,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAA2H,SAAA,GAAAb,EAAA,CAAAG,KAAA;QAAFhH,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAgG,IAAA,GAAAc,EAAA,CAAAG,KAAA;QAAFhH,EAAE,CAAA8G,cAAA,CAAAD,EAAA,GAAF7G,EAAE,CAAA+G,WAAA,QAAAhH,GAAA,CAAAiG,UAAA,GAAAa,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,MAAA;MAAA7F,IAAA;MAAAL,UAAA;MAAAI,YAAA;MAAAP,OAAA;IAAA;IAAA+D,UAAA;IAAAuC,QAAA,GAAFlH,EAAE,CAAA8K,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA/G,QAAA,WAAAygB,uBAAA7kB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFE,EAAE,CAAA4kB,uBAAA,KAkyEjE,CAAC;QAlyE8D5kB,EAAE,CAAAsjB,UAAA,IAAA/iB,2BAAA,eAmyEvB,CAAC,IAAAS,2BAAA,eAGH,CAAC;QAtyEsBhB,EAAE,CAAA6kB,qBAAA;MAAA;IAAA;IAAA1Z,YAAA,GA0yEhC/F,YAAY,EAA4GJ,gBAAgB,EAA+D+C,aAAa,EAAiF9D,UAAU,EAAyDwE,OAAO;IAAA2C,aAAA;EAAA;AAC9a;AACA;EAAA,QAAAxG,SAAA,oBAAAA,SAAA,KA5yE6F5E,EAAE,CAAA6E,iBAAA,CA4yEJof,aAAa,EAAc,CAAC;IAC3Gxf,IAAI,EAAExC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3Bb,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBkH,aAAa,EAAEjJ,iBAAiB,CAACoJ,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAF,eAAe,EAAEnJ,uBAAuB,CAACoJ,OAAO;MAChDE,OAAO,EAAE,CAACpG,YAAY,EAAEJ,gBAAgB,EAAE+C,aAAa,EAAE9D,UAAU,EAAEwE,OAAO;IAChF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAErH,IAAI,EAAE,CAAC;MAC/CqD,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEb,UAAU,EAAE,CAAC;MACb0D,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAET,YAAY,EAAE,CAAC;MACfsD,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEhB,OAAO,EAAE,CAAC;MACV6D,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE8F,SAAS,EAAE,CAAC;MACZjD,IAAI,EAAE7B,SAAS;MACfkC,IAAI,EAAE,CAACM,YAAY,EAAE;QAAE0f,MAAM,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAE/e,IAAI,EAAE,CAAC;MACPtB,IAAI,EAAE7B,SAAS;MACfkC,IAAI,EAAE,CAACb,UAAU,EAAE;QAAE6gB,MAAM,EAAE;MAAK,CAAC;IACvC,CAAC,CAAC;IAAE9e,UAAU,EAAE,CAAC;MACbvB,IAAI,EAAE7B,SAAS;MACfkC,IAAI,EAAE,CAACE,gBAAgB,EAAE;QAAE8f,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,qBAAqB,GAAG,CAC1B7N,QAAQ,EACRlN,SAAS,EACT/F,UAAU,EACVkG,aAAa,EACbnF,gBAAgB,EAChBE,gBAAgB,EAChBE,YAAY,EACZqD,OAAO,EACPmD,MAAM,EACN7D,aAAa,EACbK,aAAa,EACbsC,YAAY,EACZjB,eAAe,EACfgC,YAAY,EACZ/B,eAAe,EACfyM,aAAa,EACbM,eAAe,EACfG,eAAe,EACfqN,aAAa,EACblY,YAAY,EACZiK,cAAc,EACde,eAAe,CAClB;AACD,MAAMiO,cAAc,CAAC;EACjB,OAAO5gB,IAAI,YAAA6gB,uBAAA3gB,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0gB,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAj3E8EllB,EAAE,CAAAmlB,gBAAA;IAAA1gB,IAAA,EAi3ESugB;EAAc;EA2ClH,OAAOI,IAAI,kBA55E8EplB,EAAE,CAAAqlB,gBAAA;IAAA7Z,OAAA,GA45EmC1H,eAAe;EAAA;AACjJ;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KA95E6F5E,EAAE,CAAA6E,iBAAA,CA85EJmgB,cAAc,EAAc,CAAC;IAC5GvgB,IAAI,EAAE5B,QAAQ;IACdiC,IAAI,EAAE,CAAC;MACCwgB,OAAO,EAAEP,qBAAqB;MAC9BvZ,OAAO,EAAE,CAAC1H,eAAe,EAAE,GAAGihB,qBAAqB;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAStd,WAAW,EAAEmB,UAAU,EAAED,gBAAgB,EAAE5E,SAAS,EAAE0E,OAAO,EAAExE,UAAU,EAAEkG,aAAa,EAAE/E,YAAY,EAAEgD,aAAa,EAAElD,gBAAgB,EAAEuG,YAAY,EAAE/B,eAAe,EAAE3B,aAAa,EAAE/C,gBAAgB,EAAE0F,YAAY,EAAEjB,eAAe,EAAEsC,YAAY,EAAEiK,cAAc,EAAEpK,MAAM,EAAE5B,SAAS,EAAEkN,QAAQ,EAAE8N,cAAc,EAAEf,aAAa,EAAE9N,aAAa,EAAES,eAAe,EAAEH,eAAe,EAAEM,eAAe,EAAEhB,2BAA2B,EAAE/R,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}