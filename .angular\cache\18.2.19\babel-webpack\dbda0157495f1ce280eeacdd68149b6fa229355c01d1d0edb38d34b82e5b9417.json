{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DataService {\n  constructor(http) {\n    this.http = http;\n  }\n  getTestData() {\n    console.log('Fetching data from /assets/samplejson.json');\n    return this.http.get('/assets/samplejson.json');\n  }\n  getMatrixData() {\n    return this.getTestData().pipe(map(data => {\n      console.log('Raw data from JSON:', data);\n      const transformedData = this.transformToMatrix(data);\n      console.log('Transformed matrix data:', transformedData);\n      return transformedData;\n    }));\n  }\n  transformToMatrix(data) {\n    const batchNumbers = [...new Set(data.map(item => item.BatchNo))].sort();\n    const testNames = [...new Set(data.map(item => item.TestName))].sort();\n    const subTestsByTest = {};\n    const dataMatrix = {};\n    const unitsByTestAndSubTest = {};\n    // Initialize structures\n    testNames.forEach(testName => {\n      subTestsByTest[testName] = [...new Set(data.filter(item => item.TestName === testName).map(item => item.SubTestName))].sort();\n      unitsByTestAndSubTest[testName] = {};\n    });\n    batchNumbers.forEach(batchNo => {\n      dataMatrix[batchNo] = {};\n      testNames.forEach(testName => {\n        dataMatrix[batchNo][testName] = {};\n      });\n    });\n    // Populate data matrix and units\n    data.forEach(item => {\n      if (!dataMatrix[item.BatchNo][item.TestName][item.SubTestName]) {\n        dataMatrix[item.BatchNo][item.TestName][item.SubTestName] = item;\n        unitsByTestAndSubTest[item.TestName][item.SubTestName] = item.UOM;\n      }\n    });\n    return {\n      batchNumbers,\n      testNames,\n      subTestsByTest,\n      dataMatrix,\n      unitsByTestAndSubTest\n    };\n  }\n  static {\n    this.ɵfac = function DataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DataService,\n      factory: DataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "DataService", "constructor", "http", "getTestData", "console", "log", "get", "getMatrixData", "pipe", "data", "transformedData", "transformToMatrix", "batchNumbers", "Set", "item", "BatchNo", "sort", "testNames", "TestName", "subTestsByTest", "dataMatrix", "unitsByTestAndSubTest", "for<PERSON>ach", "testName", "filter", "SubTestName", "batchNo", "UOM", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\services\\data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\nexport interface TestData {\n  ID: number;\n  TestID: string;\n  BatchNo: string;\n  CreatedOn: string;\n  TestCode: string;\n  TestName: string;\n  Minimum: number;\n  LSL: number;\n  USL: number;\n  SD: string;\n  ED: string;\n  SubTestName: string;\n  PpL: number;\n  PpU: number;\n  CpL: number;\n  CpU: number;\n  Pp: number;\n  PpK: number;\n  Cp: number;\n  CpK: number;\n  UOM: string;\n  CPV_FLAG: string;\n}\n\nexport interface MatrixData {\n  batchNumbers: string[];\n  testNames: string[];\n  subTestsByTest: { [testName: string]: string[] };\n  dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } };\n  unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } };\n  ppPpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Pp: number | null, PpK: number | null } } };\n  cpCpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Cp: number | null, CpK: number | null } } };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DataService {\n\n  constructor(private http: HttpClient) { }\n\n  getTestData(): Observable<TestData[]> {\n    console.log('Fetching data from /assets/samplejson.json');\n    return this.http.get<TestData[]>('/assets/samplejson.json');\n  }\n\n  getMatrixData(): Observable<MatrixData> {\n    return this.getTestData().pipe(\n      map(data => {\n        console.log('Raw data from JSON:', data);\n        const transformedData = this.transformToMatrix(data);\n        console.log('Transformed matrix data:', transformedData);\n        return transformedData;\n      })\n    );\n  }\n\n  private transformToMatrix(data: TestData[]): MatrixData {\n    const batchNumbers = [...new Set(data.map(item => item.BatchNo))].sort();\n    const testNames = [...new Set(data.map(item => item.TestName))].sort();\n    \n    const subTestsByTest: { [testName: string]: string[] } = {};\n    const dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } } = {};\n    const unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } } = {};\n\n    // Initialize structures\n    testNames.forEach(testName => {\n      subTestsByTest[testName] = [...new Set(data.filter(item => item.TestName === testName).map(item => item.SubTestName))].sort();\n      unitsByTestAndSubTest[testName] = {};\n    });\n\n    batchNumbers.forEach(batchNo => {\n      dataMatrix[batchNo] = {};\n      testNames.forEach(testName => {\n        dataMatrix[batchNo][testName] = {};\n      });\n    });\n\n    // Populate data matrix and units\n    data.forEach(item => {\n      if (!dataMatrix[item.BatchNo][item.TestName][item.SubTestName]) {\n        dataMatrix[item.BatchNo][item.TestName][item.SubTestName] = item;\n        unitsByTestAndSubTest[item.TestName][item.SubTestName] = item.UOM;\n      }\n    });\n\n    return {\n      batchNumbers,\n      testNames,\n      subTestsByTest,\n      dataMatrix,\n      unitsByTestAndSubTest\n    };\n  }\n}\n"], "mappings": "AAGA,SAASA,GAAG,QAAQ,gBAAgB;;;AAwCpC,OAAM,MAAOC,WAAW;EAEtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,WAAWA,CAAA;IACTC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAa,yBAAyB,CAAC;EAC7D;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACJ,WAAW,EAAE,CAACK,IAAI,CAC5BT,GAAG,CAACU,IAAI,IAAG;MACTL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,IAAI,CAAC;MACxC,MAAMC,eAAe,GAAG,IAAI,CAACC,iBAAiB,CAACF,IAAI,CAAC;MACpDL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,eAAe,CAAC;MACxD,OAAOA,eAAe;IACxB,CAAC,CAAC,CACH;EACH;EAEQC,iBAAiBA,CAACF,IAAgB;IACxC,MAAMG,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,IAAI,CAACV,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;IACxE,MAAMC,SAAS,GAAG,CAAC,GAAG,IAAIJ,GAAG,CAACJ,IAAI,CAACV,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,EAAE;IAEtE,MAAMG,cAAc,GAAqC,EAAE;IAC3D,MAAMC,UAAU,GAAuF,EAAE;IACzG,MAAMC,qBAAqB,GAA8D,EAAE;IAE3F;IACAJ,SAAS,CAACK,OAAO,CAACC,QAAQ,IAAG;MAC3BJ,cAAc,CAACI,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAIV,GAAG,CAACJ,IAAI,CAACe,MAAM,CAACV,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAKK,QAAQ,CAAC,CAACxB,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACW,WAAW,CAAC,CAAC,CAAC,CAACT,IAAI,EAAE;MAC7HK,qBAAqB,CAACE,QAAQ,CAAC,GAAG,EAAE;IACtC,CAAC,CAAC;IAEFX,YAAY,CAACU,OAAO,CAACI,OAAO,IAAG;MAC7BN,UAAU,CAACM,OAAO,CAAC,GAAG,EAAE;MACxBT,SAAS,CAACK,OAAO,CAACC,QAAQ,IAAG;QAC3BH,UAAU,CAACM,OAAO,CAAC,CAACH,QAAQ,CAAC,GAAG,EAAE;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACAd,IAAI,CAACa,OAAO,CAACR,IAAI,IAAG;MAClB,IAAI,CAACM,UAAU,CAACN,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACW,WAAW,CAAC,EAAE;QAC9DL,UAAU,CAACN,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACW,WAAW,CAAC,GAAGX,IAAI;QAChEO,qBAAqB,CAACP,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACW,WAAW,CAAC,GAAGX,IAAI,CAACa,GAAG;MACnE;IACF,CAAC,CAAC;IAEF,OAAO;MACLf,YAAY;MACZK,SAAS;MACTE,cAAc;MACdC,UAAU;MACVC;KACD;EACH;;;uCAxDWrB,WAAW,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAX/B,WAAW;MAAAgC,OAAA,EAAXhC,WAAW,CAAAiC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}