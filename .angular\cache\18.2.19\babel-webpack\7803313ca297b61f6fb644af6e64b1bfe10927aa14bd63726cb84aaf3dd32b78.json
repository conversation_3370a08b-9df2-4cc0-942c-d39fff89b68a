{"ast": null, "code": "/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n  /**\n   * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n  /**\n   * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n  /**\n   * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n   * all the way right.\n   */\n  RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    }\n    // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return RtlScrollAxisType.NORMAL;\n  }\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n    // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n    }\n    scrollContainer.remove();\n  }\n  return rtlScrollAxisType;\n}\nexport { RtlScrollAxisType as R, getRtlScrollAxisType as g, supportsScrollBehavior as s };", "map": {"version": 3, "names": ["RtlScrollAxisType", "rtlScrollAxisType", "scrollBehaviorSupported", "supportsScrollBehavior", "document", "Element", "documentElement", "style", "scrollToFunction", "prototype", "scrollTo", "test", "toString", "getRtlScrollAxisType", "NORMAL", "scrollContainer", "createElement", "containerStyle", "dir", "width", "overflow", "visibility", "pointerEvents", "position", "content", "contentStyle", "height", "append<PERSON><PERSON><PERSON>", "body", "scrollLeft", "NEGATED", "INVERTED", "remove", "R", "g", "s"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/scrolling-BkvA05C8.mjs"], "sourcesContent": ["/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n    /**\n     * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n    /**\n     * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n    /**\n     * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return RtlScrollAxisType.NORMAL;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\nexport { RtlScrollAxisType as R, getRtlScrollAxisType as g, supportsScrollBehavior as s };\n"], "mappings": "AAAA;AACA,IAAIA,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;AACJ;AACA;AACA;EACIA,iBAAiB,CAACA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC7D;AACJ;AACA;AACA;EACIA,iBAAiB,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC/D;AACJ;AACA;AACA;EACIA,iBAAiB,CAACA,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACrE,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA,IAAIC,iBAAiB;AACrB;AACA,IAAIC,uBAAuB;AAC3B;AACA,SAASC,sBAAsBA,CAAA,EAAG;EAC9B,IAAID,uBAAuB,IAAI,IAAI,EAAE;IACjC;IACA;IACA,IAAI,OAAOE,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,IAAI,OAAOC,OAAO,KAAK,UAAU,IAAI,CAACA,OAAO,EAAE;MACxFH,uBAAuB,GAAG,KAAK;MAC/B,OAAOA,uBAAuB;IAClC;IACA;IACA,IAAI,gBAAgB,IAAIE,QAAQ,CAACE,eAAe,CAACC,KAAK,EAAE;MACpDL,uBAAuB,GAAG,IAAI;IAClC,CAAC,MACI;MACD;MACA;MACA,MAAMM,gBAAgB,GAAGH,OAAO,CAACI,SAAS,CAACC,QAAQ;MACnD,IAAIF,gBAAgB,EAAE;QAClB;QACA;QACA;QACA;QACAN,uBAAuB,GAAG,CAAC,2BAA2B,CAACS,IAAI,CAACH,gBAAgB,CAACI,QAAQ,CAAC,CAAC,CAAC;MAC5F,CAAC,MACI;QACDV,uBAAuB,GAAG,KAAK;MACnC;IACJ;EACJ;EACA,OAAOA,uBAAuB;AAClC;AACA;AACA;AACA;AACA;AACA,SAASW,oBAAoBA,CAAA,EAAG;EAC5B;EACA,IAAI,OAAOT,QAAQ,KAAK,QAAQ,IAAI,CAACA,QAAQ,EAAE;IAC3C,OAAOJ,iBAAiB,CAACc,MAAM;EACnC;EACA,IAAIb,iBAAiB,IAAI,IAAI,EAAE;IAC3B;IACA,MAAMc,eAAe,GAAGX,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;IACrD,MAAMC,cAAc,GAAGF,eAAe,CAACR,KAAK;IAC5CQ,eAAe,CAACG,GAAG,GAAG,KAAK;IAC3BD,cAAc,CAACE,KAAK,GAAG,KAAK;IAC5BF,cAAc,CAACG,QAAQ,GAAG,MAAM;IAChCH,cAAc,CAACI,UAAU,GAAG,QAAQ;IACpCJ,cAAc,CAACK,aAAa,GAAG,MAAM;IACrCL,cAAc,CAACM,QAAQ,GAAG,UAAU;IACpC,MAAMC,OAAO,GAAGpB,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;IAC7C,MAAMS,YAAY,GAAGD,OAAO,CAACjB,KAAK;IAClCkB,YAAY,CAACN,KAAK,GAAG,KAAK;IAC1BM,YAAY,CAACC,MAAM,GAAG,KAAK;IAC3BX,eAAe,CAACY,WAAW,CAACH,OAAO,CAAC;IACpCpB,QAAQ,CAACwB,IAAI,CAACD,WAAW,CAACZ,eAAe,CAAC;IAC1Cd,iBAAiB,GAAGD,iBAAiB,CAACc,MAAM;IAC5C;IACA;IACA;IACA,IAAIC,eAAe,CAACc,UAAU,KAAK,CAAC,EAAE;MAClC;MACA;MACA;MACA;MACAd,eAAe,CAACc,UAAU,GAAG,CAAC;MAC9B5B,iBAAiB,GACbc,eAAe,CAACc,UAAU,KAAK,CAAC,GAAG7B,iBAAiB,CAAC8B,OAAO,GAAG9B,iBAAiB,CAAC+B,QAAQ;IACjG;IACAhB,eAAe,CAACiB,MAAM,CAAC,CAAC;EAC5B;EACA,OAAO/B,iBAAiB;AAC5B;AAEA,SAASD,iBAAiB,IAAIiC,CAAC,EAAEpB,oBAAoB,IAAIqB,CAAC,EAAE/B,sBAAsB,IAAIgC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}