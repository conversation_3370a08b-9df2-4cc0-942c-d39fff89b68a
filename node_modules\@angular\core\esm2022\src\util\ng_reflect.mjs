/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export function normalizeDebugBindingName(name) {
    // Attribute names with `$` (eg `x-y$`) are valid per spec, but unsupported by some browsers
    name = camelCaseToDashCase(name.replace(/[$@]/g, '_'));
    return `ng-reflect-${name}`;
}
const CAMEL_CASE_REGEXP = /([A-Z])/g;
function camelCaseToDashCase(input) {
    return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());
}
export function normalizeDebugBindingValue(value) {
    try {
        // Limit the size of the value as otherwise the DOM just gets polluted.
        return value != null ? value.toString().slice(0, 30) : value;
    }
    catch (e) {
        return '[ERROR] Exception while trying to serialize the value';
    }
}
//# sourceMappingURL=data:application/json;base64,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