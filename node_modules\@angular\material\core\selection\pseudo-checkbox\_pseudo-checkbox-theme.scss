@use '../../theming/theming';
@use '../../theming/inspection';
@use '../../style/sass-utils';
@use '../../tokens/token-utils';
@use './m2-pseudo-checkbox';
@use './m3-pseudo-checkbox';
@use 'sass:map';

@mixin _palette-styles($theme, $palette-name) {
  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values-mixed(
        m2-pseudo-checkbox.get-color-tokens($theme, $palette-name));
  }
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: pseudo-checkbox,
      tokens: token-utils.get-overrides(m3-pseudo-checkbox.get-tokens(), pseudo-checkbox)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs color theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-pseudo-checkbox.get-tokens($theme, $color-variant), color));
  } @else {
    // Default to the accent color. Note that the pseudo checkboxes are meant to inherit the
    // theme from their parent, rather than implementing their own theming, which is why we
    // don't attach to the `mat-*` classes. Also note that this needs to be below `.mat-primary`
    // in order to allow for the color to be overwritten if the checkbox is inside a parent that
    // has `mat-accent` and is placed inside another parent that has `mat-primary`.
    @include _palette-styles($theme, accent);

    .mat-primary {
      @include _palette-styles($theme, primary);
    }

    .mat-accent {
      @include _palette-styles($theme, accent);
    }

    .mat-warn {
      @include _palette-styles($theme, warn);
    }
  }
}

/// Outputs typography theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
}

/// Outputs density theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-pseudo-checkbox') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
