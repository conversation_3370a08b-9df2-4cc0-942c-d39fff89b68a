{"ast": null, "code": "export { _ as _isNumberValue, a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nexport { c as coerceArray } from './array-I1yfCXUO.mjs';\nexport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n  return result;\n}\nexport { coerceBooleanProperty, coerceStringArray };", "map": {"version": 3, "names": ["_", "_isNumberValue", "a", "coerceElement", "c", "coerceNumberProperty", "coerce<PERSON><PERSON><PERSON>", "coerceCssPixelValue", "coerceBooleanProperty", "value", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "separator", "result", "sourceValues", "Array", "isArray", "split", "sourceValue", "trimmedString", "trim", "push"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/coercion.mjs"], "sourcesContent": ["export { _ as _isNumberValue, a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nexport { c as coerceArray } from './array-I1yfCXUO.mjs';\nexport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { coerceBooleanProperty, coerceStringArray };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,wBAAwB;AAC3G,SAASD,CAAC,IAAIE,WAAW,QAAQ,sBAAsB;AACvD,SAASF,CAAC,IAAIG,mBAAmB,QAAQ,gCAAgC;AACzE,OAAO,eAAe;;AAEtB;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAI,IAAI,IAAI,GAAGA,KAAK,EAAE,KAAK,OAAO;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACD,KAAK,EAAEE,SAAS,GAAG,KAAK,EAAE;EACjD,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIH,KAAK,IAAI,IAAI,EAAE;IACf,MAAMI,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,GAAGA,KAAK,EAAE,CAACO,KAAK,CAACL,SAAS,CAAC;IAC/E,KAAK,MAAMM,WAAW,IAAIJ,YAAY,EAAE;MACpC,MAAMK,aAAa,GAAG,GAAGD,WAAW,EAAE,CAACE,IAAI,CAAC,CAAC;MAC7C,IAAID,aAAa,EAAE;QACfN,MAAM,CAACQ,IAAI,CAACF,aAAa,CAAC;MAC9B;IACJ;EACJ;EACA,OAAON,MAAM;AACjB;AAEA,SAASJ,qBAAqB,EAAEE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}