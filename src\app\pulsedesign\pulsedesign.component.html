<div class="pulse-container">
  <h1>Welcome to pulse latest design</h1>

  <!-- Debug Information -->
  <div *ngIf="!loading" class="debug-info">
    <p>Loading: {{ loading }}</p>
    <p>Matrix Data Available: {{ !!matrixData }}</p>
    <p *ngIf="matrixData">Batch Numbers: {{ matrixData.batchNumbers?.length }}</p>
    <p *ngIf="matrixData">Test Names: {{ matrixData.testNames?.length }}</p>
  </div>

  <mat-card class="matrix-card" *ngIf="!loading && matrixData">
    <mat-card-header>
      <mat-card-title>Test Data Matrix</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="table-container">
        <!-- Simple table structure first -->
        <table class="matrix-table">
          <!-- Header Row with TestName and SubTestName -->
          <thead>
            <!-- Main TestName Header Row -->
            <tr class="test-name-header">
              <th class="batch-header" rowspan="2">BatchNo</th>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <th class="test-name-cell" [attr.colspan]="matrixData.subTestsByTest[testName].length">
                  {{ testName }}
                </th>
              </ng-container>
            </tr>
            <!-- SubTestName Header Row -->
            <tr class="sub-test-header">
              <ng-container *ngFor="let testName of matrixData.testNames">
                <th class="sub-test-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  {{ subTestName }}
                </th>
              </ng-container>
            </tr>
          </thead>

          <!-- Data Rows -->
          <tbody>
            <tr *ngFor="let batchNo of matrixData.batchNumbers" class="data-row">
              <td class="batch-cell">{{ batchNo }}</td>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <td class="value-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  {{ getMinimumValue(batchNo, testName, subTestName) | number:'1.2-3' }}
                </td>
              </ng-container>
            </tr>
          </tbody>

          <!-- Footer Rows -->
          <tfoot>
            <!-- Unit Row -->
            <tr class="footer-row unit-row">
              <td class="footer-label">Unit</td>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <td class="footer-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  {{ getUnit(testName, subTestName) }}
                </td>
              </ng-container>
            </tr>

            <!-- Pp | PpK Row -->
            <tr class="footer-row pp-row">
              <td class="footer-label">Pp | PpK</td>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <td class="footer-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  {{ getPpPpKValue(testName, subTestName) }}
                </td>
              </ng-container>
            </tr>

            <!-- Cp | CpK Row -->
            <tr class="footer-row cp-row">
              <td class="footer-label">Cp | CpK</td>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <td class="footer-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  {{ getCpCpKValue(testName, subTestName) }}
                </td>
              </ng-container>
            </tr>

            <!-- Actions Row -->
            <tr class="footer-row actions-row">
              <td class="footer-label">Actions</td>
              <ng-container *ngFor="let testName of matrixData.testNames">
                <td class="action-cell" *ngFor="let subTestName of matrixData.subTestsByTest[testName]">
                  <button mat-icon-button class="action-button"
                          (click)="openActionModal($event, testName, subTestName)">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                </td>
              </ng-container>
            </tr>
          </tfoot>
        </table>
      </div>
    </mat-card-content>
  </mat-card>

  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading data...</p>
  </div>

  <div *ngIf="!loading && !matrixData" class="error-container">
    <p>No data available or error loading data.</p>
  </div>

  <!-- Modal Action Dropdown -->
  <div *ngIf="showActionModal" class="modal-overlay" (click)="closeActionModal()">
    <div class="action-modal"
         [style.top]="modalPosition.top"
         [style.left]="modalPosition.left"
         (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h4>Select Action</h4>
        <button mat-icon-button (click)="closeActionModal()" class="close-button">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <mat-list class="action-list">
        <mat-list-item (click)="onPCAClick(selectedTestName, selectedSubTestName)" class="action-item">
          <mat-icon matListItemIcon>analytics</mat-icon>
          <span matListItemTitle>PCA</span>
        </mat-list-item>
        <mat-list-item (click)="onNelsonClick(selectedTestName, selectedSubTestName)" class="action-item">
          <mat-icon matListItemIcon>trending_up</mat-icon>
          <span matListItemTitle>Nelson</span>
        </mat-list-item>
        <mat-list-item (click)="onSQCClick(selectedTestName, selectedSubTestName)" class="action-item">
          <mat-icon matListItemIcon>assessment</mat-icon>
          <span matListItemTitle>SQC</span>
        </mat-list-item>
        <mat-list-item (click)="onDownloadClick(selectedTestName, selectedSubTestName)" class="action-item">
          <mat-icon matListItemIcon>download</mat-icon>
          <span matListItemTitle>Download</span>
        </mat-list-item>
      </mat-list>
    </div>
  </div>
</div>
