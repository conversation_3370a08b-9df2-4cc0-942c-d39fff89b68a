@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, elevated-card);

/// Generates the tokens for MDC elevated-card
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC elevated-card
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-utils.get-mdc-tokens('elevated-card', $systems, $exclude-hardcoded);

  @return token-utils.namespace-tokens($prefix, $mdc-tokens, $token-slots);
}
