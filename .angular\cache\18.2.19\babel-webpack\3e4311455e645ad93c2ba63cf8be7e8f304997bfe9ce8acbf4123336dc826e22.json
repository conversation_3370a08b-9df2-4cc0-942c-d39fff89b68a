{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n  return new Observable(subscriber => {\n    innerFrom(observableFactory()).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "defer", "observableFactory", "subscriber", "subscribe"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/rxjs/dist/esm/internal/observable/defer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n    return new Observable((subscriber) => {\n        innerFrom(observableFactory()).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,OAAO,SAASC,KAAKA,CAACC,iBAAiB,EAAE;EACrC,OAAO,IAAIH,UAAU,CAAEI,UAAU,IAAK;IAClCH,SAAS,CAACE,iBAAiB,CAAC,CAAC,CAAC,CAACE,SAAS,CAACD,UAAU,CAAC;EACxD,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}