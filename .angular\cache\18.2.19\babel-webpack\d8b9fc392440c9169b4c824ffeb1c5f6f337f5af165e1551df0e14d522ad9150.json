{"ast": null, "code": "export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-DLjkiju1.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-DHa4AVFz.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-DHa4AVFz.mjs';\nexport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, DOCUMENT, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-CZAE5aFC.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-CPmlyB_c.mjs';\nexport { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport '@angular/common';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  /** Map of all registered message elements that have been placed into the document. */\n  _messageRegistry = new Map();\n  /** Container for all registered messages. */\n  _messagesContainer = null;\n  /** Unique ID for the service. */\n  _id = `${nextId++}`;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AriaDescriber)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AriaDescriber,\n    factory: AriaDescriber.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  _focusTrapStack = [];\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusTrapManager = inject(FocusTrapManager);\n  _document = inject(DOCUMENT);\n  _inertStrategy;\n  _injector = inject(Injector);\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ConfigurableFocusTrapFactory,\n    factory: ConfigurableFocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };", "map": {"version": 3, "names": ["C", "CdkMonitorFocus", "d", "FOCUS_MONITOR_DEFAULT_OPTIONS", "F", "FocusMonitor", "c", "FocusMonitorDetectionMode", "a", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "b", "INPUT_MODALITY_DETECTOR_OPTIONS", "I", "InputModalityDetector", "FocusTrap", "InteractivityChecker", "A", "A11yModule", "CdkAriaLive", "CdkTrapFocus", "FocusTrapFactory", "HighContrastMode", "H", "HighContrastModeDetector", "IsFocusableConfig", "g", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "e", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "f", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "L", "LiveAnnouncer", "_", "_IdGenerator", "i0", "inject", "DOCUMENT", "APP_ID", "Injectable", "InjectionToken", "NgZone", "Injector", "P", "Platform", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "ActiveDescendantKeyManager", "FocusKeyManager", "ListKeyManager", "Subject", "T", "TREE_KEY_MANAGER", "TREE_KEY_MANAGER_FACTORY", "TREE_KEY_MANAGER_FACTORY_PROVIDER", "TreeKeyManager", "i", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "trim", "some", "existingId", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "filter", "val", "length", "removeAttribute", "attrValue", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "_platform", "_document", "_messageRegistry", "Map", "_messagesContainer", "_id", "constructor", "load", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "AriaDescriber_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "serviceId", "NoopTreeKeyManager", "_isNoopTreeKeyManager", "change", "destroy", "complete", "onKeydown", "getActiveItemIndex", "getActiveItem", "focusItem", "NOOP_TREE_KEY_MANAGER_FACTORY", "NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER", "provide", "useFactory", "ConfigurableFocusTrap", "_focusTrapManager", "_inertStrategy", "enabled", "_enabled", "value", "register", "deregister", "_element", "_checker", "_ngZone", "config", "injector", "defer", "_enable", "preventFocus", "toggleAnchors", "_disable", "allowFocus", "EventListenerFocusTrapInertStrategy", "_listener", "focusTrap", "removeEventListener", "_trapFocus", "runOutsideAngular", "addEventListener", "event", "target", "focusTrapRoot", "contains", "closest", "setTimeout", "activeElement", "focusFirstTabbableElement", "FOCUS_TRAP_INERT_STRATEGY", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "FocusTrapManager_Factory", "ConfigurableFocusTrapFactory", "_injector", "inertStrategy", "optional", "create", "configObject", "ConfigurableFocusTrapFactory_Factory"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/a11y.mjs"], "sourcesContent": ["export { C as CdkMonitorFocus, d as FOCUS_MONITOR_DEFAULT_OPTIONS, F as FocusMonitor, c as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-DLjkiju1.mjs';\nimport { a as FocusTrap, I as InteractivityChecker } from './a11y-module-DHa4AVFz.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrapFactory, b as HighContrastMode, H as HighContrastModeDetector, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-DHa4AVFz.mjs';\nexport { _ as _IdGenerator } from './id-generator-LuoRZSid.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, DOCUMENT, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-DNDzkVcI.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-B2sGQXxD.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-CZAE5aFC.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-CPmlyB_c.mjs';\nexport { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-KnCoIkIC.mjs';\nexport { b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-KnCoIkIC.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport 'rxjs/operators';\nimport './keycodes-CpHkExLC.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\nimport './element-x4z00URv.mjs';\nimport './breakpoints-observer-QutrMj4x.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport '@angular/common';\nimport './typeahead-9ZW4Dtsf.mjs';\nimport './keycodes.mjs';\nimport './coercion/private.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    if (ids.some(existingId => existingId.trim() === id)) {\n        return;\n    }\n    ids.push(id);\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    const filteredIds = ids.filter(val => val !== id);\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    const attrValue = el.getAttribute(attr);\n    return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT);\n    /** Map of all registered message elements that have been placed into the document. */\n    _messageRegistry = new Map();\n    /** Container for all registered messages. */\n    _messagesContainer = null;\n    /** Unique ID for the service. */\n    _id = `${nextId++}`;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        if (!this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AriaDescriber, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AriaDescriber, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n    _isNoopTreeKeyManager = true;\n    // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n    // implementation that does not emit to streams.\n    change = new Subject();\n    destroy() {\n        this.change.complete();\n    }\n    onKeydown() {\n        // noop\n    }\n    getActiveItemIndex() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    getActiveItem() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    focusItem() {\n        // noop\n    }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n    return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: NOOP_TREE_KEY_MANAGER_FACTORY,\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    _focusTrapManager;\n    _inertStrategy;\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n        super(_element, _checker, _ngZone, _document, config.defer, injector);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    /** Focus event handler. */\n    _listener = null;\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    _focusTrapStack = [];\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _focusTrapManager = inject(FocusTrapManager);\n    _document = inject(DOCUMENT);\n    _inertStrategy;\n    _injector = inject(Injector);\n    constructor() {\n        const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, { optional: true });\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"20.0.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, FocusTrap, InteractivityChecker, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,uCAAuC,EAAEC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,qBAAqB,QAAQ,8BAA8B;AAC1Q,SAASL,CAAC,IAAIM,SAAS,EAAEF,CAAC,IAAIG,oBAAoB,QAAQ,4BAA4B;AACtF,SAASC,CAAC,IAAIC,UAAU,EAAEf,CAAC,IAAIgB,WAAW,EAAElB,CAAC,IAAImB,YAAY,EAAEf,CAAC,IAAIgB,gBAAgB,EAAEV,CAAC,IAAIW,gBAAgB,EAAEC,CAAC,IAAIC,wBAAwB,EAAEjB,CAAC,IAAIkB,iBAAiB,EAAEC,CAAC,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,oCAAoC,EAAEC,CAAC,IAAIC,aAAa,QAAQ,4BAA4B;AAC7U,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASX,CAAC,IAAIY,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAAS9B,CAAC,IAAI+B,0BAA0B,QAAQ,6CAA6C;AAC7F,SAAS3C,CAAC,IAAI4C,eAAe,QAAQ,kCAAkC;AACvE,SAASjB,CAAC,IAAIkB,cAAc,QAAQ,iCAAiC;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,iCAAiC;AACvE,SAAS1C,CAAC,IAAI2C,wBAAwB,EAAE/C,CAAC,IAAIgD,iCAAiC,EAAE9C,CAAC,IAAI+C,cAAc,QAAQ,iCAAiC;AAC5I,SAASC,CAAC,IAAIC,+BAA+B,EAAEjD,CAAC,IAAIkD,gCAAgC,QAAQ,qCAAqC;AACjI,OAAO,gBAAgB;AACvB,OAAO,yBAAyB;AAChC,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,OAAO,wBAAwB;AAC/B,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,gBAAgB;AACvB,OAAO,wBAAwB;;AAE/B;AACA,MAAMC,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACvC,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,IAAIF,GAAG,CAACG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACF,IAAI,CAAC,CAAC,KAAKH,EAAE,CAAC,EAAE;IAClD;EACJ;EACAC,GAAG,CAACK,IAAI,CAACN,EAAE,CAAC;EACZF,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEE,GAAG,CAACO,IAAI,CAACZ,YAAY,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAACX,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC1C,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,MAAMO,WAAW,GAAGT,GAAG,CAACU,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKZ,EAAE,CAAC;EACjD,IAAIU,WAAW,CAACG,MAAM,EAAE;IACpBf,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEW,WAAW,CAACF,IAAI,CAACZ,YAAY,CAAC,CAAC;EACzD,CAAC,MACI;IACDE,EAAE,CAACgB,eAAe,CAACf,IAAI,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACJ,EAAE,EAAEC,IAAI,EAAE;EACnC;EACA,MAAMgB,SAAS,GAAGjB,EAAE,CAACkB,YAAY,CAACjB,IAAI,CAAC;EACvC,OAAOgB,SAAS,EAAEE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,mCAAmC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D;AACA,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,SAAS,GAAGlD,MAAM,CAACQ,QAAQ,CAAC;EAC5B2C,SAAS,GAAGnD,MAAM,CAACC,QAAQ,CAAC;EAC5B;EACAmD,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;EACAC,kBAAkB,GAAG,IAAI;EACzB;EACAC,GAAG,GAAG,GAAGP,MAAM,EAAE,EAAE;EACnBQ,WAAWA,CAAA,EAAG;IACVxD,MAAM,CAACS,sBAAsB,CAAC,CAACgD,IAAI,CAAC/C,qBAAqB,CAAC;IAC1D,IAAI,CAAC6C,GAAG,GAAGvD,MAAM,CAACE,MAAM,CAAC,GAAG,GAAG,GAAG8C,MAAM,EAAE;EAC9C;EACAU,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACH,WAAW,EAAEC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;MAC7B;MACAK,YAAY,CAACL,OAAO,EAAE,IAAI,CAACL,GAAG,CAAC;MAC/B,IAAI,CAACH,gBAAgB,CAACc,GAAG,CAACH,GAAG,EAAE;QAAEI,cAAc,EAAEP,OAAO;QAAEQ,cAAc,EAAE;MAAE,CAAC,CAAC;IAClF,CAAC,MACI,IAAI,CAAC,IAAI,CAAChB,gBAAgB,CAACiB,GAAG,CAACN,GAAG,CAAC,EAAE;MACtC,IAAI,CAACO,qBAAqB,CAACV,OAAO,EAAEC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACtD,IAAI,CAACS,oBAAoB,CAACb,WAAW,EAAEI,GAAG,CAAC;IAC/C;EACJ;EACAU,iBAAiBA,CAACd,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAC1C,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,CAACc,cAAc,CAACf,WAAW,CAAC,EAAE;MAC/C;IACJ;IACA,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACrD,IAAI,CAACY,uBAAuB,CAAChB,WAAW,EAAEI,GAAG,CAAC;IAClD;IACA;IACA;IACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMgB,iBAAiB,GAAG,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CAACd,GAAG,CAAC;MACxD,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAc,KAAK,CAAC,EAAE;QAC7D,IAAI,CAACU,qBAAqB,CAACf,GAAG,CAAC;MACnC;IACJ;IACA,IAAI,IAAI,CAACT,kBAAkB,EAAEyB,UAAU,CAACvC,MAAM,KAAK,CAAC,EAAE;MAClD,IAAI,CAACc,kBAAkB,CAAC0B,MAAM,CAAC,CAAC;MAChC,IAAI,CAAC1B,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACA;EACA2B,WAAWA,CAAA,EAAG;IACV,MAAMC,iBAAiB,GAAG,IAAI,CAAC/B,SAAS,CAACgC,gBAAgB,CAAC,IAAIpC,8BAA8B,KAAK,IAAI,CAACQ,GAAG,IAAI,CAAC;IAC9G,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,iBAAiB,CAAC1C,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC/C,IAAI,CAACgE,iCAAiC,CAACF,iBAAiB,CAAC9D,CAAC,CAAC,CAAC;MAC5D8D,iBAAiB,CAAC9D,CAAC,CAAC,CAACqB,eAAe,CAACM,8BAA8B,CAAC;IACxE;IACA,IAAI,CAACO,kBAAkB,EAAE0B,MAAM,CAAC,CAAC;IACjC,IAAI,CAAC1B,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACF,gBAAgB,CAACiC,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIf,qBAAqBA,CAACV,OAAO,EAAEC,IAAI,EAAE;IACjC,MAAMM,cAAc,GAAG,IAAI,CAAChB,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC1DrB,YAAY,CAACE,cAAc,EAAE,IAAI,CAACZ,GAAG,CAAC;IACtCY,cAAc,CAACoB,WAAW,GAAG3B,OAAO;IACpC,IAAIC,IAAI,EAAE;MACNM,cAAc,CAACjC,YAAY,CAAC,MAAM,EAAE2B,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAClC,kBAAkB,CAACmC,WAAW,CAACtB,cAAc,CAAC;IACnD,IAAI,CAACf,gBAAgB,CAACc,GAAG,CAACF,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC,EAAE;MAAEM,cAAc;MAAEC,cAAc,EAAE;IAAE,CAAC,CAAC;EAC3F;EACA;EACAU,qBAAqBA,CAACf,GAAG,EAAE;IACvB,IAAI,CAACX,gBAAgB,CAACyB,GAAG,CAACd,GAAG,CAAC,EAAEI,cAAc,EAAEa,MAAM,CAAC,CAAC;IACxD,IAAI,CAAC5B,gBAAgB,CAACsC,MAAM,CAAC3B,GAAG,CAAC;EACrC;EACA;EACAyB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAClC,kBAAkB,EAAE;MACzB;IACJ;IACA,MAAMqC,kBAAkB,GAAG,mCAAmC;IAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACzC,SAAS,CAACgC,gBAAgB,CAAC,IAAIQ,kBAAkB,qBAAqB,CAAC;IACrG,KAAK,IAAIvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,gBAAgB,CAACpD,MAAM,EAAEpB,CAAC,EAAE,EAAE;MAC9C;MACA;MACA;MACA;MACAwE,gBAAgB,CAACxE,CAAC,CAAC,CAAC4D,MAAM,CAAC,CAAC;IAChC;IACA,MAAMa,iBAAiB,GAAG,IAAI,CAAC1C,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC7D;IACA;IACA;IACA;IACAO,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;IAC7C;IACA;IACAF,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAACN,kBAAkB,CAAC;IACnDE,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACtD,IAAI,CAAC,IAAI,CAAC/C,SAAS,CAACgD,SAAS,EAAE;MAC3BL,iBAAiB,CAAC3D,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IACxD;IACA,IAAI,CAACiB,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACI,iBAAiB,CAAC;IAClD,IAAI,CAACvC,kBAAkB,GAAGuC,iBAAiB;EAC/C;EACA;EACAT,iCAAiCA,CAACgB,OAAO,EAAE;IACvC;IACA,MAAMC,oBAAoB,GAAGxE,mBAAmB,CAACuE,OAAO,EAAE,kBAAkB,CAAC,CAAC9D,MAAM,CAACX,EAAE,IAAIA,EAAE,CAAC2E,OAAO,CAACxD,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACtIsD,OAAO,CAAClE,YAAY,CAAC,kBAAkB,EAAEmE,oBAAoB,CAAClE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACIqC,oBAAoBA,CAAC4B,OAAO,EAAErC,GAAG,EAAE;IAC/B,MAAMa,iBAAiB,GAAG,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CAACd,GAAG,CAAC;IACxD;IACA;IACAvC,mBAAmB,CAAC4E,OAAO,EAAE,kBAAkB,EAAExB,iBAAiB,CAACT,cAAc,CAACxC,EAAE,CAAC;IACrFyE,OAAO,CAAClE,YAAY,CAACa,8BAA8B,EAAE,IAAI,CAACQ,GAAG,CAAC;IAC9DqB,iBAAiB,CAACR,cAAc,EAAE;EACtC;EACA;AACJ;AACA;AACA;EACIO,uBAAuBA,CAACyB,OAAO,EAAErC,GAAG,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CAACd,GAAG,CAAC;IACxDa,iBAAiB,CAACR,cAAc,EAAE;IAClChC,sBAAsB,CAACgE,OAAO,EAAE,kBAAkB,EAAExB,iBAAiB,CAACT,cAAc,CAACxC,EAAE,CAAC;IACxFyE,OAAO,CAAC3D,eAAe,CAACM,8BAA8B,CAAC;EAC3D;EACA;EACAwB,4BAA4BA,CAAC6B,OAAO,EAAErC,GAAG,EAAE;IACvC,MAAMwC,YAAY,GAAG1E,mBAAmB,CAACuE,OAAO,EAAE,kBAAkB,CAAC;IACrE,MAAMxB,iBAAiB,GAAG,IAAI,CAACxB,gBAAgB,CAACyB,GAAG,CAACd,GAAG,CAAC;IACxD,MAAMyC,SAAS,GAAG5B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAc,CAACxC,EAAE;IAC1E,OAAO,CAAC,CAAC6E,SAAS,IAAID,YAAY,CAACD,OAAO,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA;EACA1C,eAAeA,CAACsC,OAAO,EAAExC,OAAO,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACc,cAAc,CAAC0B,OAAO,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIxC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,MAAM6C,cAAc,GAAG7C,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,GAAGA,OAAO,EAAE,CAAC9B,IAAI,CAAC,CAAC;IACjE,MAAM4E,SAAS,GAAGN,OAAO,CAACzD,YAAY,CAAC,YAAY,CAAC;IACpD;IACA;IACA,OAAO8D,cAAc,GAAG,CAACC,SAAS,IAAIA,SAAS,CAAC5E,IAAI,CAAC,CAAC,KAAK2E,cAAc,GAAG,KAAK;EACrF;EACA;EACA/B,cAAcA,CAAC0B,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY;EAC3D;EACA,OAAOC,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF9D,aAAa;EAAA;EAChH,OAAO+D,KAAK,kBAD6EjH,EAAE,CAAAkH,kBAAA;IAAAC,KAAA,EACYjE,aAAa;IAAAkE,OAAA,EAAblE,aAAa,CAAA4D,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC5I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FtH,EAAE,CAAAuH,iBAAA,CAGJrE,aAAa,EAAc,CAAC;IAC3GsE,IAAI,EAAEpH,UAAU;IAChBqH,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,SAASpD,MAAMA,CAACJ,OAAO,EAAEC,IAAI,EAAE;EAC3B,OAAO,OAAOD,OAAO,KAAK,QAAQ,GAAG,GAAGC,IAAI,IAAI,EAAE,IAAID,OAAO,EAAE,GAAGA,OAAO;AAC7E;AACA;AACA,SAASK,YAAYA,CAACmC,OAAO,EAAEqB,SAAS,EAAE;EACtC,IAAI,CAACrB,OAAO,CAACzE,EAAE,EAAE;IACbyE,OAAO,CAACzE,EAAE,GAAG,GAAGmB,yBAAyB,IAAI2E,SAAS,IAAIzE,MAAM,EAAE,EAAE;EACxE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0E,kBAAkB,CAAC;EACrBC,qBAAqB,GAAG,IAAI;EAC5B;EACA;EACAC,MAAM,GAAG,IAAI9G,OAAO,CAAC,CAAC;EACtB+G,OAAOA,CAAA,EAAG;IACN,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC;EAC1B;EACAC,SAASA,CAAA,EAAG;IACR;EAAA;EAEJC,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,OAAO,IAAI;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR;EAAA;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAAA,EAAG;EACrC,OAAO,MAAM,IAAIT,kBAAkB,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,sCAAsC,GAAG;EAC3CC,OAAO,EAAErH,gBAAgB;EACzBsH,UAAU,EAAEH;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,qBAAqB,SAAS7J,SAAS,CAAC;EAC1C8J,iBAAiB;EACjBC,cAAc;EACd;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACH,iBAAiB,CAACK,QAAQ,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACL,iBAAiB,CAACM,UAAU,CAAC,IAAI,CAAC;IAC3C;EACJ;EACAtF,WAAWA,CAACuF,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE9F,SAAS,EAAEqF,iBAAiB,EAAEC,cAAc,EAAES,MAAM,EAAEC,QAAQ,EAAE;IACrG,KAAK,CAACJ,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE9F,SAAS,EAAE+F,MAAM,CAACE,KAAK,EAAED,QAAQ,CAAC;IACrE,IAAI,CAACX,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACD,iBAAiB,CAACK,QAAQ,CAAC,IAAI,CAAC;EACzC;EACA;EACAhB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACW,iBAAiB,CAACM,UAAU,CAAC,IAAI,CAAC;IACvC,KAAK,CAACjB,OAAO,CAAC,CAAC;EACnB;EACA;EACAwB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACZ,cAAc,CAACa,YAAY,CAAC,IAAI,CAAC;IACtC,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAC5B;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACf,cAAc,CAACgB,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAACF,aAAa,CAAC,KAAK,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMG,mCAAmC,CAAC;EACtC;EACAC,SAAS,GAAG,IAAI;EAChB;EACAL,YAAYA,CAACM,SAAS,EAAE;IACpB;IACA,IAAI,IAAI,CAACD,SAAS,EAAE;MAChBC,SAAS,CAACzG,SAAS,CAAC0G,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,SAAS,EAAE,IAAI,CAAC;IAC1E;IACA,IAAI,CAACA,SAAS,GAAIpK,CAAC,IAAK,IAAI,CAACuK,UAAU,CAACF,SAAS,EAAErK,CAAC,CAAC;IACrDqK,SAAS,CAACX,OAAO,CAACc,iBAAiB,CAAC,MAAM;MACtCH,SAAS,CAACzG,SAAS,CAAC6G,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACL,SAAS,EAAE,IAAI,CAAC;IACvE,CAAC,CAAC;EACN;EACA;EACAF,UAAUA,CAACG,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;MACjB;IACJ;IACAC,SAAS,CAACzG,SAAS,CAAC0G,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,SAAS,EAAE,IAAI,CAAC;IACtE,IAAI,CAACA,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,UAAUA,CAACF,SAAS,EAAEK,KAAK,EAAE;IACzB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMC,aAAa,GAAGP,SAAS,CAACb,QAAQ;IACxC;IACA;IACA,IAAImB,MAAM,IAAI,CAACC,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,OAAO,GAAG,sBAAsB,CAAC,EAAE;MACxF;MACA;MACA;MACAC,UAAU,CAAC,MAAM;QACb;QACA,IAAIV,SAAS,CAAClB,OAAO,IAAI,CAACyB,aAAa,CAACC,QAAQ,CAACR,SAAS,CAACzG,SAAS,CAACoH,aAAa,CAAC,EAAE;UACjFX,SAAS,CAACY,yBAAyB,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA,MAAMC,yBAAyB,GAAG,IAAIrK,cAAc,CAAC,2BAA2B,CAAC;;AAEjF;AACA,MAAMsK,gBAAgB,CAAC;EACnB;EACA;EACAC,eAAe,GAAG,EAAE;EACpB;AACJ;AACA;AACA;EACI9B,QAAQA,CAACe,SAAS,EAAE;IAChB;IACA,IAAI,CAACe,eAAe,GAAG,IAAI,CAACA,eAAe,CAACrI,MAAM,CAACsI,EAAE,IAAIA,EAAE,KAAKhB,SAAS,CAAC;IAC1E,IAAIiB,KAAK,GAAG,IAAI,CAACF,eAAe;IAChC,IAAIE,KAAK,CAACrI,MAAM,EAAE;MACdqI,KAAK,CAACA,KAAK,CAACrI,MAAM,GAAG,CAAC,CAAC,CAACgH,QAAQ,CAAC,CAAC;IACtC;IACAqB,KAAK,CAAC5I,IAAI,CAAC2H,SAAS,CAAC;IACrBA,SAAS,CAACP,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIP,UAAUA,CAACc,SAAS,EAAE;IAClBA,SAAS,CAACJ,QAAQ,CAAC,CAAC;IACpB,MAAMqB,KAAK,GAAG,IAAI,CAACF,eAAe;IAClC,MAAMvJ,CAAC,GAAGyJ,KAAK,CAACvE,OAAO,CAACsD,SAAS,CAAC;IAClC,IAAIxI,CAAC,KAAK,CAAC,CAAC,EAAE;MACVyJ,KAAK,CAACC,MAAM,CAAC1J,CAAC,EAAE,CAAC,CAAC;MAClB,IAAIyJ,KAAK,CAACrI,MAAM,EAAE;QACdqI,KAAK,CAACA,KAAK,CAACrI,MAAM,GAAG,CAAC,CAAC,CAAC6G,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;EACA,OAAOxC,IAAI,YAAAkE,yBAAAhE,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2D,gBAAgB;EAAA;EACnH,OAAO1D,KAAK,kBA5O6EjH,EAAE,CAAAkH,kBAAA;IAAAC,KAAA,EA4OYwD,gBAAgB;IAAAvD,OAAA,EAAhBuD,gBAAgB,CAAA7D,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9O6FtH,EAAE,CAAAuH,iBAAA,CA8OJoD,gBAAgB,EAAc,CAAC;IAC9GnD,IAAI,EAAEpH,UAAU;IAChBqH,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA,MAAM4D,4BAA4B,CAAC;EAC/BhC,QAAQ,GAAGhJ,MAAM,CAACrB,oBAAoB,CAAC;EACvCsK,OAAO,GAAGjJ,MAAM,CAACK,MAAM,CAAC;EACxBmI,iBAAiB,GAAGxI,MAAM,CAAC0K,gBAAgB,CAAC;EAC5CvH,SAAS,GAAGnD,MAAM,CAACC,QAAQ,CAAC;EAC5BwI,cAAc;EACdwC,SAAS,GAAGjL,MAAM,CAACM,QAAQ,CAAC;EAC5BkD,WAAWA,CAAA,EAAG;IACV,MAAM0H,aAAa,GAAGlL,MAAM,CAACyK,yBAAyB,EAAE;MAAEU,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E;IACA,IAAI,CAAC1C,cAAc,GAAGyC,aAAa,IAAI,IAAIxB,mCAAmC,CAAC,CAAC;EACpF;EACA0B,MAAMA,CAAChF,OAAO,EAAE8C,MAAM,GAAG;IAAEE,KAAK,EAAE;EAAM,CAAC,EAAE;IACvC,IAAIiC,YAAY;IAChB,IAAI,OAAOnC,MAAM,KAAK,SAAS,EAAE;MAC7BmC,YAAY,GAAG;QAAEjC,KAAK,EAAEF;MAAO,CAAC;IACpC,CAAC,MACI;MACDmC,YAAY,GAAGnC,MAAM;IACzB;IACA,OAAO,IAAIX,qBAAqB,CAACnC,OAAO,EAAE,IAAI,CAAC4C,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC9F,SAAS,EAAE,IAAI,CAACqF,iBAAiB,EAAE,IAAI,CAACC,cAAc,EAAE4C,YAAY,EAAE,IAAI,CAACJ,SAAS,CAAC;EACrK;EACA,OAAOpE,IAAI,YAAAyE,qCAAAvE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiE,4BAA4B;EAAA;EAC/H,OAAOhE,KAAK,kBA3Q6EjH,EAAE,CAAAkH,kBAAA;IAAAC,KAAA,EA2QY8D,4BAA4B;IAAA7D,OAAA,EAA5B6D,4BAA4B,CAAAnE,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7Q6FtH,EAAE,CAAAuH,iBAAA,CA6QJ0D,4BAA4B,EAAc,CAAC;IAC1HzD,IAAI,EAAEpH,UAAU;IAChBqH,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASnE,aAAa,EAAEF,8BAA8B,EAAED,yBAAyB,EAAEyF,qBAAqB,EAAEyC,4BAA4B,EAAEtB,mCAAmC,EAAEe,yBAAyB,EAAE/L,SAAS,EAAEC,oBAAoB,EAAEkE,qBAAqB,EAAEsF,6BAA6B,EAAEC,sCAAsC,EAAEV,kBAAkB,EAAE1G,gBAAgB,EAAEQ,mBAAmB,EAAEK,mBAAmB,EAAEO,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}