@use 'sass:map';
@use 'sass:math';
@use './m2-icon-button';
@use './m3-icon-button';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-icon-button.get-tokens($theme), base));
  } @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-icon-button.get-unthemable-tokens());
    }
  }
}

@mixin _m2-icon-button-variant($theme, $palette) {
  $mat-tokens: if(
    $palette,
    m2-icon-button.private-get-color-palette-color-tokens($theme, $palette),
    m2-icon-button.get-color-tokens($theme)
  );

  @include token-utils.create-token-values-mixed($mat-tokens);
}

/// Outputs color theme styles for the mat-icon-button.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant: The color variant to use for the
//         button: primary, secondary, tertiary, or error.
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-icon-button.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include _m2-icon-button-variant($theme, null);

      .mat-mdc-icon-button {
        &.mat-primary {
          @include _m2-icon-button-variant($theme, primary);
        }

        &.mat-accent {
          @include _m2-icon-button-variant($theme, accent);
        }

        &.mat-warn {
          @include _m2-icon-button-variant($theme, warn);
        }
      }
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-icon-button.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-icon-button.get-typography-tokens($theme)
      );
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-icon-button.get-tokens($theme), density));
  } @else {
    $icon-size: 24px;
    $density-scale: inspection.get-theme-density($theme);
    $size-map: (
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
      -5: 28px,
    );
    $calculated-size: map.get($size-map, $density-scale);

    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(
          m2-icon-button.get-density-tokens(
              $theme, $exclude: (icon-button-state-layer-size))
      );
    }

    // Use `mat-mdc-button-base` to increase the specificity over the button's structural styles.
    .mat-mdc-icon-button.mat-mdc-button-base {
      // Match the styles that used to be present. This is necessary for backwards
      // compat to match the previous implementations selector count (two classes).
      --mdc-icon-button-state-layer-size: #{$calculated-size};
      --mat-icon-button-state-layer-size: #{$calculated-size};

      // TODO: Switch calculated-size to "var(--mat-icon-button-state-layer-size)"
      // Currently fails validation because the variable is "undefined"
      // in the sass stack.
      // TODO: Switch icon-size to "var(--mat-icon-button-icon-size)". Currently
      // fails validation because the variable is "undefined" in the sass stack.
      width: var(--mat-icon-button-state-layer-size);
      height: var(--mat-icon-button-state-layer-size);
      padding: math.div($calculated-size - $icon-size, 2);
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: icon-button,
      tokens: token-utils.get-overrides(m3-icon-button.get-tokens(), icon-button)
    ),
  );
}

@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon-button.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant: The color variant to use for the button: primary,
//         secondary, tertiary, or error.
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-icon-button') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
