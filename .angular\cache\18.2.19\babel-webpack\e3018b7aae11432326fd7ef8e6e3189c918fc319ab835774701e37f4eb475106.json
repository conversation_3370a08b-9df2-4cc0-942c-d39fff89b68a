{"ast": null, "code": "import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { P as PAGE_DOWN, a as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-CpHkExLC.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  _items;\n  _activeItemIndex = signal(-1);\n  _activeItem = signal(null);\n  _wrap = false;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _itemChangesSubscription;\n  _vertical = true;\n  _horizontal;\n  _allowedModifierKeys = [];\n  _homeAndEnd = false;\n  _pageUpAndDown = {\n    enabled: false,\n    delta: 10\n  };\n  _effectRef;\n  _typeahead;\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  _skipPredicateFn = item => item.disabled;\n  constructor(_items, injector) {\n    this._items = _items;\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut = new Subject();\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject();\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex());\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex() - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex() + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex();\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex() < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex() < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex.set(index);\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex() + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex() + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex()) {\n        this._activeItemIndex.set(newIndex);\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nexport { ListKeyManager as L };", "map": {"version": 3, "names": ["signal", "QueryList", "isSignal", "effect", "Subscription", "Subject", "T", "Typeahead", "hasModifierKey", "P", "PAGE_DOWN", "a", "PAGE_UP", "E", "END", "H", "HOME", "L", "LEFT_ARROW", "R", "RIGHT_ARROW", "U", "UP_ARROW", "D", "DOWN_ARROW", "TAB", "ListKeyManager", "_items", "_activeItemIndex", "_activeItem", "_wrap", "_typeaheadSubscription", "EMPTY", "_itemChangesSubscription", "_vertical", "_horizontal", "_allowedModifierKeys", "_homeAndEnd", "_pageUpAndDown", "enabled", "delta", "_effectRef", "_typeahead", "_skipPredicateFn", "item", "disabled", "constructor", "injector", "changes", "subscribe", "newItems", "_itemsChanged", "toArray", "ngDevMode", "Error", "tabOut", "change", "skipPredicate", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "withHorizontalOrientation", "direction", "withAllowedModifierKeys", "keys", "withTypeAhead", "debounceInterval", "items", "_getItemsArray", "length", "some", "get<PERSON><PERSON><PERSON>", "unsubscribe", "undefined", "selectedItem", "setActiveItem", "cancelTypeahead", "reset", "withHomeAndEnd", "withPageUpDown", "previousActiveItem", "updateActiveItem", "next", "onKeydown", "event", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "indexOf", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "targetIndex", "_setActiveItemByIndex", "itemsLength", "handle<PERSON>ey", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByDelta", "itemArray", "index", "set", "setCurrentSelectedItemIndex", "destroy", "complete", "_setActiveInWrapMode", "_setActiveInDefaultMode", "i", "fallback<PERSON><PERSON><PERSON>", "setItems", "newIndex"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/list-key-manager-C7tp3RbG.mjs"], "sourcesContent": ["import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-9ZW4Dtsf.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { P as PAGE_DOWN, a as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-CpHkExLC.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    _items;\n    _activeItemIndex = signal(-1);\n    _activeItem = signal(null);\n    _wrap = false;\n    _typeaheadSubscription = Subscription.EMPTY;\n    _itemChangesSubscription;\n    _vertical = true;\n    _horizontal;\n    _allowedModifierKeys = [];\n    _homeAndEnd = false;\n    _pageUpAndDown = { enabled: false, delta: 10 };\n    _effectRef;\n    _typeahead;\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    _skipPredicateFn = (item) => item.disabled;\n    constructor(_items, injector) {\n        this._items = _items;\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => this._itemsChanged(newItems.toArray()));\n        }\n        else if (isSignal(_items)) {\n            if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('ListKeyManager constructed with a signal must receive an injector');\n            }\n            this._effectRef = effect(() => this._itemsChanged(_items()), { injector });\n        }\n    }\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    change = new Subject();\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const items = this._getItemsArray();\n            if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n                throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n            }\n        }\n        this._typeaheadSubscription.unsubscribe();\n        const items = this._getItemsArray();\n        this._typeahead = new Typeahead(items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.setActiveItem(item);\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._typeahead?.reset();\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem();\n        this.updateActiveItem(item);\n        if (this._activeItem() !== previousActiveItem) {\n            this.change.next(this._activeItemIndex());\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex() - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex() + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    this._typeahead?.handleKey(event);\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex();\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem();\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return !!this._typeahead && this._typeahead.isTyping();\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex() < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex() < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem.set(activeItem == null ? null : activeItem);\n        this._activeItemIndex.set(index);\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._effectRef?.destroy();\n        this._typeahead?.destroy();\n        this.tabOut.complete();\n        this.change.complete();\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex() + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex() + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        if (isSignal(this._items)) {\n            return this._items();\n        }\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n    /** Callback for when the items have changed. */\n    _itemsChanged(newItems) {\n        this._typeahead?.setItems(newItems);\n        const activeItem = this._activeItem();\n        if (activeItem) {\n            const newIndex = newItems.indexOf(activeItem);\n            if (newIndex > -1 && newIndex !== this._activeItemIndex()) {\n                this._activeItemIndex.set(newIndex);\n                this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n            }\n        }\n    }\n}\n\nexport { ListKeyManager as L };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AACnE,SAASC,YAAY,EAAEC,OAAO,QAAQ,MAAM;AAC5C,SAASC,CAAC,IAAIC,SAAS,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,UAAU,EAAElB,CAAC,IAAImB,GAAG,QAAQ,yBAAyB;;AAExK;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,MAAM;EACNC,gBAAgB,GAAG5B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B6B,WAAW,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC1B8B,KAAK,GAAG,KAAK;EACbC,sBAAsB,GAAG3B,YAAY,CAAC4B,KAAK;EAC3CC,wBAAwB;EACxBC,SAAS,GAAG,IAAI;EAChBC,WAAW;EACXC,oBAAoB,GAAG,EAAE;EACzBC,WAAW,GAAG,KAAK;EACnBC,cAAc,GAAG;IAAEC,OAAO,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAG,CAAC;EAC9CC,UAAU;EACVC,UAAU;EACV;AACJ;AACA;AACA;EACIC,gBAAgB,GAAIC,IAAI,IAAKA,IAAI,CAACC,QAAQ;EAC1CC,WAAWA,CAACnB,MAAM,EAAEoB,QAAQ,EAAE;IAC1B,IAAI,CAACpB,MAAM,GAAGA,MAAM;IACpB;IACA;IACA;IACA,IAAIA,MAAM,YAAY1B,SAAS,EAAE;MAC7B,IAAI,CAACgC,wBAAwB,GAAGN,MAAM,CAACqB,OAAO,CAACC,SAAS,CAAEC,QAAQ,IAAK,IAAI,CAACC,aAAa,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC,MACI,IAAIlD,QAAQ,CAACyB,MAAM,CAAC,EAAE;MACvB,IAAI,CAACoB,QAAQ,KAAK,OAAOM,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC9D,MAAM,IAAIC,KAAK,CAAC,mEAAmE,CAAC;MACxF;MACA,IAAI,CAACb,UAAU,GAAGtC,MAAM,CAAC,MAAM,IAAI,CAACgD,aAAa,CAACxB,MAAM,CAAC,CAAC,CAAC,EAAE;QAAEoB;MAAS,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;EACIQ,MAAM,GAAG,IAAIlD,OAAO,CAAC,CAAC;EACtB;EACAmD,MAAM,GAAG,IAAInD,OAAO,CAAC,CAAC;EACtB;AACJ;AACA;AACA;AACA;EACIoD,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACf,gBAAgB,GAAGe,SAAS;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,GAAG,IAAI,EAAE;IACxB,IAAI,CAAC9B,KAAK,GAAG8B,UAAU;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,uBAAuBA,CAACtB,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAACL,SAAS,GAAGK,OAAO;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIuB,yBAAyBA,CAACC,SAAS,EAAE;IACjC,IAAI,CAAC5B,WAAW,GAAG4B,SAAS;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,uBAAuBA,CAACC,IAAI,EAAE;IAC1B,IAAI,CAAC7B,oBAAoB,GAAG6B,IAAI;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACC,gBAAgB,GAAG,GAAG,EAAE;IAClC,IAAI,OAAOd,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMe,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,IAAIF,KAAK,CAACG,IAAI,CAAC3B,IAAI,IAAI,OAAOA,IAAI,CAAC4B,QAAQ,KAAK,UAAU,CAAC,EAAE;QAC7E,MAAMlB,KAAK,CAAC,8EAA8E,CAAC;MAC/F;IACJ;IACA,IAAI,CAACvB,sBAAsB,CAAC0C,WAAW,CAAC,CAAC;IACzC,MAAML,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAAC3B,UAAU,GAAG,IAAInC,SAAS,CAAC6D,KAAK,EAAE;MACnCD,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGO,SAAS;MACrFjB,aAAa,EAAEb,IAAI,IAAI,IAAI,CAACD,gBAAgB,CAACC,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAACb,sBAAsB,GAAG,IAAI,CAACW,UAAU,CAACiC,YAAY,CAAC1B,SAAS,CAACL,IAAI,IAAI;MACzE,IAAI,CAACgC,aAAa,CAAChC,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACAiC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACnC,UAAU,EAAEoC,KAAK,CAAC,CAAC;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,cAAcA,CAACxC,OAAO,GAAG,IAAI,EAAE;IAC3B,IAAI,CAACF,WAAW,GAAGE,OAAO;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyC,cAAcA,CAACzC,OAAO,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACvC,IAAI,CAACF,cAAc,GAAG;MAAEC,OAAO;MAAEC;IAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACAoC,aAAaA,CAAChC,IAAI,EAAE;IAChB,MAAMqC,kBAAkB,GAAG,IAAI,CAACpD,WAAW,CAAC,CAAC;IAC7C,IAAI,CAACqD,gBAAgB,CAACtC,IAAI,CAAC;IAC3B,IAAI,IAAI,CAACf,WAAW,CAAC,CAAC,KAAKoD,kBAAkB,EAAE;MAC3C,IAAI,CAACzB,MAAM,CAAC2B,IAAI,CAAC,IAAI,CAACvD,gBAAgB,CAAC,CAAC,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIwD,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAC9D,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAI;MAClD,OAAO,CAACL,KAAK,CAACK,QAAQ,CAAC,IAAI,IAAI,CAACtD,oBAAoB,CAACuD,OAAO,CAACD,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF,QAAQJ,OAAO;MACX,KAAK7D,GAAG;QACJ,IAAI,CAAC8B,MAAM,CAAC4B,IAAI,CAAC,CAAC;QAClB;MACJ,KAAK3D,UAAU;QACX,IAAI,IAAI,CAACU,SAAS,IAAIsD,iBAAiB,EAAE;UACrC,IAAI,CAACI,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKtE,QAAQ;QACT,IAAI,IAAI,CAACY,SAAS,IAAIsD,iBAAiB,EAAE;UACrC,IAAI,CAACK,qBAAqB,CAAC,CAAC;UAC5B;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKzE,WAAW;QACZ,IAAI,IAAI,CAACe,WAAW,IAAIqD,iBAAiB,EAAE;UACvC,IAAI,CAACrD,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC0D,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK1E,UAAU;QACX,IAAI,IAAI,CAACiB,WAAW,IAAIqD,iBAAiB,EAAE;UACvC,IAAI,CAACrD,WAAW,KAAK,KAAK,GAAG,IAAI,CAACyD,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK7E,IAAI;QACL,IAAI,IAAI,CAACqB,WAAW,IAAImD,iBAAiB,EAAE;UACvC,IAAI,CAACM,kBAAkB,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKhF,GAAG;QACJ,IAAI,IAAI,CAACuB,WAAW,IAAImD,iBAAiB,EAAE;UACvC,IAAI,CAACO,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKnF,OAAO;QACR,IAAI,IAAI,CAAC0B,cAAc,CAACC,OAAO,IAAIiD,iBAAiB,EAAE;UAClD,MAAMQ,WAAW,GAAG,IAAI,CAACpE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACU,cAAc,CAACE,KAAK;UACvE,IAAI,CAACyD,qBAAqB,CAACD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;UAChE;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKtF,SAAS;QACV,IAAI,IAAI,CAAC4B,cAAc,CAACC,OAAO,IAAIiD,iBAAiB,EAAE;UAClD,MAAMQ,WAAW,GAAG,IAAI,CAACpE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACU,cAAc,CAACE,KAAK;UACvE,MAAM0D,WAAW,GAAG,IAAI,CAAC7B,cAAc,CAAC,CAAC,CAACC,MAAM;UAChD,IAAI,CAAC2B,qBAAqB,CAACD,WAAW,GAAGE,WAAW,GAAGF,WAAW,GAAGE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzF;QACJ,CAAC,MACI;UACD;QACJ;MACJ;QACI,IAAIV,iBAAiB,IAAIhF,cAAc,CAAC6E,KAAK,EAAE,UAAU,CAAC,EAAE;UACxD,IAAI,CAAC3C,UAAU,EAAEyD,SAAS,CAACd,KAAK,CAAC;QACrC;QACA;QACA;QACA;IACR;IACA,IAAI,CAAC3C,UAAU,EAAEoC,KAAK,CAAC,CAAC;IACxBO,KAAK,CAACe,cAAc,CAAC,CAAC;EAC1B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACzE,gBAAgB,CAAC,CAAC;EAClC;EACA;EACA,IAAI0E,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACzE,WAAW,CAAC,CAAC;EAC7B;EACA;EACA0E,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC,IAAI,CAAC7D,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC6D,QAAQ,CAAC,CAAC;EAC1D;EACA;EACAT,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACG,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC5B,cAAc,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE;EACA;EACAsB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAChE,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACkE,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACU,qBAAqB,CAAC,CAAC,CAAC;EAC3F;EACA;EACAX,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACjE,gBAAgB,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACE,KAAK,GACnC,IAAI,CAACiE,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACS,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACxC;EACAtB,gBAAgBA,CAACtC,IAAI,EAAE;IACnB,MAAM6D,SAAS,GAAG,IAAI,CAACpC,cAAc,CAAC,CAAC;IACvC,MAAMqC,KAAK,GAAG,OAAO9D,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG6D,SAAS,CAACd,OAAO,CAAC/C,IAAI,CAAC;IACvE,MAAM0D,UAAU,GAAGG,SAAS,CAACC,KAAK,CAAC;IACnC;IACA,IAAI,CAAC7E,WAAW,CAAC8E,GAAG,CAACL,UAAU,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU,CAAC;IAC5D,IAAI,CAAC1E,gBAAgB,CAAC+E,GAAG,CAACD,KAAK,CAAC;IAChC,IAAI,CAAChE,UAAU,EAAEkE,2BAA2B,CAACF,KAAK,CAAC;EACvD;EACA;EACAG,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC9E,sBAAsB,CAAC0C,WAAW,CAAC,CAAC;IACzC,IAAI,CAACxC,wBAAwB,EAAEwC,WAAW,CAAC,CAAC;IAC5C,IAAI,CAAChC,UAAU,EAAEoE,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACnE,UAAU,EAAEmE,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACtD,MAAM,CAACuD,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACtD,MAAM,CAACsD,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIN,qBAAqBA,CAAChE,KAAK,EAAE;IACzB,IAAI,CAACV,KAAK,GAAG,IAAI,CAACiF,oBAAoB,CAACvE,KAAK,CAAC,GAAG,IAAI,CAACwE,uBAAuB,CAACxE,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACIuE,oBAAoBA,CAACvE,KAAK,EAAE;IACxB,MAAM4B,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI7C,KAAK,CAACE,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACpC,MAAMP,KAAK,GAAG,CAAC,IAAI,CAAC9E,gBAAgB,CAAC,CAAC,GAAGY,KAAK,GAAGyE,CAAC,GAAG7C,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACE,MAAM;MACjF,MAAM1B,IAAI,GAAGwB,KAAK,CAACsC,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAAC/D,gBAAgB,CAACC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACgC,aAAa,CAAC8B,KAAK,CAAC;QACzB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIM,uBAAuBA,CAACxE,KAAK,EAAE;IAC3B,IAAI,CAACyD,qBAAqB,CAAC,IAAI,CAACrE,gBAAgB,CAAC,CAAC,GAAGY,KAAK,EAAEA,KAAK,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;EACIyD,qBAAqBA,CAACS,KAAK,EAAEQ,aAAa,EAAE;IACxC,MAAM9C,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACD,KAAK,CAACsC,KAAK,CAAC,EAAE;MACf;IACJ;IACA,OAAO,IAAI,CAAC/D,gBAAgB,CAACyB,KAAK,CAACsC,KAAK,CAAC,CAAC,EAAE;MACxCA,KAAK,IAAIQ,aAAa;MACtB,IAAI,CAAC9C,KAAK,CAACsC,KAAK,CAAC,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAAC9B,aAAa,CAAC8B,KAAK,CAAC;EAC7B;EACA;EACArC,cAAcA,CAAA,EAAG;IACb,IAAInE,QAAQ,CAAC,IAAI,CAACyB,MAAM,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,MAAM,CAAC,CAAC;IACxB;IACA,OAAO,IAAI,CAACA,MAAM,YAAY1B,SAAS,GAAG,IAAI,CAAC0B,MAAM,CAACyB,OAAO,CAAC,CAAC,GAAG,IAAI,CAACzB,MAAM;EACjF;EACA;EACAwB,aAAaA,CAACD,QAAQ,EAAE;IACpB,IAAI,CAACR,UAAU,EAAEyE,QAAQ,CAACjE,QAAQ,CAAC;IACnC,MAAMoD,UAAU,GAAG,IAAI,CAACzE,WAAW,CAAC,CAAC;IACrC,IAAIyE,UAAU,EAAE;MACZ,MAAMc,QAAQ,GAAGlE,QAAQ,CAACyC,OAAO,CAACW,UAAU,CAAC;MAC7C,IAAIc,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAACxF,gBAAgB,CAAC,CAAC,EAAE;QACvD,IAAI,CAACA,gBAAgB,CAAC+E,GAAG,CAACS,QAAQ,CAAC;QACnC,IAAI,CAAC1E,UAAU,EAAEkE,2BAA2B,CAACQ,QAAQ,CAAC;MAC1D;IACJ;EACJ;AACJ;AAEA,SAAS1F,cAAc,IAAIT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}