/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import * as o from '../../../output/output_ast';
import { Identifiers } from '../../../render3/r3_identifiers';
import * as ir from '../ir';
// This file contains helpers for generating calls to Ivy instructions. In particular, each
// instruction type is represented as a function, which may select a specific instruction variant
// depending on the exact arguments.
export function element(slot, tag, constIndex, localRefIndex, sourceSpan) {
    return elementOrContainerBase(Identifiers.element, slot, tag, constIndex, localRefIndex, sourceSpan);
}
export function elementStart(slot, tag, constIndex, localRefIndex, sourceSpan) {
    return elementOrContainerBase(Identifiers.elementStart, slot, tag, constIndex, localRefIndex, sourceSpan);
}
function elementOrContainerBase(instruction, slot, tag, constIndex, localRefIndex, sourceSpan) {
    const args = [o.literal(slot)];
    if (tag !== null) {
        args.push(o.literal(tag));
    }
    if (localRefIndex !== null) {
        args.push(o.literal(constIndex), // might be null, but that's okay.
        o.literal(localRefIndex));
    }
    else if (constIndex !== null) {
        args.push(o.literal(constIndex));
    }
    return call(instruction, args, sourceSpan);
}
export function elementEnd(sourceSpan) {
    return call(Identifiers.elementEnd, [], sourceSpan);
}
export function elementContainerStart(slot, constIndex, localRefIndex, sourceSpan) {
    return elementOrContainerBase(Identifiers.elementContainerStart, slot, 
    /* tag */ null, constIndex, localRefIndex, sourceSpan);
}
export function elementContainer(slot, constIndex, localRefIndex, sourceSpan) {
    return elementOrContainerBase(Identifiers.elementContainer, slot, 
    /* tag */ null, constIndex, localRefIndex, sourceSpan);
}
export function elementContainerEnd() {
    return call(Identifiers.elementContainerEnd, [], null);
}
export function template(slot, templateFnRef, decls, vars, tag, constIndex, localRefs, sourceSpan) {
    const args = [
        o.literal(slot),
        templateFnRef,
        o.literal(decls),
        o.literal(vars),
        o.literal(tag),
        o.literal(constIndex),
    ];
    if (localRefs !== null) {
        args.push(o.literal(localRefs));
        args.push(o.importExpr(Identifiers.templateRefExtractor));
    }
    while (args[args.length - 1].isEquivalent(o.NULL_EXPR)) {
        args.pop();
    }
    return call(Identifiers.templateCreate, args, sourceSpan);
}
export function disableBindings() {
    return call(Identifiers.disableBindings, [], null);
}
export function enableBindings() {
    return call(Identifiers.enableBindings, [], null);
}
export function listener(name, handlerFn, eventTargetResolver, syntheticHost, sourceSpan) {
    const args = [o.literal(name), handlerFn];
    if (eventTargetResolver !== null) {
        args.push(o.literal(false)); // `useCapture` flag, defaults to `false`
        args.push(o.importExpr(eventTargetResolver));
    }
    return call(syntheticHost ? Identifiers.syntheticHostListener : Identifiers.listener, args, sourceSpan);
}
export function twoWayBindingSet(target, value) {
    return o.importExpr(Identifiers.twoWayBindingSet).callFn([target, value]);
}
export function twoWayListener(name, handlerFn, sourceSpan) {
    return call(Identifiers.twoWayListener, [o.literal(name), handlerFn], sourceSpan);
}
export function pipe(slot, name) {
    return call(Identifiers.pipe, [o.literal(slot), o.literal(name)], null);
}
export function namespaceHTML() {
    return call(Identifiers.namespaceHTML, [], null);
}
export function namespaceSVG() {
    return call(Identifiers.namespaceSVG, [], null);
}
export function namespaceMath() {
    return call(Identifiers.namespaceMathML, [], null);
}
export function advance(delta, sourceSpan) {
    return call(Identifiers.advance, delta > 1 ? [o.literal(delta)] : [], sourceSpan);
}
export function reference(slot) {
    return o.importExpr(Identifiers.reference).callFn([o.literal(slot)]);
}
export function nextContext(steps) {
    return o.importExpr(Identifiers.nextContext).callFn(steps === 1 ? [] : [o.literal(steps)]);
}
export function getCurrentView() {
    return o.importExpr(Identifiers.getCurrentView).callFn([]);
}
export function restoreView(savedView) {
    return o.importExpr(Identifiers.restoreView).callFn([savedView]);
}
export function resetView(returnValue) {
    return o.importExpr(Identifiers.resetView).callFn([returnValue]);
}
export function text(slot, initialValue, sourceSpan) {
    const args = [o.literal(slot, null)];
    if (initialValue !== '') {
        args.push(o.literal(initialValue));
    }
    return call(Identifiers.text, args, sourceSpan);
}
export function defer(selfSlot, primarySlot, dependencyResolverFn, loadingSlot, placeholderSlot, errorSlot, loadingConfig, placeholderConfig, enableTimerScheduling, sourceSpan) {
    const args = [
        o.literal(selfSlot),
        o.literal(primarySlot),
        dependencyResolverFn ?? o.literal(null),
        o.literal(loadingSlot),
        o.literal(placeholderSlot),
        o.literal(errorSlot),
        loadingConfig ?? o.literal(null),
        placeholderConfig ?? o.literal(null),
        enableTimerScheduling ? o.importExpr(Identifiers.deferEnableTimerScheduling) : o.literal(null),
    ];
    let expr;
    while ((expr = args[args.length - 1]) !== null &&
        expr instanceof o.LiteralExpr &&
        expr.value === null) {
        args.pop();
    }
    return call(Identifiers.defer, args, sourceSpan);
}
const deferTriggerToR3TriggerInstructionsMap = new Map([
    [ir.DeferTriggerKind.Idle, [Identifiers.deferOnIdle, Identifiers.deferPrefetchOnIdle]],
    [
        ir.DeferTriggerKind.Immediate,
        [Identifiers.deferOnImmediate, Identifiers.deferPrefetchOnImmediate],
    ],
    [ir.DeferTriggerKind.Timer, [Identifiers.deferOnTimer, Identifiers.deferPrefetchOnTimer]],
    [ir.DeferTriggerKind.Hover, [Identifiers.deferOnHover, Identifiers.deferPrefetchOnHover]],
    [
        ir.DeferTriggerKind.Interaction,
        [Identifiers.deferOnInteraction, Identifiers.deferPrefetchOnInteraction],
    ],
    [
        ir.DeferTriggerKind.Viewport,
        [Identifiers.deferOnViewport, Identifiers.deferPrefetchOnViewport],
    ],
]);
export function deferOn(trigger, args, prefetch, sourceSpan) {
    const instructions = deferTriggerToR3TriggerInstructionsMap.get(trigger);
    if (instructions === undefined) {
        throw new Error(`Unable to determine instruction for trigger ${trigger}`);
    }
    const instructionToCall = prefetch ? instructions[1] : instructions[0];
    return call(instructionToCall, args.map((a) => o.literal(a)), sourceSpan);
}
export function projectionDef(def) {
    return call(Identifiers.projectionDef, def ? [def] : [], null);
}
export function projection(slot, projectionSlotIndex, attributes, fallbackFnName, fallbackDecls, fallbackVars, sourceSpan) {
    const args = [o.literal(slot)];
    if (projectionSlotIndex !== 0 || attributes !== null || fallbackFnName !== null) {
        args.push(o.literal(projectionSlotIndex));
        if (attributes !== null) {
            args.push(attributes);
        }
        if (fallbackFnName !== null) {
            if (attributes === null) {
                args.push(o.literal(null));
            }
            args.push(o.variable(fallbackFnName), o.literal(fallbackDecls), o.literal(fallbackVars));
        }
    }
    return call(Identifiers.projection, args, sourceSpan);
}
export function i18nStart(slot, constIndex, subTemplateIndex, sourceSpan) {
    const args = [o.literal(slot), o.literal(constIndex)];
    if (subTemplateIndex !== null) {
        args.push(o.literal(subTemplateIndex));
    }
    return call(Identifiers.i18nStart, args, sourceSpan);
}
export function repeaterCreate(slot, viewFnName, decls, vars, tag, constIndex, trackByFn, trackByUsesComponentInstance, emptyViewFnName, emptyDecls, emptyVars, emptyTag, emptyConstIndex, sourceSpan) {
    const args = [
        o.literal(slot),
        o.variable(viewFnName),
        o.literal(decls),
        o.literal(vars),
        o.literal(tag),
        o.literal(constIndex),
        trackByFn,
    ];
    if (trackByUsesComponentInstance || emptyViewFnName !== null) {
        args.push(o.literal(trackByUsesComponentInstance));
        if (emptyViewFnName !== null) {
            args.push(o.variable(emptyViewFnName), o.literal(emptyDecls), o.literal(emptyVars));
            if (emptyTag !== null || emptyConstIndex !== null) {
                args.push(o.literal(emptyTag));
            }
            if (emptyConstIndex !== null) {
                args.push(o.literal(emptyConstIndex));
            }
        }
    }
    return call(Identifiers.repeaterCreate, args, sourceSpan);
}
export function repeater(collection, sourceSpan) {
    return call(Identifiers.repeater, [collection], sourceSpan);
}
export function deferWhen(prefetch, expr, sourceSpan) {
    return call(prefetch ? Identifiers.deferPrefetchWhen : Identifiers.deferWhen, [expr], sourceSpan);
}
export function declareLet(slot, sourceSpan) {
    return call(Identifiers.declareLet, [o.literal(slot)], sourceSpan);
}
export function storeLet(value, sourceSpan) {
    return o.importExpr(Identifiers.storeLet).callFn([value], sourceSpan);
}
export function readContextLet(slot) {
    return o.importExpr(Identifiers.readContextLet).callFn([o.literal(slot)]);
}
export function i18n(slot, constIndex, subTemplateIndex, sourceSpan) {
    const args = [o.literal(slot), o.literal(constIndex)];
    if (subTemplateIndex) {
        args.push(o.literal(subTemplateIndex));
    }
    return call(Identifiers.i18n, args, sourceSpan);
}
export function i18nEnd(endSourceSpan) {
    return call(Identifiers.i18nEnd, [], endSourceSpan);
}
export function i18nAttributes(slot, i18nAttributesConfig) {
    const args = [o.literal(slot), o.literal(i18nAttributesConfig)];
    return call(Identifiers.i18nAttributes, args, null);
}
export function property(name, expression, sanitizer, sourceSpan) {
    const args = [o.literal(name), expression];
    if (sanitizer !== null) {
        args.push(sanitizer);
    }
    return call(Identifiers.property, args, sourceSpan);
}
export function twoWayProperty(name, expression, sanitizer, sourceSpan) {
    const args = [o.literal(name), expression];
    if (sanitizer !== null) {
        args.push(sanitizer);
    }
    return call(Identifiers.twoWayProperty, args, sourceSpan);
}
export function attribute(name, expression, sanitizer, namespace) {
    const args = [o.literal(name), expression];
    if (sanitizer !== null || namespace !== null) {
        args.push(sanitizer ?? o.literal(null));
    }
    if (namespace !== null) {
        args.push(o.literal(namespace));
    }
    return call(Identifiers.attribute, args, null);
}
export function styleProp(name, expression, unit, sourceSpan) {
    const args = [o.literal(name), expression];
    if (unit !== null) {
        args.push(o.literal(unit));
    }
    return call(Identifiers.styleProp, args, sourceSpan);
}
export function classProp(name, expression, sourceSpan) {
    return call(Identifiers.classProp, [o.literal(name), expression], sourceSpan);
}
export function styleMap(expression, sourceSpan) {
    return call(Identifiers.styleMap, [expression], sourceSpan);
}
export function classMap(expression, sourceSpan) {
    return call(Identifiers.classMap, [expression], sourceSpan);
}
const PIPE_BINDINGS = [
    Identifiers.pipeBind1,
    Identifiers.pipeBind2,
    Identifiers.pipeBind3,
    Identifiers.pipeBind4,
];
export function pipeBind(slot, varOffset, args) {
    if (args.length < 1 || args.length > PIPE_BINDINGS.length) {
        throw new Error(`pipeBind() argument count out of bounds`);
    }
    const instruction = PIPE_BINDINGS[args.length - 1];
    return o.importExpr(instruction).callFn([o.literal(slot), o.literal(varOffset), ...args]);
}
export function pipeBindV(slot, varOffset, args) {
    return o.importExpr(Identifiers.pipeBindV).callFn([o.literal(slot), o.literal(varOffset), args]);
}
export function textInterpolate(strings, expressions, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    return callVariadicInstruction(TEXT_INTERPOLATE_CONFIG, [], interpolationArgs, [], sourceSpan);
}
export function i18nExp(expr, sourceSpan) {
    return call(Identifiers.i18nExp, [expr], sourceSpan);
}
export function i18nApply(slot, sourceSpan) {
    return call(Identifiers.i18nApply, [o.literal(slot)], sourceSpan);
}
export function propertyInterpolate(name, strings, expressions, sanitizer, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    const extraArgs = [];
    if (sanitizer !== null) {
        extraArgs.push(sanitizer);
    }
    return callVariadicInstruction(PROPERTY_INTERPOLATE_CONFIG, [o.literal(name)], interpolationArgs, extraArgs, sourceSpan);
}
export function attributeInterpolate(name, strings, expressions, sanitizer, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    const extraArgs = [];
    if (sanitizer !== null) {
        extraArgs.push(sanitizer);
    }
    return callVariadicInstruction(ATTRIBUTE_INTERPOLATE_CONFIG, [o.literal(name)], interpolationArgs, extraArgs, sourceSpan);
}
export function stylePropInterpolate(name, strings, expressions, unit, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    const extraArgs = [];
    if (unit !== null) {
        extraArgs.push(o.literal(unit));
    }
    return callVariadicInstruction(STYLE_PROP_INTERPOLATE_CONFIG, [o.literal(name)], interpolationArgs, extraArgs, sourceSpan);
}
export function styleMapInterpolate(strings, expressions, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    return callVariadicInstruction(STYLE_MAP_INTERPOLATE_CONFIG, [], interpolationArgs, [], sourceSpan);
}
export function classMapInterpolate(strings, expressions, sourceSpan) {
    const interpolationArgs = collateInterpolationArgs(strings, expressions);
    return callVariadicInstruction(CLASS_MAP_INTERPOLATE_CONFIG, [], interpolationArgs, [], sourceSpan);
}
export function hostProperty(name, expression, sanitizer, sourceSpan) {
    const args = [o.literal(name), expression];
    if (sanitizer !== null) {
        args.push(sanitizer);
    }
    return call(Identifiers.hostProperty, args, sourceSpan);
}
export function syntheticHostProperty(name, expression, sourceSpan) {
    return call(Identifiers.syntheticHostProperty, [o.literal(name), expression], sourceSpan);
}
export function pureFunction(varOffset, fn, args) {
    return callVariadicInstructionExpr(PURE_FUNCTION_CONFIG, [o.literal(varOffset), fn], args, [], null);
}
/**
 * Collates the string an expression arguments for an interpolation instruction.
 */
function collateInterpolationArgs(strings, expressions) {
    if (strings.length < 1 || expressions.length !== strings.length - 1) {
        throw new Error(`AssertionError: expected specific shape of args for strings/expressions in interpolation`);
    }
    const interpolationArgs = [];
    if (expressions.length === 1 && strings[0] === '' && strings[1] === '') {
        interpolationArgs.push(expressions[0]);
    }
    else {
        let idx;
        for (idx = 0; idx < expressions.length; idx++) {
            interpolationArgs.push(o.literal(strings[idx]), expressions[idx]);
        }
        // idx points at the last string.
        interpolationArgs.push(o.literal(strings[idx]));
    }
    return interpolationArgs;
}
function call(instruction, args, sourceSpan) {
    const expr = o.importExpr(instruction).callFn(args, sourceSpan);
    return ir.createStatementOp(new o.ExpressionStatement(expr, sourceSpan));
}
export function conditional(condition, contextValue, sourceSpan) {
    const args = [condition];
    if (contextValue !== null) {
        args.push(contextValue);
    }
    return call(Identifiers.conditional, args, sourceSpan);
}
/**
 * `InterpolationConfig` for the `textInterpolate` instruction.
 */
const TEXT_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.textInterpolate,
        Identifiers.textInterpolate1,
        Identifiers.textInterpolate2,
        Identifiers.textInterpolate3,
        Identifiers.textInterpolate4,
        Identifiers.textInterpolate5,
        Identifiers.textInterpolate6,
        Identifiers.textInterpolate7,
        Identifiers.textInterpolate8,
    ],
    variable: Identifiers.textInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
/**
 * `InterpolationConfig` for the `propertyInterpolate` instruction.
 */
const PROPERTY_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.propertyInterpolate,
        Identifiers.propertyInterpolate1,
        Identifiers.propertyInterpolate2,
        Identifiers.propertyInterpolate3,
        Identifiers.propertyInterpolate4,
        Identifiers.propertyInterpolate5,
        Identifiers.propertyInterpolate6,
        Identifiers.propertyInterpolate7,
        Identifiers.propertyInterpolate8,
    ],
    variable: Identifiers.propertyInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
/**
 * `InterpolationConfig` for the `stylePropInterpolate` instruction.
 */
const STYLE_PROP_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.styleProp,
        Identifiers.stylePropInterpolate1,
        Identifiers.stylePropInterpolate2,
        Identifiers.stylePropInterpolate3,
        Identifiers.stylePropInterpolate4,
        Identifiers.stylePropInterpolate5,
        Identifiers.stylePropInterpolate6,
        Identifiers.stylePropInterpolate7,
        Identifiers.stylePropInterpolate8,
    ],
    variable: Identifiers.stylePropInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
/**
 * `InterpolationConfig` for the `attributeInterpolate` instruction.
 */
const ATTRIBUTE_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.attribute,
        Identifiers.attributeInterpolate1,
        Identifiers.attributeInterpolate2,
        Identifiers.attributeInterpolate3,
        Identifiers.attributeInterpolate4,
        Identifiers.attributeInterpolate5,
        Identifiers.attributeInterpolate6,
        Identifiers.attributeInterpolate7,
        Identifiers.attributeInterpolate8,
    ],
    variable: Identifiers.attributeInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
/**
 * `InterpolationConfig` for the `styleMapInterpolate` instruction.
 */
const STYLE_MAP_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.styleMap,
        Identifiers.styleMapInterpolate1,
        Identifiers.styleMapInterpolate2,
        Identifiers.styleMapInterpolate3,
        Identifiers.styleMapInterpolate4,
        Identifiers.styleMapInterpolate5,
        Identifiers.styleMapInterpolate6,
        Identifiers.styleMapInterpolate7,
        Identifiers.styleMapInterpolate8,
    ],
    variable: Identifiers.styleMapInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
/**
 * `InterpolationConfig` for the `classMapInterpolate` instruction.
 */
const CLASS_MAP_INTERPOLATE_CONFIG = {
    constant: [
        Identifiers.classMap,
        Identifiers.classMapInterpolate1,
        Identifiers.classMapInterpolate2,
        Identifiers.classMapInterpolate3,
        Identifiers.classMapInterpolate4,
        Identifiers.classMapInterpolate5,
        Identifiers.classMapInterpolate6,
        Identifiers.classMapInterpolate7,
        Identifiers.classMapInterpolate8,
    ],
    variable: Identifiers.classMapInterpolateV,
    mapping: (n) => {
        if (n % 2 === 0) {
            throw new Error(`Expected odd number of arguments`);
        }
        return (n - 1) / 2;
    },
};
const PURE_FUNCTION_CONFIG = {
    constant: [
        Identifiers.pureFunction0,
        Identifiers.pureFunction1,
        Identifiers.pureFunction2,
        Identifiers.pureFunction3,
        Identifiers.pureFunction4,
        Identifiers.pureFunction5,
        Identifiers.pureFunction6,
        Identifiers.pureFunction7,
        Identifiers.pureFunction8,
    ],
    variable: Identifiers.pureFunctionV,
    mapping: (n) => n,
};
function callVariadicInstructionExpr(config, baseArgs, interpolationArgs, extraArgs, sourceSpan) {
    const n = config.mapping(interpolationArgs.length);
    if (n < config.constant.length) {
        // Constant calling pattern.
        return o
            .importExpr(config.constant[n])
            .callFn([...baseArgs, ...interpolationArgs, ...extraArgs], sourceSpan);
    }
    else if (config.variable !== null) {
        // Variable calling pattern.
        return o
            .importExpr(config.variable)
            .callFn([...baseArgs, o.literalArr(interpolationArgs), ...extraArgs], sourceSpan);
    }
    else {
        throw new Error(`AssertionError: unable to call variadic function`);
    }
}
function callVariadicInstruction(config, baseArgs, interpolationArgs, extraArgs, sourceSpan) {
    return ir.createStatementOp(callVariadicInstructionExpr(config, baseArgs, interpolationArgs, extraArgs, sourceSpan).toStmt());
}
//# sourceMappingURL=data:application/json;base64,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