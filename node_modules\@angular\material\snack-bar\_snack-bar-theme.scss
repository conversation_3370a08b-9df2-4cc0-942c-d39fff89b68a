@use 'sass:map';
@use '@material/snackbar/snackbar-theme' as mdc-snackbar-theme;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/style/sass-utils';
@use '../core/typography/typography';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mdc/snack-bar' as tokens-mdc-snack-bar;
@use '../core/tokens/m2/mat/snack-bar' as tokens-mat-snack-bar;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include mdc-snackbar-theme.theme(tokens-mdc-snack-bar.get-unthemable-tokens());
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-snackbar-theme.theme(tokens-mdc-snack-bar.get-color-tokens($theme));
      @include token-utils.create-token-values(
          tokens-mat-snack-bar.$prefix,
          tokens-mat-snack-bar.get-color-tokens($theme)
      );
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-snackbar-theme.theme(tokens-mdc-snack-bar.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values(
    $tokens,
    (prefix: tokens-mdc-snack-bar.$prefix, tokens: tokens-mdc-snack-bar.get-token-slots()),
    (prefix: tokens-mat-snack-bar.$prefix, tokens: tokens-mat-snack-bar.get-token-slots()),
  );
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-snack-bar') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  @if ($tokens != ()) {
    @include mdc-snackbar-theme.theme(map.get($tokens, tokens-mdc-snack-bar.$prefix));
    @include token-utils.create-token-values(
        tokens-mat-snack-bar.$prefix, map.get($tokens, tokens-mat-snack-bar.$prefix));
  }
}
