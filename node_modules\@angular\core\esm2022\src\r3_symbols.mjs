/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/*
 * This file exists to support compilation of @angular/core in Ivy mode.
 *
 * When the Angular compiler processes a compilation unit, it normally writes imports to
 * @angular/core. When compiling the core package itself this strategy isn't usable. Instead, the
 * compiler writes imports to this file.
 *
 * Only a subset of such imports are supported - core is not allowed to declare components or pipes.
 * A check in ngtsc's `R3SymbolsImportRewriter` validates this condition. The rewriter is only used
 * when compiling @angular/core and is responsible for translating an external name (prefixed with
 * ɵ) to the internal symbol name as exported below.
 *
 * The below symbols are used for @Injectable and @NgModule compilation.
 */
export { ɵɵinject } from './di/injector_compatibility';
export { ɵɵdefineInjectable, ɵɵdefineInjector } from './di/interface/defs';
export { ɵɵdefineNgModule } from './render3/definition';
export { setClassMetadata, setClassMetadataAsync } from './render3/metadata';
export { NgModuleFactory } from './render3/ng_module_ref';
export { noSideEffects as ɵnoSideEffects } from './util/closure';
/**
 * The existence of this constant (in this particular file) informs the Angular compiler that the
 * current program is actually @angular/core, which needs to be compiled specially.
 */
export const ITS_JUST_ANGULAR = true;
//# sourceMappingURL=data:application/json;base64,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