{"ast": null, "code": "import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// Angular may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static {\n    this.ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MutationObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    this._observedElements = new Map();\n    this._ngZone = inject(NgZone);\n  }\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: MutationObserverFactory\n  }], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  constructor(_contentObserver, _elementRef) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    /** Event emitted for each change in the element's content. */\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent',\n      standalone: true\n    }]\n  }], () => [{\n    type: ContentObserver\n  }, {\n    type: i0.ElementRef\n  }], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static {\n    this.ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ObserversModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["coerceElement", "coerceNumberProperty", "i0", "Injectable", "inject", "NgZone", "EventEmitter", "booleanAttribute", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "map", "filter", "debounceTime", "shouldIgnoreRecord", "record", "type", "target", "Comment", "i", "addedNodes", "length", "removedNodes", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "MutationObserverFactory_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "ContentObserver", "constructor", "_mutationObserverFactory", "_observedElements", "Map", "_ngZone", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "pipe", "records", "subscribe", "run", "next", "unsubscribe", "_unobserveElement", "runOutsideAngular", "has", "mutations", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "ContentObserver_Factory", "ɵɵinject", "CdkObserveContent", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_contentObserver", "_elementRef", "event", "_currentSubscription", "ngAfterContentInit", "CdkObserveContent_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "selector", "alias", "transform", "ObserversModule", "ObserversModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\n\n// Angular may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n    // Ignore changes to comment text.\n    if (record.type === 'characterData' && record.target instanceof Comment) {\n        return true;\n    }\n    // Ignore addition / removal of comments.\n    if (record.type === 'childList') {\n        for (let i = 0; i < record.addedNodes.length; i++) {\n            if (!(record.addedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        for (let i = 0; i < record.removedNodes.length; i++) {\n            if (!(record.removedNodes[i] instanceof Comment)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Observe everything else.\n    return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n        this._ngZone = inject(NgZone);\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream\n                .pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length))\n                .subscribe(records => {\n                this._ngZone.run(() => {\n                    observer.next(records);\n                });\n            });\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._observedElements.has(element)) {\n                const stream = new Subject();\n                const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n                if (observer) {\n                    observer.observe(element, {\n                        characterData: true,\n                        childList: true,\n                        subtree: true,\n                    });\n                }\n                this._observedElements.set(element, { observer, stream, count: 1 });\n            }\n            else {\n                this._observedElements.get(element).count++;\n            }\n            return this._observedElements.get(element).stream;\n        });\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ContentObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: MutationObserverFactory }] });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    constructor(_contentObserver, _elementRef) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.0.0\", type: CdkObserveContent, isStandalone: true, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\", booleanAttribute], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: ContentObserver }, { type: i0.ElementRef }], propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkObserveContentDisabled', transform: booleanAttribute }]\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.0\", ngImport: i0, type: ObserversModule, imports: [CdkObserveContent], exports: [CdkObserveContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.0\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkObserveContent],\n                    exports: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,oBAAoB,QAAQ,uBAAuB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9H,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;;AAE1D;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAChC;EACA,IAAIA,MAAM,CAACC,IAAI,KAAK,eAAe,IAAID,MAAM,CAACE,MAAM,YAAYC,OAAO,EAAE;IACrE,OAAO,IAAI;EACf;EACA;EACA,IAAIH,MAAM,CAACC,IAAI,KAAK,WAAW,EAAE;IAC7B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,EAAEJ,MAAM,CAACK,UAAU,CAACD,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC5C,OAAO,KAAK;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACO,YAAY,CAACD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjD,IAAI,EAAEJ,MAAM,CAACO,YAAY,CAACH,CAAC,CAAC,YAAYD,OAAO,CAAC,EAAE;QAC9C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,MAAMK,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;EAC1F;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,gCAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFN,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAACO,KAAK,kBAD6E/B,EAAE,CAAAgC,kBAAA;MAAAC,KAAA,EACYT,uBAAuB;MAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGpC,EAAE,CAAAqC,iBAAA,CAGXb,uBAAuB,EAAc,CAAC;IACrHP,IAAI,EAAEhB,UAAU;IAChBqC,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAACC,wBAAwB,EAAE;IAClC,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;IACxD;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClC,IAAI,CAACC,OAAO,GAAG1C,MAAM,CAACC,MAAM,CAAC;EACjC;EACA0C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACH,iBAAiB,CAACI,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EAClF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAGlD,aAAa,CAACqD,YAAY,CAAC;IAC3C,OAAO,IAAIzC,UAAU,CAAE0C,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;MAC5C,MAAMO,YAAY,GAAGF,MAAM,CACtBG,IAAI,CAAC5C,GAAG,CAAC6C,OAAO,IAAIA,OAAO,CAAC5C,MAAM,CAACG,MAAM,IAAI,CAACD,kBAAkB,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC4C,OAAO,IAAI,CAAC,CAACA,OAAO,CAACnC,MAAM,CAAC,CAAC,CAChHoC,SAAS,CAACD,OAAO,IAAI;QACtB,IAAI,CAACb,OAAO,CAACe,GAAG,CAAC,MAAM;UACnBP,QAAQ,CAACQ,IAAI,CAACH,OAAO,CAAC;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAO,MAAM;QACTF,YAAY,CAACM,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,iBAAiB,CAACd,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,eAAeA,CAACN,OAAO,EAAE;IACrB,OAAO,IAAI,CAACJ,OAAO,CAACmB,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACrB,iBAAiB,CAACsB,GAAG,CAAChB,OAAO,CAAC,EAAE;QACtC,MAAMK,MAAM,GAAG,IAAI1C,OAAO,CAAC,CAAC;QAC5B,MAAMyC,QAAQ,GAAG,IAAI,CAACX,wBAAwB,CAAChB,MAAM,CAACwC,SAAS,IAAIZ,MAAM,CAACO,IAAI,CAACK,SAAS,CAAC,CAAC;QAC1F,IAAIb,QAAQ,EAAE;UACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;YACtBkB,aAAa,EAAE,IAAI;YACnBC,SAAS,EAAE,IAAI;YACfC,OAAO,EAAE;UACb,CAAC,CAAC;QACN;QACA,IAAI,CAAC1B,iBAAiB,CAAC2B,GAAG,CAACrB,OAAO,EAAE;UAAEI,QAAQ;UAAEC,MAAM;UAAEiB,KAAK,EAAE;QAAE,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAAC5B,iBAAiB,CAAC6B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;MAC/C;MACA,OAAO,IAAI,CAAC5B,iBAAiB,CAAC6B,GAAG,CAACvB,OAAO,CAAC,CAACK,MAAM;IACrD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIS,iBAAiBA,CAACd,OAAO,EAAE;IACvB,IAAI,IAAI,CAACN,iBAAiB,CAACsB,GAAG,CAAChB,OAAO,CAAC,EAAE;MACrC,IAAI,CAACN,iBAAiB,CAAC6B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAAC5B,iBAAiB,CAAC6B,GAAG,CAACvB,OAAO,CAAC,CAACsB,KAAK,EAAE;QAC5C,IAAI,CAACrB,gBAAgB,CAACD,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACN,iBAAiB,CAACsB,GAAG,CAAChB,OAAO,CAAC,EAAE;MACrC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACX,iBAAiB,CAAC6B,GAAG,CAACvB,OAAO,CAAC;MAChE,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACoB,UAAU,CAAC,CAAC;MACzB;MACAnB,MAAM,CAACoB,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC/B,iBAAiB,CAACgC,MAAM,CAAC1B,OAAO,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACpB,IAAI,YAAA+C,wBAAA7C,iBAAA;MAAA,YAAAA,iBAAA,IAAwFS,eAAe,EAlFzBvC,EAAE,CAAA4E,QAAA,CAkFyCpD,uBAAuB;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACO,KAAK,kBAnF6E/B,EAAE,CAAAgC,kBAAA;MAAAC,KAAA,EAmFYM,eAAe;MAAAL,OAAA,EAAfK,eAAe,CAAAX,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArFoGpC,EAAE,CAAAqC,iBAAA,CAqFXE,eAAe,EAAc,CAAC;IAC7GtB,IAAI,EAAEhB,UAAU;IAChBqC,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElB,IAAI,EAAEO;EAAwB,CAAC,CAAC;AAAA;AACrE;AACA;AACA;AACA;AACA,MAAMqD,iBAAiB,CAAC;EACpB;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAGrF,oBAAoB,CAACiF,KAAK,CAAC;IAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;EACA1C,WAAWA,CAAC6C,gBAAgB,EAAEC,WAAW,EAAE;IACvC,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACC,KAAK,GAAG,IAAInF,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC2E,SAAS,GAAG,KAAK;IACtB,IAAI,CAACS,oBAAoB,GAAG,IAAI;EACpC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACD,oBAAoB,IAAI,CAAC,IAAI,CAACV,QAAQ,EAAE;MAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ;EACArC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoC,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAM5B,MAAM,GAAG,IAAI,CAACgC,gBAAgB,CAACnC,OAAO,CAAC,IAAI,CAACoC,WAAW,CAAC;IAC9D,IAAI,CAACE,oBAAoB,GAAG,CAAC,IAAI,CAACL,QAAQ,GAAG9B,MAAM,CAACG,IAAI,CAAC1C,YAAY,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,GAAG9B,MAAM,EAAEK,SAAS,CAAC,IAAI,CAAC6B,KAAK,CAAC;EACzH;EACAN,YAAYA,CAAA,EAAG;IACX,IAAI,CAACO,oBAAoB,EAAE3B,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAACjC,IAAI,YAAA8D,0BAAA5D,iBAAA;MAAA,YAAAA,iBAAA,IAAwF+C,iBAAiB,EAzI3B7E,EAAE,CAAA2F,iBAAA,CAyI2CpD,eAAe,GAzI5DvC,EAAE,CAAA2F,iBAAA,CAyIuE3F,EAAE,CAAC4F,UAAU;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAACC,IAAI,kBA1I8E7F,EAAE,CAAA8F,iBAAA;MAAA7E,IAAA,EA0IJ4D,iBAAiB;MAAAkB,SAAA;MAAAC,MAAA;QAAAlB,QAAA,+CAAqHzE,gBAAgB;QAAA8E,QAAA;MAAA;MAAAc,OAAA;QAAAV,KAAA;MAAA;MAAAW,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA1IpJpG,EAAE,CAAAqG,wBAAA;IAAA,EA0IsQ;EAAE;AAC9W;AACA;EAAA,QAAAjE,SAAA,oBAAAA,SAAA,KA5IoGpC,EAAE,CAAAqC,iBAAA,CA4IXwC,iBAAiB,EAAc,CAAC;IAC/G5D,IAAI,EAAEX,SAAS;IACfgC,IAAI,EAAE,CAAC;MACCgE,QAAQ,EAAE,qBAAqB;MAC/BJ,QAAQ,EAAE,mBAAmB;MAC7BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElF,IAAI,EAAEsB;EAAgB,CAAC,EAAE;IAAEtB,IAAI,EAAEjB,EAAE,CAAC4F;EAAW,CAAC,CAAC,EAAkB;IAAEL,KAAK,EAAE,CAAC;MAClGtE,IAAI,EAAEV,MAAM;MACZ+B,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEwC,QAAQ,EAAE,CAAC;MACX7D,IAAI,EAAET,KAAK;MACX8B,IAAI,EAAE,CAAC;QAAEiE,KAAK,EAAE,2BAA2B;QAAEC,SAAS,EAAEnG;MAAiB,CAAC;IAC9E,CAAC,CAAC;IAAE8E,QAAQ,EAAE,CAAC;MACXlE,IAAI,EAAET;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiG,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC7E,IAAI,YAAA8E,wBAAA5E,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2E,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA9J8E3G,EAAE,CAAA4G,gBAAA;MAAA3F,IAAA,EA8JSwF;IAAe,EAA+D;EAAE;EAC3L;IAAS,IAAI,CAACI,IAAI,kBA/J8E7G,EAAE,CAAA8G,gBAAA;MAAAC,SAAA,EA+JqC,CAACvF,uBAAuB;IAAC,EAAG;EAAE;AACzK;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KAjKoGpC,EAAE,CAAAqC,iBAAA,CAiKXoE,eAAe,EAAc,CAAC;IAC7GxF,IAAI,EAAER,QAAQ;IACd6B,IAAI,EAAE,CAAC;MACC0E,OAAO,EAAE,CAACnC,iBAAiB,CAAC;MAC5BoC,OAAO,EAAE,CAACpC,iBAAiB,CAAC;MAC5BkC,SAAS,EAAE,CAACvF,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASqD,iBAAiB,EAAEtC,eAAe,EAAEf,uBAAuB,EAAEiF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}