@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use 'sass:meta';
@use 'sass:color';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    snack-bar-container-shape: 4px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $surface: inspection.get-theme-color($theme, background, card);

  @return (
    snack-bar-container-color: if(
        meta.type-of($surface) == color,
        color.mix(if($is-dark, #fff, #000), $surface, 80%),
        $surface),
    snack-bar-supporting-text-color:
        if(meta.type-of($surface) == color, rgba($surface, 0.87), $surface),
    snack-bar-button-color:
      if(
        $is-dark,
        inspection.get-theme-color($theme, primary, 500),
        inspection.get-theme-color($theme, primary, 100)
      )
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    snack-bar-supporting-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    snack-bar-supporting-text-line-height:
        inspection.get-theme-typography($theme, body-2, line-height),
    snack-bar-supporting-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    snack-bar-supporting-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
