/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import * as o from '../../output/output_ast';
import { Identifiers as R3 } from '../r3_identifiers';
import { CONTEXT_NAME, RENDER_FLAGS, TEMPORARY_NAME, temporaryAllocator } from './util';
//  if (rf & flags) { .. }
function renderFlagCheckIfStmt(flags, statements) {
    return o.ifStmt(o.variable(RENDER_FLAGS).bitwiseAnd(o.literal(flags), null, false), statements);
}
/**
 * Translates query flags into `TQueryFlags` type in
 * packages/core/src/render3/interfaces/query.ts
 * @param query
 */
function toQueryFlags(query) {
    return ((query.descendants ? 1 /* QueryFlags.descendants */ : 0 /* QueryFlags.none */) |
        (query.static ? 2 /* QueryFlags.isStatic */ : 0 /* QueryFlags.none */) |
        (query.emitDistinctChangesOnly ? 4 /* QueryFlags.emitDistinctChangesOnly */ : 0 /* QueryFlags.none */));
}
export function getQueryPredicate(query, constantPool) {
    if (Array.isArray(query.predicate)) {
        let predicate = [];
        query.predicate.forEach((selector) => {
            // Each item in predicates array may contain strings with comma-separated refs
            // (for ex. 'ref, ref1, ..., refN'), thus we extract individual refs and store them
            // as separate array entities
            const selectors = selector.split(',').map((token) => o.literal(token.trim()));
            predicate.push(...selectors);
        });
        return constantPool.getConstLiteral(o.literalArr(predicate), true);
    }
    else {
        // The original predicate may have been wrapped in a `forwardRef()` call.
        switch (query.predicate.forwardRef) {
            case 0 /* ForwardRefHandling.None */:
            case 2 /* ForwardRefHandling.Unwrapped */:
                return query.predicate.expression;
            case 1 /* ForwardRefHandling.Wrapped */:
                return o.importExpr(R3.resolveForwardRef).callFn([query.predicate.expression]);
        }
    }
}
export function createQueryCreateCall(query, constantPool, queryTypeFns, prependParams) {
    const parameters = [];
    if (prependParams !== undefined) {
        parameters.push(...prependParams);
    }
    if (query.isSignal) {
        parameters.push(new o.ReadPropExpr(o.variable(CONTEXT_NAME), query.propertyName));
    }
    parameters.push(getQueryPredicate(query, constantPool), o.literal(toQueryFlags(query)));
    if (query.read) {
        parameters.push(query.read);
    }
    const queryCreateFn = query.isSignal ? queryTypeFns.signalBased : queryTypeFns.nonSignal;
    return o.importExpr(queryCreateFn).callFn(parameters);
}
const queryAdvancePlaceholder = Symbol('queryAdvancePlaceholder');
/**
 * Collapses query advance placeholders in a list of statements.
 *
 * This allows for less generated code because multiple sibling query advance
 * statements can be collapsed into a single call with the count as argument.
 *
 * e.g.
 *
 * ```ts
 *   bla();
 *   queryAdvance();
 *   queryAdvance();
 *   bla();
 * ```
 *
 *   --> will turn into
 *
 * ```
 *   bla();
 *   queryAdvance(2);
 *   bla();
 * ```
 */
function collapseAdvanceStatements(statements) {
    const result = [];
    let advanceCollapseCount = 0;
    const flushAdvanceCount = () => {
        if (advanceCollapseCount > 0) {
            result.unshift(o
                .importExpr(R3.queryAdvance)
                .callFn(advanceCollapseCount === 1 ? [] : [o.literal(advanceCollapseCount)])
                .toStmt());
            advanceCollapseCount = 0;
        }
    };
    // Iterate through statements in reverse and collapse advance placeholders.
    for (let i = statements.length - 1; i >= 0; i--) {
        const st = statements[i];
        if (st === queryAdvancePlaceholder) {
            advanceCollapseCount++;
        }
        else {
            flushAdvanceCount();
            result.unshift(st);
        }
    }
    flushAdvanceCount();
    return result;
}
// Define and update any view queries
export function createViewQueriesFunction(viewQueries, constantPool, name) {
    const createStatements = [];
    const updateStatements = [];
    const tempAllocator = temporaryAllocator((st) => updateStatements.push(st), TEMPORARY_NAME);
    viewQueries.forEach((query) => {
        // creation call, e.g. r3.viewQuery(somePredicate, true) or
        //                r3.viewQuerySignal(ctx.prop, somePredicate, true);
        const queryDefinitionCall = createQueryCreateCall(query, constantPool, {
            signalBased: R3.viewQuerySignal,
            nonSignal: R3.viewQuery,
        });
        createStatements.push(queryDefinitionCall.toStmt());
        // Signal queries update lazily and we just advance the index.
        if (query.isSignal) {
            updateStatements.push(queryAdvancePlaceholder);
            return;
        }
        // update, e.g. (r3.queryRefresh(tmp = r3.loadQuery()) && (ctx.someDir = tmp));
        const temporary = tempAllocator();
        const getQueryList = o.importExpr(R3.loadQuery).callFn([]);
        const refresh = o.importExpr(R3.queryRefresh).callFn([temporary.set(getQueryList)]);
        const updateDirective = o
            .variable(CONTEXT_NAME)
            .prop(query.propertyName)
            .set(query.first ? temporary.prop('first') : temporary);
        updateStatements.push(refresh.and(updateDirective).toStmt());
    });
    const viewQueryFnName = name ? `${name}_Query` : null;
    return o.fn([new o.FnParam(RENDER_FLAGS, o.NUMBER_TYPE), new o.FnParam(CONTEXT_NAME, null)], [
        renderFlagCheckIfStmt(1 /* core.RenderFlags.Create */, createStatements),
        renderFlagCheckIfStmt(2 /* core.RenderFlags.Update */, collapseAdvanceStatements(updateStatements)),
    ], o.INFERRED_TYPE, null, viewQueryFnName);
}
// Define and update any content queries
export function createContentQueriesFunction(queries, constantPool, name) {
    const createStatements = [];
    const updateStatements = [];
    const tempAllocator = temporaryAllocator((st) => updateStatements.push(st), TEMPORARY_NAME);
    for (const query of queries) {
        // creation, e.g. r3.contentQuery(dirIndex, somePredicate, true, null) or
        //                r3.contentQuerySignal(dirIndex, propName, somePredicate, <flags>, <read>).
        createStatements.push(createQueryCreateCall(query, constantPool, { nonSignal: R3.contentQuery, signalBased: R3.contentQuerySignal }, 
        /* prependParams */ [o.variable('dirIndex')]).toStmt());
        // Signal queries update lazily and we just advance the index.
        if (query.isSignal) {
            updateStatements.push(queryAdvancePlaceholder);
            continue;
        }
        // update, e.g. (r3.queryRefresh(tmp = r3.loadQuery()) && (ctx.someDir = tmp));
        const temporary = tempAllocator();
        const getQueryList = o.importExpr(R3.loadQuery).callFn([]);
        const refresh = o.importExpr(R3.queryRefresh).callFn([temporary.set(getQueryList)]);
        const updateDirective = o
            .variable(CONTEXT_NAME)
            .prop(query.propertyName)
            .set(query.first ? temporary.prop('first') : temporary);
        updateStatements.push(refresh.and(updateDirective).toStmt());
    }
    const contentQueriesFnName = name ? `${name}_ContentQueries` : null;
    return o.fn([
        new o.FnParam(RENDER_FLAGS, o.NUMBER_TYPE),
        new o.FnParam(CONTEXT_NAME, null),
        new o.FnParam('dirIndex', null),
    ], [
        renderFlagCheckIfStmt(1 /* core.RenderFlags.Create */, createStatements),
        renderFlagCheckIfStmt(2 /* core.RenderFlags.Update */, collapseAdvanceStatements(updateStatements)),
    ], o.INFERRED_TYPE, null, contentQueriesFnName);
}
//# sourceMappingURL=data:application/json;base64,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