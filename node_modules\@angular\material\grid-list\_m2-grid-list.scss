@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // TODO(crisbeto): other components have tokens for all typography dimensions.
    // Here we only set the font size to maintain the same appearance as the pre-tokens
    // theming API. Consider adding more tokens for letter spacing, font weight etc.
    grid-list-tile-header-primary-text-size:
        inspection.get-theme-typography($theme, body-2, font-size),
    grid-list-tile-header-secondary-text-size:
        inspection.get-theme-typography($theme, caption, font-size),
    grid-list-tile-footer-primary-text-size:
        inspection.get-theme-typography($theme, body-2, font-size),
    grid-list-tile-footer-secondary-text-size:
        inspection.get-theme-typography($theme, caption, font-size),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
