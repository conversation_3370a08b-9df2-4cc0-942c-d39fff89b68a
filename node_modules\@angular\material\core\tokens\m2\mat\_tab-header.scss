@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, tab-header);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // For some period of time, the MDC tabs removed the divider. This has been added back in
    // and will be present in M3.
    divider-color: transparent,
    divider-height: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $inactive-label-text-color: inspection.get-theme-color($theme, foreground, text, 0.6);
  $active-label-text-color: inspection.get-theme-color($theme, $palette-name, default);
  $ripple-color: inspection.get-theme-color($theme, $palette-name, default);

  @return (
    disabled-ripple-color: inspection.get-theme-color($theme, foreground, disabled),
    pagination-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),

    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    inactive-label-text-color: $inactive-label-text-color,
    active-label-text-color: $active-label-text-color,

    // Tokens needed to implement the gmat styles. Externally they don't change.
    active-ripple-color: $ripple-color,
    inactive-ripple-color: $ripple-color,
    inactive-focus-label-text-color: $inactive-label-text-color,
    inactive-hover-label-text-color: $inactive-label-text-color,
    active-focus-label-text-color: $active-label-text-color,
    active-hover-label-text-color: $active-label-text-color,
    active-focus-indicator-color: $active-label-text-color,
    active-hover-indicator-color: $active-label-text-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    label-text-font: inspection.get-theme-typography($theme, button, font-family),
    label-text-size: inspection.get-theme-typography($theme, button, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, button, letter-spacing),
    label-text-line-height: inspection.get-theme-typography($theme, button, line-height),
    label-text-weight: inspection.get-theme-typography($theme, button, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
          get-unthemable-tokens(),
          get-color-tokens(token-utils.$placeholder-color-config),
          get-typography-tokens(token-utils.$placeholder-typography-config),
          get-density-tokens(token-utils.$placeholder-density-config)
  );
}
