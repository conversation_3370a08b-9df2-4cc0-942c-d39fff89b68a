@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/theming/theming';
@use '../core/tokens/token-utils';
@use './m2-icon';
@use './m3-icon';
@use 'sass:map';

@mixin _palette-colors($theme, $palette-name) {
  $color: inspection.get-theme-color($theme, $palette-name, text);
  $tokens: m2-icon.private-get-icon-color-tokens($color);
  @include token-utils.create-token-values-mixed($tokens);
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-icon.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-icon.get-tokens($theme), base));
  } @else {
  }
}

/// Outputs color theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-icon.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-icon.get-color-tokens($theme));
    }

    .mat-icon {
      &.mat-primary {
        @include _palette-colors($theme, primary);
      }

      &.mat-accent {
        @include _palette-colors($theme, accent);
      }

      &.mat-warn {
        @include _palette-colors($theme, warn);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-icon.get-tokens($theme), typography));
  } @else {
  }
}

/// Outputs density theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-icon.get-tokens($theme), density));
  } @else {
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: icon,
      tokens: token-utils.get-overrides(m3-icon.get-tokens(), icon)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-icon') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
