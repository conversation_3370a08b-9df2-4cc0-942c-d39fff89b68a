/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import * as o from '../output/output_ast';
import { Identifiers as R3 } from './r3_identifiers';
import { devOnlyGuardedExpression } from './util';
export function compileClassMetadata(metadata) {
    const fnCall = internalCompileClassMetadata(metadata);
    return o.arrowFn([], [devOnlyGuardedExpression(fnCall).toStmt()]).callFn([]);
}
/** Compiles only the `setClassMetadata` call without any additional wrappers. */
function internalCompileClassMetadata(metadata) {
    return o
        .importExpr(R3.setClassMetadata)
        .callFn([
        metadata.type,
        metadata.decorators,
        metadata.ctorParameters ?? o.literal(null),
        metadata.propDecorators ?? o.literal(null),
    ]);
}
/**
 * Wraps the `setClassMetadata` function with extra logic that dynamically
 * loads dependencies from `@defer` blocks.
 *
 * Generates a call like this:
 * ```
 * setClassMetadataAsync(type, () => [
 *   import('./cmp-a').then(m => m.CmpA);
 *   import('./cmp-b').then(m => m.CmpB);
 * ], (CmpA, CmpB) => {
 *   setClassMetadata(type, decorators, ctorParameters, propParameters);
 * });
 * ```
 *
 * Similar to the `setClassMetadata` call, it's wrapped into the `ngDevMode`
 * check to tree-shake away this code in production mode.
 */
export function compileComponentClassMetadata(metadata, dependencies) {
    if (dependencies === null || dependencies.length === 0) {
        // If there are no deferrable symbols - just generate a regular `setClassMetadata` call.
        return compileClassMetadata(metadata);
    }
    return internalCompileSetClassMetadataAsync(metadata, dependencies.map((dep) => new o.FnParam(dep.symbolName, o.DYNAMIC_TYPE)), compileComponentMetadataAsyncResolver(dependencies));
}
/**
 * Identical to `compileComponentClassMetadata`. Used for the cases where we're unable to
 * analyze the deferred block dependencies, but we have a reference to the compiled
 * dependency resolver function that we can use as is.
 * @param metadata Class metadata for the internal `setClassMetadata` call.
 * @param deferResolver Expression representing the deferred dependency loading function.
 * @param deferredDependencyNames Names of the dependencies that are being loaded asynchronously.
 */
export function compileOpaqueAsyncClassMetadata(metadata, deferResolver, deferredDependencyNames) {
    return internalCompileSetClassMetadataAsync(metadata, deferredDependencyNames.map((name) => new o.FnParam(name, o.DYNAMIC_TYPE)), deferResolver);
}
/**
 * Internal logic used to compile a `setClassMetadataAsync` call.
 * @param metadata Class metadata for the internal `setClassMetadata` call.
 * @param wrapperParams Parameters to be set on the callback that wraps `setClassMetata`.
 * @param dependencyResolverFn Function to resolve the deferred dependencies.
 */
function internalCompileSetClassMetadataAsync(metadata, wrapperParams, dependencyResolverFn) {
    // Omit the wrapper since it'll be added around `setClassMetadataAsync` instead.
    const setClassMetadataCall = internalCompileClassMetadata(metadata);
    const setClassMetaWrapper = o.arrowFn(wrapperParams, [setClassMetadataCall.toStmt()]);
    const setClassMetaAsync = o
        .importExpr(R3.setClassMetadataAsync)
        .callFn([metadata.type, dependencyResolverFn, setClassMetaWrapper]);
    return o.arrowFn([], [devOnlyGuardedExpression(setClassMetaAsync).toStmt()]).callFn([]);
}
/**
 * Compiles the function that loads the dependencies for the
 * entire component in `setClassMetadataAsync`.
 */
export function compileComponentMetadataAsyncResolver(dependencies) {
    const dynamicImports = dependencies.map(({ symbolName, importPath, isDefaultImport }) => {
        // e.g. `(m) => m.CmpA`
        const innerFn = 
        // Default imports are always accessed through the `default` property.
        o.arrowFn([new o.FnParam('m', o.DYNAMIC_TYPE)], o.variable('m').prop(isDefaultImport ? 'default' : symbolName));
        // e.g. `import('./cmp-a').then(...)`
        return new o.DynamicImportExpr(importPath).prop('then').callFn([innerFn]);
    });
    // e.g. `() => [ ... ];`
    return o.arrowFn([], o.literalArr(dynamicImports));
}
//# sourceMappingURL=data:application/json;base64,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