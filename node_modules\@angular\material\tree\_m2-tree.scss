@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $foreground-base: inspection.get-theme-color($theme, foreground, base);

  @return (
    tree-container-background-color: inspection.get-theme-color($theme, background, card),
    tree-node-text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    tree-node-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    tree-node-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    tree-node-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),

    // TODO(crisbeto): provide tokens for line height and letter spacing to match other components.
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $min-height-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
    -4: 28px,
  );

  @return (
    tree-node-min-height: map.get($min-height-scale, $density-scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
