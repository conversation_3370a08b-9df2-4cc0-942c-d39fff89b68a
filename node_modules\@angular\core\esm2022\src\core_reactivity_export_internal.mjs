/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { SIGNAL as ɵSIGNAL } from '@angular/core/primitives/signals';
export { isSignal } from './render3/reactivity/api';
export { computed } from './render3/reactivity/computed';
export { signal, ɵunwrapWritableSignal, } from './render3/reactivity/signal';
export { untracked } from './render3/reactivity/untracked';
export { effect, EffectScheduler as ɵEffectScheduler, } from './render3/reactivity/effect';
export { assertNotInReactiveContext } from './render3/reactivity/asserts';
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29yZV9yZWFjdGl2aXR5X2V4cG9ydF9pbnRlcm5hbC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2NvcmVfcmVhY3Rpdml0eV9leHBvcnRfaW50ZXJuYWwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLE1BQU0sSUFBSSxPQUFPLEVBQUMsTUFBTSxrQ0FBa0MsQ0FBQztBQUVuRSxPQUFPLEVBQUMsUUFBUSxFQUEwQixNQUFNLDBCQUEwQixDQUFDO0FBQzNFLE9BQU8sRUFBQyxRQUFRLEVBQXdCLE1BQU0sK0JBQStCLENBQUM7QUFDOUUsT0FBTyxFQUVMLE1BQU0sRUFFTixxQkFBcUIsR0FDdEIsTUFBTSw2QkFBNkIsQ0FBQztBQUNyQyxPQUFPLEVBQUMsU0FBUyxFQUFDLE1BQU0sZ0NBQWdDLENBQUM7QUFDekQsT0FBTyxFQUVMLE1BQU0sRUFJTixlQUFlLElBQUksZ0JBQWdCLEdBQ3BDLE1BQU0sNkJBQTZCLENBQUM7QUFDckMsT0FBTyxFQUFDLDBCQUEwQixFQUFDLE1BQU0sOEJBQThCLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmV4cG9ydCB7U0lHTkFMIGFzIMm1U0lHTkFMfSBmcm9tICdAYW5ndWxhci9jb3JlL3ByaW1pdGl2ZXMvc2lnbmFscyc7XG5cbmV4cG9ydCB7aXNTaWduYWwsIFNpZ25hbCwgVmFsdWVFcXVhbGl0eUZufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9hcGknO1xuZXhwb3J0IHtjb21wdXRlZCwgQ3JlYXRlQ29tcHV0ZWRPcHRpb25zfSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9jb21wdXRlZCc7XG5leHBvcnQge1xuICBDcmVhdGVTaWduYWxPcHRpb25zLFxuICBzaWduYWwsXG4gIFdyaXRhYmxlU2lnbmFsLFxuICDJtXVud3JhcFdyaXRhYmxlU2lnbmFsLFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9zaWduYWwnO1xuZXhwb3J0IHt1bnRyYWNrZWR9IGZyb20gJy4vcmVuZGVyMy9yZWFjdGl2aXR5L3VudHJhY2tlZCc7XG5leHBvcnQge1xuICBDcmVhdGVFZmZlY3RPcHRpb25zLFxuICBlZmZlY3QsXG4gIEVmZmVjdFJlZixcbiAgRWZmZWN0Q2xlYW51cEZuLFxuICBFZmZlY3RDbGVhbnVwUmVnaXN0ZXJGbixcbiAgRWZmZWN0U2NoZWR1bGVyIGFzIMm1RWZmZWN0U2NoZWR1bGVyLFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9lZmZlY3QnO1xuZXhwb3J0IHthc3NlcnROb3RJblJlYWN0aXZlQ29udGV4dH0gZnJvbSAnLi9yZW5kZXIzL3JlYWN0aXZpdHkvYXNzZXJ0cyc7XG4iXX0=