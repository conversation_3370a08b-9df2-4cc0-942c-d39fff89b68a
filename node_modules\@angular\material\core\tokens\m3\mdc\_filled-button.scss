@use 'sass:map';
@use 'sass:meta';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, filled-button);

/// Generates the tokens for MDC filled-button
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC filled-button
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-utils.get-mdc-tokens('filled-button', $systems, $exclude-hardcoded);
  $variant-tokens: (
    primary: (), // Default, no overrides needed.
    secondary: (
      container-color: map.get($systems, md-sys-color, secondary),
      focus-label-text-color: map.get($systems, md-sys-color, on-secondary),
      focus-state-layer-color: map.get($systems, md-sys-color, on-secondary),
      hover-label-text-color: map.get($systems, md-sys-color, on-secondary),
      hover-state-layer-color: map.get($systems, md-sys-color, on-secondary),
      label-text-color: map.get($systems, md-sys-color, on-secondary),
      pressed-label-text-color: map.get($systems, md-sys-color, on-secondary),
      pressed-state-layer-color: map.get($systems, md-sys-color, on-secondary),
      with-icon-focus-icon-color: map.get($systems, md-sys-color, on-secondary),
      with-icon-hover-icon-color: map.get($systems, md-sys-color, on-secondary),
      with-icon-icon-color: map.get($systems, md-sys-color, on-secondary),
      with-icon-pressed-icon-color: map.get($systems, md-sys-color, on-secondary)
    ),
    tertiary: (
      container-color: map.get($systems, md-sys-color, tertiary),
      focus-label-text-color: map.get($systems, md-sys-color, on-tertiary),
      focus-state-layer-color: map.get($systems, md-sys-color, on-tertiary),
      hover-label-text-color: map.get($systems, md-sys-color, on-tertiary),
      hover-state-layer-color: map.get($systems, md-sys-color, on-tertiary),
      label-text-color: map.get($systems, md-sys-color, on-tertiary),
      pressed-label-text-color: map.get($systems, md-sys-color, on-tertiary),
      pressed-state-layer-color: map.get($systems, md-sys-color, on-tertiary),
      with-icon-focus-icon-color: map.get($systems, md-sys-color, on-tertiary),
      with-icon-hover-icon-color: map.get($systems, md-sys-color, on-tertiary),
      with-icon-icon-color: map.get($systems, md-sys-color, on-tertiary),
      with-icon-pressed-icon-color: map.get($systems, md-sys-color, on-tertiary)
    ),
    error: (
      container-color: map.get($systems, md-sys-color, error),
      focus-label-text-color: map.get($systems, md-sys-color, on-error),
      focus-state-layer-color: map.get($systems, md-sys-color, on-error),
      hover-label-text-color: map.get($systems, md-sys-color, on-error),
      hover-state-layer-color: map.get($systems, md-sys-color, on-error),
      label-text-color: map.get($systems, md-sys-color, on-error),
      pressed-label-text-color: map.get($systems, md-sys-color, on-error),
      pressed-state-layer-color: map.get($systems, md-sys-color, on-error),
      with-icon-focus-icon-color: map.get($systems, md-sys-color, on-error),
      with-icon-hover-icon-color: map.get($systems, md-sys-color, on-error),
      with-icon-icon-color: map.get($systems, md-sys-color, on-error),
      with-icon-pressed-icon-color: map.get($systems, md-sys-color, on-error)
    )
  );

  @return token-utils.namespace-tokens($prefix, (
    _fix-tokens($mdc-tokens),
    token-utils.map-values($variant-tokens, meta.get-function(_fix-tokens))
  ), $token-slots);
}


/// Fixes inconsistent values in the filled button tokens so that they can produce valid styles.
/// @param {Map} $initial-tokens Map of filled button tokens currently being generated.
/// @return {Map} The given tokens, with the invalid values replaced with valid ones.
@function _fix-tokens($initial-tokens) {
  // Need to get the hardcoded values, because they include opacities that are used for the disabled
  // state.
  $hardcoded-tokens: token-utils.get-mdc-tokens('filled-button', (), false);

  @return token-utils.combine-color-tokens($initial-tokens, $hardcoded-tokens, (
    (
      color: disabled-label-text-color,
      opacity: disabled-label-text-opacity,
    ),
    (
      color: disabled-container-color,
      opacity: disabled-container-opacity,
    )
  ));
}
