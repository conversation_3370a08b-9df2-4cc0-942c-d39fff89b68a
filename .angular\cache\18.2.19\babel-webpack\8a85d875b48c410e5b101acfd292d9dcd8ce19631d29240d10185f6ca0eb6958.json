{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.routes';\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "routes", "providers", "catch", "err", "console", "error"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.routes';\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,kBAAkB;AAEzCH,oBAAoB,CAACC,YAAY,EAAE;EACjCG,SAAS,EAAE,CACTF,aAAa,CAACC,MAAM,CAAC;CAExB,CAAC,CAACE,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}