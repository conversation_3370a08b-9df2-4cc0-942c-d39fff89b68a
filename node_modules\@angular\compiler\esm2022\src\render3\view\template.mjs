/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { Lexer } from '../../expression_parser/lexer';
import { Parser } from '../../expression_parser/parser';
import * as html from '../../ml_parser/ast';
import { DEFAULT_INTERPOLATION_CONFIG } from '../../ml_parser/defaults';
import { HtmlParser } from '../../ml_parser/html_parser';
import { WhitespaceVisitor } from '../../ml_parser/html_whitespaces';
import { DomElementSchemaRegistry } from '../../schema/dom_element_schema_registry';
import { BindingParser } from '../../template_parser/binding_parser';
import { htmlAstToRender3Ast } from '../r3_template_transform';
import { I18nMetaVisitor } from './i18n/meta';
export const LEADING_TRIVIA_CHARS = [' ', '\n', '\r', '\t'];
/**
 * Parse a template into render3 `Node`s and additional metadata, with no other dependencies.
 *
 * @param template text of the template to parse
 * @param templateUrl URL to use for source mapping of the parsed template
 * @param options options to modify how the template is parsed
 */
export function parseTemplate(template, templateUrl, options = {}) {
    const { interpolationConfig, preserveWhitespaces, enableI18nLegacyMessageIdFormat, allowInvalidAssignmentEvents, } = options;
    const bindingParser = makeBindingParser(interpolationConfig, allowInvalidAssignmentEvents);
    const htmlParser = new HtmlParser();
    const parseResult = htmlParser.parse(template, templateUrl, {
        leadingTriviaChars: LEADING_TRIVIA_CHARS,
        ...options,
        tokenizeExpansionForms: true,
        tokenizeBlocks: options.enableBlockSyntax ?? true,
        tokenizeLet: options.enableLetSyntax ?? true,
    });
    if (!options.alwaysAttemptHtmlToR3AstConversion &&
        parseResult.errors &&
        parseResult.errors.length > 0) {
        const parsedTemplate = {
            interpolationConfig,
            preserveWhitespaces,
            errors: parseResult.errors,
            nodes: [],
            styleUrls: [],
            styles: [],
            ngContentSelectors: [],
        };
        if (options.collectCommentNodes) {
            parsedTemplate.commentNodes = [];
        }
        return parsedTemplate;
    }
    let rootNodes = parseResult.rootNodes;
    // We need to use the same `retainEmptyTokens` value for both parses to avoid
    // causing a mismatch when reusing source spans, even if the
    // `preserveSignificantWhitespace` behavior is different between the two
    // parses.
    const retainEmptyTokens = !(options.preserveSignificantWhitespace ?? true);
    // process i18n meta information (scan attributes, generate ids)
    // before we run whitespace removal process, because existing i18n
    // extraction process (ng extract-i18n) relies on a raw content to generate
    // message ids
    const i18nMetaVisitor = new I18nMetaVisitor(interpolationConfig, 
    /* keepI18nAttrs */ !preserveWhitespaces, enableI18nLegacyMessageIdFormat, 
    /* containerBlocks */ undefined, options.preserveSignificantWhitespace, retainEmptyTokens);
    const i18nMetaResult = i18nMetaVisitor.visitAllWithErrors(rootNodes);
    if (!options.alwaysAttemptHtmlToR3AstConversion &&
        i18nMetaResult.errors &&
        i18nMetaResult.errors.length > 0) {
        const parsedTemplate = {
            interpolationConfig,
            preserveWhitespaces,
            errors: i18nMetaResult.errors,
            nodes: [],
            styleUrls: [],
            styles: [],
            ngContentSelectors: [],
        };
        if (options.collectCommentNodes) {
            parsedTemplate.commentNodes = [];
        }
        return parsedTemplate;
    }
    rootNodes = i18nMetaResult.rootNodes;
    if (!preserveWhitespaces) {
        // Always preserve significant whitespace here because this is used to generate the `goog.getMsg`
        // and `$localize` calls which should retain significant whitespace in order to render the
        // correct output. We let this diverge from the message IDs generated earlier which might not
        // have preserved significant whitespace.
        //
        // This should use `visitAllWithSiblings` to set `WhitespaceVisitor` context correctly, however
        // there is an existing bug where significant whitespace is not properly retained in the JS
        // output of leading/trailing whitespace for ICU messages due to the existing lack of context\
        // in `WhitespaceVisitor`. Using `visitAllWithSiblings` here would fix that bug and retain the
        // whitespace, however it would also change the runtime representation which we don't want to do
        // right now.
        rootNodes = html.visitAll(new WhitespaceVisitor(
        /* preserveSignificantWhitespace */ true, 
        /* originalNodeMap */ undefined, 
        /* requireContext */ false), rootNodes);
        // run i18n meta visitor again in case whitespaces are removed (because that might affect
        // generated i18n message content) and first pass indicated that i18n content is present in a
        // template. During this pass i18n IDs generated at the first pass will be preserved, so we can
        // mimic existing extraction process (ng extract-i18n)
        if (i18nMetaVisitor.hasI18nMeta) {
            rootNodes = html.visitAll(new I18nMetaVisitor(interpolationConfig, 
            /* keepI18nAttrs */ false, 
            /* enableI18nLegacyMessageIdFormat */ undefined, 
            /* containerBlocks */ undefined, 
            /* preserveSignificantWhitespace */ true, retainEmptyTokens), rootNodes);
        }
    }
    const { nodes, errors, styleUrls, styles, ngContentSelectors, commentNodes } = htmlAstToRender3Ast(rootNodes, bindingParser, { collectCommentNodes: !!options.collectCommentNodes });
    errors.push(...parseResult.errors, ...i18nMetaResult.errors);
    const parsedTemplate = {
        interpolationConfig,
        preserveWhitespaces,
        errors: errors.length > 0 ? errors : null,
        nodes,
        styleUrls,
        styles,
        ngContentSelectors,
    };
    if (options.collectCommentNodes) {
        parsedTemplate.commentNodes = commentNodes;
    }
    return parsedTemplate;
}
const elementRegistry = new DomElementSchemaRegistry();
/**
 * Construct a `BindingParser` with a default configuration.
 */
export function makeBindingParser(interpolationConfig = DEFAULT_INTERPOLATION_CONFIG, allowInvalidAssignmentEvents = false) {
    return new BindingParser(new Parser(new Lexer()), interpolationConfig, elementRegistry, [], allowInvalidAssignmentEvents);
}
//# sourceMappingURL=data:application/json;base64,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