@use '../tokens/m2/mat/option' as tokens-mat-option;
@use '../tokens/token-utils';
@use '../style/sass-utils';
@use '../theming/theming';
@use '../theming/inspection';
@use '../theming/validation';
@use '../typography/typography';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-option.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {}
}

/// Outputs color theme styles for the mat-option.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the selected option: primary, secondary,
///      tertiary, or error (If not specified, default secondary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(tokens-mat-option.$prefix,
        tokens-mat-option.get-color-tokens($theme));
    }

    .mat-accent {
      @include token-utils.create-token-values(tokens-mat-option.$prefix,
        tokens-mat-option.get-color-tokens($theme, accent));
    }

    .mat-warn {
      @include token-utils.create-token-values(tokens-mat-option.$prefix,
        tokens-mat-option.get-color-tokens($theme, warn));
    }
  }
}

/// Outputs typography theme styles for the mat-option.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(tokens-mat-option.$prefix,
        tokens-mat-option.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-option.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {}
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values(
    $tokens,
    (prefix: tokens-mat-option.$prefix, tokens: tokens-mat-option.get-token-slots()),
  );
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-option.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the selected option: primary, secondary,
///      tertiary, or error (If not specified, default secondary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-option') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mat-option-tokens: token-utils.get-tokens-for($tokens, tokens-mat-option.$prefix, $options...);
  @include token-utils.create-token-values(tokens-mat-option.$prefix, $mat-option-tokens);
}
