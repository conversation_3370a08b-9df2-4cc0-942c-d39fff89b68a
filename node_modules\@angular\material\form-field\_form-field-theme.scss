@use '@material/textfield/filled-text-field-theme' as mdc-filled-text-field-theme;
@use '@material/textfield/outlined-text-field-theme' as mdc-outlined-text-field-theme;
@use '../core/tokens/m2/mdc/filled-text-field' as tokens-mdc-filled-text-field;
@use '../core/tokens/m2/mdc/outlined-text-field' as tokens-mdc-outlined-text-field;
@use '../core/tokens/m2/mat/form-field' as tokens-mat-form-field;
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-form-field.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
          tokens-mdc-filled-text-field.$prefix,
          tokens-mdc-filled-text-field.get-unthemable-tokens());
      @include token-utils.create-token-values(
          tokens-mdc-outlined-text-field.$prefix,
          tokens-mdc-outlined-text-field.get-unthemable-tokens());
      @include token-utils.create-token-values(
          tokens-mat-form-field.$prefix,
          tokens-mat-form-field.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-form-field.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the form field: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-filled-text-field-theme.theme(
          tokens-mdc-filled-text-field.get-color-tokens($theme));
      @include mdc-outlined-text-field-theme.theme(
          tokens-mdc-outlined-text-field.get-color-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-form-field.$prefix,
        tokens-mat-form-field.get-color-tokens($theme));
    }

    .mat-mdc-form-field.mat-accent {
      @include mdc-filled-text-field-theme.theme(
          tokens-mdc-filled-text-field.private-get-color-palette-color-tokens($theme, accent));
      @include mdc-outlined-text-field-theme.theme(
          tokens-mdc-outlined-text-field.private-get-color-palette-color-tokens($theme, accent));
      @include token-utils.create-token-values(tokens-mat-form-field.$prefix,
        tokens-mat-form-field.private-get-color-palette-color-tokens($theme, accent));
    }

    .mat-mdc-form-field.mat-warn {
      @include mdc-filled-text-field-theme.theme(
          tokens-mdc-filled-text-field.private-get-color-palette-color-tokens($theme, warn));
      @include mdc-outlined-text-field-theme.theme(
          tokens-mdc-outlined-text-field.private-get-color-palette-color-tokens($theme, warn));
      @include token-utils.create-token-values(tokens-mat-form-field.$prefix,
        tokens-mat-form-field.private-get-color-palette-color-tokens($theme, warn));
    }
  }
}

/// Outputs typography theme styles for the mat-form-field.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-filled-text-field-theme.theme(
          tokens-mdc-filled-text-field.get-typography-tokens($theme));
      @include mdc-outlined-text-field-theme.theme(
          tokens-mdc-outlined-text-field.get-typography-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-form-field.$prefix,
        tokens-mat-form-field.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-form-field.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(tokens-mat-form-field.$prefix,
        tokens-mat-form-field.get-density-tokens($theme));
    }
  }
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  $filled-text-field-tokens: tokens-mdc-filled-text-field.get-token-slots();
  $outlined-text-field-tokens: tokens-mdc-outlined-text-field.get-token-slots();
  $form-field-tokens: tokens-mat-form-field.get-token-slots();

  @include token-utils.batch-create-token-values(
    $tokens,
    (prefix: tokens-mdc-filled-text-field.$prefix, tokens: $filled-text-field-tokens),
    (prefix: tokens-mdc-outlined-text-field.$prefix, tokens: $outlined-text-field-tokens),
    (prefix: tokens-mat-form-field.$prefix, tokens: $form-field-tokens),
  );
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-form-field.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the form field: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-form-field') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-filled-text-field-tokens:
    token-utils.get-tokens-for($tokens, tokens-mdc-filled-text-field.$prefix, $options...);
  $mdc-outlined-text-field-tokens:
    token-utils.get-tokens-for($tokens, tokens-mdc-outlined-text-field.$prefix, $options...);
  $mat-form-field-tokens:
    token-utils.get-tokens-for($tokens, tokens-mat-form-field.$prefix, $options...);
  @include mdc-filled-text-field-theme.theme($mdc-filled-text-field-tokens);
  @include mdc-outlined-text-field-theme.theme($mdc-outlined-text-field-tokens);
  @include token-utils.create-token-values(tokens-mat-form-field.$prefix, $mat-form-field-tokens);
}
