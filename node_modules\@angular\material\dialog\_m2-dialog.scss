@use '../core/tokens/m2-utils';
@use '../core/style/elevation';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    dialog-container-shape: 4px,
    dialog-container-elevation-shadow: elevation.get-box-shadow(24),
    dialog-container-max-width: 80vw,
    dialog-container-small-max-width: 80vw,
    dialog-container-min-width: 0,
    dialog-actions-alignment: start,
    dialog-actions-padding: 8px,
    dialog-content-padding: 20px 24px,
    dialog-with-actions-content-padding: 20px 24px,

    // Note: the CSS class is `title` while the M2 tokens call it `subhead`.
    // M3 calls it `headline` which is what we're aligning with.
    dialog-headline-padding: 0 24px 9px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $text-base: if(m2-utils.$private-is-internal-build,
    if($is-dark, #fff, #000),
    inspection.get-theme-color($theme, foreground, text, 1)
  );

  @return (
    dialog-container-color: inspection.get-theme-color($theme, background, dialog),
    dialog-subhead-color: sass-utils.safe-color-change($text-base, $alpha: 0.87),
    dialog-supporting-text-color: sass-utils.safe-color-change($text-base, $alpha: 0.6),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    dialog-subhead-font: inspection.get-theme-typography($theme, headline-6, font-family),
    dialog-subhead-line-height: inspection.get-theme-typography($theme, headline-6, line-height),
    dialog-subhead-size: inspection.get-theme-typography($theme, headline-6, font-size),
    dialog-subhead-weight: inspection.get-theme-typography($theme, headline-6, font-weight),
    dialog-subhead-tracking: inspection.get-theme-typography($theme, headline-6, letter-spacing),
    dialog-supporting-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    dialog-supporting-text-line-height:
        inspection.get-theme-typography($theme, body-1, line-height),
    dialog-supporting-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    dialog-supporting-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
    dialog-supporting-text-tracking:
        inspection.get-theme-typography($theme, body-1, letter-spacing),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
