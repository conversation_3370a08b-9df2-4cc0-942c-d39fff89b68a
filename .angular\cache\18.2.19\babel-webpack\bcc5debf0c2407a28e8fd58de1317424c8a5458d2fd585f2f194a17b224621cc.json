{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/data.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/progress-spinner\";\nfunction PulsedesignComponent_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Batch Numbers: \", ctx_r0.matrixData.batchNumbers == null ? null : ctx_r0.matrixData.batchNumbers.length, \"\");\n  }\n}\nfunction PulsedesignComponent_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Test Names: \", ctx_r0.matrixData.testNames == null ? null : ctx_r0.matrixData.testNames.length, \"\");\n  }\n}\nfunction PulsedesignComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, PulsedesignComponent_div_3_p_5_Template, 2, 1, \"p\", 6)(6, PulsedesignComponent_div_3_p_6_Template, 2, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Loading: \", ctx_r0.loading, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Matrix Data Available: \", !!ctx_r0.matrixData, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.matrixData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.matrixData);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"colspan\", ctx_r0.matrixData.subTestsByTest[testName_r2].length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", testName_r2, \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", subTestName_r3, \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template, 2, 1, \"th\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r4]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r5 = ctx.$implicit;\n    const testName_r6 = i0.ɵɵnextContext().$implicit;\n    const batchNo_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, ctx_r0.getMinimumValue(batchNo_r7, testName_r6, subTestName_r5), \"1.2-3\"), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template, 3, 4, \"td\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r6]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 22)(1, \"td\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const batchNo_r7 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(batchNo_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r8 = ctx.$implicit;\n    const testName_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getUnit(testName_r9, subTestName_r8), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template, 2, 1, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r9 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r9]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r10 = ctx.$implicit;\n    const testName_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getPpPpKValue(testName_r11, subTestName_r10), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template, 2, 1, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r11 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r11]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subTestName_r12 = ctx.$implicit;\n    const testName_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getCpCpKValue(testName_r13, subTestName_r12), \" \");\n  }\n}\nfunction PulsedesignComponent_mat_card_4_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template, 2, 1, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const testName_r13 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.subTestsByTest[testName_r13]);\n  }\n}\nfunction PulsedesignComponent_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 7)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Test Data Matrix\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 8)(6, \"table\", 9)(7, \"thead\")(8, \"tr\", 10)(9, \"th\", 11);\n    i0.ɵɵtext(10, \"BatchNo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, PulsedesignComponent_mat_card_4_ng_container_11_Template, 3, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"tr\", 13);\n    i0.ɵɵtemplate(13, PulsedesignComponent_mat_card_4_ng_container_13_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, PulsedesignComponent_mat_card_4_tr_15_Template, 4, 2, \"tr\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"tfoot\")(17, \"tr\", 15)(18, \"td\", 16);\n    i0.ɵɵtext(19, \"Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, PulsedesignComponent_mat_card_4_ng_container_20_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"tr\", 17)(22, \"td\", 16);\n    i0.ɵɵtext(23, \"Pp | PpK\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, PulsedesignComponent_mat_card_4_ng_container_24_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"tr\", 18)(26, \"td\", 16);\n    i0.ɵɵtext(27, \"Cp | CpK\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, PulsedesignComponent_mat_card_4_ng_container_28_Template, 2, 1, \"ng-container\", 12);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.batchNumbers);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.matrixData.testNames);\n  }\n}\nfunction PulsedesignComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PulsedesignComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\");\n    i0.ɵɵtext(2, \"No data available or error loading data.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class PulsedesignComponent {\n  constructor(dataService) {\n    this.dataService = dataService;\n    this.matrixData = null;\n    this.loading = true;\n    this.displayedColumns = [];\n  }\n  ngOnInit() {\n    console.log('Component initialized, loading data...');\n    this.dataService.getMatrixData().subscribe({\n      next: data => {\n        console.log('Data received:', data);\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n        console.log('Loading complete, matrixData:', this.matrixData);\n      },\n      error: error => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n  setupDisplayedColumns() {\n    if (!this.matrixData) return;\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n  getMinimumValue(batchNo, testName, subTestName) {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n  getPpPpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const ppPpKData = this.matrixData.ppPpKByTestAndSubTest[testName]?.[subTestName];\n    if (ppPpKData && ppPpKData.Pp !== null && ppPpKData.PpK !== null) {\n      return `${ppPpKData.Pp.toFixed(3)} | ${ppPpKData.PpK.toFixed(3)}`;\n    }\n    return '';\n  }\n  getCpCpKValue(testName, subTestName) {\n    if (!this.matrixData) return '';\n    const cpCpKData = this.matrixData.cpCpKByTestAndSubTest[testName]?.[subTestName];\n    if (cpCpKData && cpCpKData.Cp !== null && cpCpKData.CpK !== null) {\n      return `${cpCpKData.Cp.toFixed(3)} | ${cpCpKData.CpK.toFixed(3)}`;\n    }\n    return '';\n  }\n  getUnit(testName, subTestName) {\n    if (!this.matrixData) return '';\n    // First try the pre-computed units\n    const unit = this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName];\n    if (unit) return unit;\n    // Fallback: find any batch that has data for this testName and subTestName combination\n    for (const batchNo of this.matrixData.batchNumbers) {\n      const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n      if (data) {\n        return data.UOM;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function PulsedesignComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PulsedesignComponent)(i0.ɵɵdirectiveInject(i1.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PulsedesignComponent,\n      selectors: [[\"app-pulsedesign\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 4,\n      consts: [[1, \"pulse-container\"], [\"class\", \"debug-info\", 4, \"ngIf\"], [\"class\", \"matrix-card\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"debug-info\"], [4, \"ngIf\"], [1, \"matrix-card\"], [1, \"table-container\"], [1, \"matrix-table\"], [1, \"test-name-header\"], [\"rowspan\", \"2\", 1, \"batch-header\"], [4, \"ngFor\", \"ngForOf\"], [1, \"sub-test-header\"], [\"class\", \"data-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-row\", \"unit-row\"], [1, \"footer-label\"], [1, \"footer-row\", \"pp-row\"], [1, \"footer-row\", \"cp-row\"], [1, \"test-name-cell\"], [\"class\", \"sub-test-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"sub-test-cell\"], [1, \"data-row\"], [1, \"batch-cell\"], [\"class\", \"value-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"value-cell\"], [\"class\", \"footer-cell\", 4, \"ngFor\", \"ngForOf\"], [1, \"footer-cell\"], [1, \"loading-container\"], [1, \"error-container\"]],\n      template: function PulsedesignComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\");\n          i0.ɵɵtext(2, \"Welcome to pulse latest design\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, PulsedesignComponent_div_3_Template, 7, 4, \"div\", 1)(4, PulsedesignComponent_mat_card_4_Template, 29, 6, \"mat-card\", 2)(5, PulsedesignComponent_div_5_Template, 4, 0, \"div\", 3)(6, PulsedesignComponent_div_6_Template, 3, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.matrixData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.matrixData);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, MatTableModule, MatCardModule, i3.MatCard, i3.MatCardContent, i3.MatCardHeader, i3.MatCardTitle, MatProgressSpinnerModule, i4.MatProgressSpinner, HttpClientModule],\n      styles: [\".pulse-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2.5rem;\\n  text-align: center;\\n  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\\n  margin: 0 0 30px 0;\\n}\\n\\n.matrix-card[_ngcontent-%COMP%] {\\n  margin: 20px auto;\\n  max-width: 95%;\\n  box-shadow: 0 8px 16px rgba(0,0,0,0.2);\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  max-width: 100%;\\n}\\n\\n.matrix-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-size: 12px;\\n}\\n\\n.matrix-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n.matrix-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  padding: 8px;\\n  text-align: center;\\n  vertical-align: middle;\\n}\\n\\n\\n\\n.test-name-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #3f51b5;\\n  color: white;\\n  font-weight: bold;\\n  font-size: 14px;\\n}\\n\\n.sub-test-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #5c6bc0;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 12px;\\n}\\n\\n.batch-header[_ngcontent-%COMP%] {\\n  background-color: #3f51b5 !important;\\n  color: white !important;\\n  font-weight: bold !important;\\n  writing-mode: vertical-rl;\\n  text-orientation: mixed;\\n  min-width: 80px;\\n}\\n\\n\\n\\n.data-row[_ngcontent-%COMP%]:nth-child(even) {\\n  background-color: #f5f5f5;\\n}\\n\\n.data-row[_ngcontent-%COMP%]:nth-child(odd) {\\n  background-color: white;\\n}\\n\\n.batch-cell[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  font-weight: 600;\\n  color: #3f51b5;\\n}\\n\\n.value-cell[_ngcontent-%COMP%] {\\n  font-family: 'Courier New', monospace;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.footer-row[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0;\\n  font-weight: 600;\\n}\\n\\n.footer-label[_ngcontent-%COMP%] {\\n  background-color: #9e9e9e;\\n  color: white;\\n  font-weight: bold;\\n  text-align: center;\\n}\\n\\n.unit-row[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n\\n.pp-row[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n}\\n\\n.cp-row[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n}\\n\\n.footer-cell[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  font-family: 'Courier New', monospace;\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.debug-info[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  padding: 10px;\\n  margin: 10px 0;\\n  border-radius: 5px;\\n  color: #333;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 0, 0, 0.1);\\n  color: white;\\n  padding: 20px;\\n  text-align: center;\\n  border-radius: 5px;\\n  margin: 20px 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .matrix-table[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n\\n  .matrix-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .matrix-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 4px;\\n  }\\n\\n  h1[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTableModule", "MatCardModule", "MatProgressSpinnerModule", "HttpClientModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "matrixData", "batchNumbers", "length", "testNames", "ɵɵtemplate", "PulsedesignComponent_div_3_p_5_Template", "PulsedesignComponent_div_3_p_6_Template", "loading", "ɵɵproperty", "ɵɵelementContainerStart", "testName_r2", "subTestName_r3", "PulsedesignComponent_mat_card_4_ng_container_13_th_1_Template", "subTestsByTest", "testName_r4", "ɵɵpipeBind2", "getMinimumValue", "batchNo_r7", "testName_r6", "subTestName_r5", "PulsedesignComponent_mat_card_4_tr_15_ng_container_3_td_1_Template", "PulsedesignComponent_mat_card_4_tr_15_ng_container_3_Template", "ɵɵtextInterpolate", "getUnit", "testName_r9", "subTestName_r8", "PulsedesignComponent_mat_card_4_ng_container_20_td_1_Template", "getPpPpKValue", "testName_r11", "subTestName_r10", "PulsedesignComponent_mat_card_4_ng_container_24_td_1_Template", "getCpCpKValue", "testName_r13", "subTestName_r12", "PulsedesignComponent_mat_card_4_ng_container_28_td_1_Template", "PulsedesignComponent_mat_card_4_ng_container_11_Template", "PulsedesignComponent_mat_card_4_ng_container_13_Template", "PulsedesignComponent_mat_card_4_tr_15_Template", "PulsedesignComponent_mat_card_4_ng_container_20_Template", "PulsedesignComponent_mat_card_4_ng_container_24_Template", "PulsedesignComponent_mat_card_4_ng_container_28_Template", "ɵɵelement", "PulsedesignComponent", "constructor", "dataService", "displayedColumns", "ngOnInit", "console", "log", "getMatrixData", "subscribe", "next", "data", "setupDisplayedColumns", "error", "for<PERSON>ach", "testName", "subTestName", "push", "batchNo", "dataMatrix", "Minimum", "ppPpKData", "ppPpKByTestAndSubTest", "Pp", "PpK", "toFixed", "cpCpKData", "cpCpKByTestAndSubTest", "Cp", "CpK", "unit", "unitsByTestAndSubTest", "UOM", "ɵɵdirectiveInject", "i1", "DataService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PulsedesignComponent_Template", "rf", "ctx", "PulsedesignComponent_div_3_Template", "PulsedesignComponent_mat_card_4_Template", "PulsedesignComponent_div_5_Template", "PulsedesignComponent_div_6_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "MatCard", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardTitle", "i4", "MatProgressSpinner", "styles"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.ts", "C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\pulsedesign\\pulsedesign.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { HttpClientModule } from '@angular/common/http';\nimport { DataService, MatrixData, TestData } from '../services/data.service';\n\n@Component({\n  selector: 'app-pulsedesign',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTableModule,\n    MatCardModule,\n    MatProgressSpinnerModule,\n    HttpClientModule\n  ],\n  templateUrl: './pulsedesign.component.html',\n  styleUrls: ['./pulsedesign.component.css']\n})\nexport class PulsedesignComponent implements OnInit {\n  matrixData: MatrixData | null = null;\n  loading = true;\n  displayedColumns: string[] = [];\n\n  constructor(private dataService: DataService) {}\n\n  ngOnInit() {\n    console.log('Component initialized, loading data...');\n    this.dataService.getMatrixData().subscribe({\n      next: (data) => {\n        console.log('Data received:', data);\n        this.matrixData = data;\n        this.setupDisplayedColumns();\n        this.loading = false;\n        console.log('Loading complete, matrixData:', this.matrixData);\n      },\n      error: (error) => {\n        console.error('Error loading data:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private setupDisplayedColumns() {\n    if (!this.matrixData) return;\n\n    this.displayedColumns = ['batchNo'];\n    this.matrixData.testNames.forEach(testName => {\n      this.matrixData!.subTestsByTest[testName].forEach(subTestName => {\n        this.displayedColumns.push(`${testName}_${subTestName}`);\n      });\n    });\n  }\n\n  getMinimumValue(batchNo: string, testName: string, subTestName: string): number | null {\n    if (!this.matrixData) return null;\n    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n    return data ? data.Minimum : null;\n  }\n\n  getPpPpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n\n    const ppPpKData = this.matrixData.ppPpKByTestAndSubTest[testName]?.[subTestName];\n    if (ppPpKData && ppPpKData.Pp !== null && ppPpKData.PpK !== null) {\n      return `${ppPpKData.Pp.toFixed(3)} | ${ppPpKData.PpK.toFixed(3)}`;\n    }\n\n    return '';\n  }\n\n  getCpCpKValue(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n\n    const cpCpKData = this.matrixData.cpCpKByTestAndSubTest[testName]?.[subTestName];\n    if (cpCpKData && cpCpKData.Cp !== null && cpCpKData.CpK !== null) {\n      return `${cpCpKData.Cp.toFixed(3)} | ${cpCpKData.CpK.toFixed(3)}`;\n    }\n\n    return '';\n  }\n\n  getUnit(testName: string, subTestName: string): string {\n    if (!this.matrixData) return '';\n    // First try the pre-computed units\n    const unit = this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName];\n    if (unit) return unit;\n\n    // Fallback: find any batch that has data for this testName and subTestName combination\n    for (const batchNo of this.matrixData.batchNumbers) {\n      const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];\n      if (data) {\n        return data.UOM;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"pulse-container\">\n  <h1>Welcome to pulse latest design</h1>\n\n  <!-- Debug Information -->\n  <div *ngIf=\"!loading\" class=\"debug-info\">\n    <p>Loading: {{ loading }}</p>\n    <p>Matrix Data Available: {{ !!matrixData }}</p>\n    <p *ngIf=\"matrixData\">Batch Numbers: {{ matrixData.batchNumbers?.length }}</p>\n    <p *ngIf=\"matrixData\">Test Names: {{ matrixData.testNames?.length }}</p>\n  </div>\n\n  <mat-card class=\"matrix-card\" *ngIf=\"!loading && matrixData\">\n    <mat-card-header>\n      <mat-card-title>Test Data Matrix</mat-card-title>\n    </mat-card-header>\n    <mat-card-content>\n      <div class=\"table-container\">\n        <!-- Simple table structure first -->\n        <table class=\"matrix-table\">\n          <!-- Header Row with TestName and SubTestName -->\n          <thead>\n            <!-- Main TestName Header Row -->\n            <tr class=\"test-name-header\">\n              <th class=\"batch-header\" rowspan=\"2\">BatchNo</th>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <th class=\"test-name-cell\" [attr.colspan]=\"matrixData.subTestsByTest[testName].length\">\n                  {{ testName }}\n                </th>\n              </ng-container>\n            </tr>\n            <!-- SubTestName Header Row -->\n            <tr class=\"sub-test-header\">\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <th class=\"sub-test-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ subTestName }}\n                </th>\n              </ng-container>\n            </tr>\n          </thead>\n\n          <!-- Data Rows -->\n          <tbody>\n            <tr *ngFor=\"let batchNo of matrixData.batchNumbers\" class=\"data-row\">\n              <td class=\"batch-cell\">{{ batchNo }}</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"value-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getMinimumValue(batchNo, testName, subTestName) | number:'1.2-3' }}\n                </td>\n              </ng-container>\n            </tr>\n          </tbody>\n\n          <!-- Footer Rows -->\n          <tfoot>\n            <!-- Unit Row -->\n            <tr class=\"footer-row unit-row\">\n              <td class=\"footer-label\">Unit</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getUnit(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n\n            <!-- Pp | PpK Row -->\n            <tr class=\"footer-row pp-row\">\n              <td class=\"footer-label\">Pp | PpK</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getPpPpKValue(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n\n            <!-- Cp | CpK Row -->\n            <tr class=\"footer-row cp-row\">\n              <td class=\"footer-label\">Cp | CpK</td>\n              <ng-container *ngFor=\"let testName of matrixData.testNames\">\n                <td class=\"footer-cell\" *ngFor=\"let subTestName of matrixData.subTestsByTest[testName]\">\n                  {{ getCpCpKValue(testName, subTestName) }}\n                </td>\n              </ng-container>\n            </tr>\n          </tfoot>\n        </table>\n      </div>\n    </mat-card-content>\n  </mat-card>\n\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Loading data...</p>\n  </div>\n\n  <div *ngIf=\"!loading && !matrixData\" class=\"error-container\">\n    <p>No data available or error loading data.</p>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAI7E,SAASC,gBAAgB,QAAQ,sBAAsB;;;;;;;;ICDnDC,EAAA,CAAAC,cAAA,QAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAxDH,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,UAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,UAAA,CAAAC,YAAA,CAAAC,MAAA,KAAoD;;;;;IAC1ET,EAAA,CAAAC,cAAA,QAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAlDH,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAK,kBAAA,iBAAAC,MAAA,CAAAC,UAAA,CAAAG,SAAA,kBAAAJ,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAAD,MAAA,KAA8C;;;;;IAHpET,EADF,CAAAC,cAAA,aAAyC,QACpC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EADA,CAAAW,UAAA,IAAAC,uCAAA,eAAsB,IAAAC,uCAAA,eACA;IACxBb,EAAA,CAAAG,YAAA,EAAM;;;;IAJDH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,cAAAC,MAAA,CAAAQ,OAAA,KAAsB;IACtBd,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,kBAAA,8BAAAC,MAAA,CAAAC,UAAA,KAAyC;IACxCP,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAC,UAAA,CAAgB;IAChBP,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAC,UAAA,CAAgB;;;;;IAgBVP,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAC,cAAA,aAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAFsBH,EAAA,CAAAI,SAAA,EAA2D;;IACpFJ,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,WAAA,MACF;;;;;IAMAjB,EAAA,CAAAC,cAAA,aAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAa,cAAA,MACF;;;;;IAHFlB,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAQ,6DAAA,iBAA0F;;;;;;IAAxCnB,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAC,WAAA,EAAsC;;;;;IAYxFrB,EAAA,CAAAC,cAAA,aAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAsB,WAAA,OAAAhB,MAAA,CAAAiB,eAAA,CAAAC,UAAA,EAAAC,WAAA,EAAAC,cAAA,iBACF;;;;;IAHF1B,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAgB,kEAAA,iBAAuF;;;;;;IAAxC3B,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAK,WAAA,EAAsC;;;;;IAFvFzB,EADF,CAAAC,cAAA,aAAqE,aAC5C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAW,UAAA,IAAAiB,6DAAA,2BAA4D;IAK9D5B,EAAA,CAAAG,YAAA,EAAK;;;;;IANoBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAA6B,iBAAA,CAAAL,UAAA,CAAa;IACDxB,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;;;;;IAcxDV,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAwB,OAAA,CAAAC,WAAA,EAAAC,cAAA,OACF;;;;;IAHFhC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAAsB,6DAAA,iBAAwF;;;;;;IAAxCjC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAW,WAAA,EAAsC;;;;;IAUtF/B,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4B,aAAA,CAAAC,YAAA,EAAAC,eAAA,OACF;;;;;IAHFpC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAA0B,6DAAA,iBAAwF;;;;;;IAAxCrC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAe,YAAA,EAAsC;;;;;IAUtFnC,EAAA,CAAAC,cAAA,aAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IADHH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAgC,aAAA,CAAAC,YAAA,EAAAC,eAAA,OACF;;;;;IAHFxC,EAAA,CAAAgB,uBAAA,GAA4D;IAC1DhB,EAAA,CAAAW,UAAA,IAAA8B,6DAAA,iBAAwF;;;;;;IAAxCzC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAa,cAAA,CAAAmB,YAAA,EAAsC;;;;;IAjEhGvC,EAFJ,CAAAC,cAAA,kBAA6D,sBAC1C,qBACC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAClCF,EADkC,CAAAG,YAAA,EAAiB,EACjC;IASRH,EARV,CAAAC,cAAA,uBAAkB,aACa,eAEC,YAEnB,aAEwB,aACU;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAW,UAAA,KAAA+B,wDAAA,2BAA4D;IAK9D1C,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAW,UAAA,KAAAgC,wDAAA,2BAA4D;IAMhE3C,EADE,CAAAG,YAAA,EAAK,EACC;IAGRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAW,UAAA,KAAAiC,8CAAA,iBAAqE;IAQvE5C,EAAA,CAAAG,YAAA,EAAQ;IAMJH,EAHJ,CAAAC,cAAA,aAAO,cAE2B,cACL;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAW,UAAA,KAAAkC,wDAAA,2BAA4D;IAK9D7C,EAAA,CAAAG,YAAA,EAAK;IAIHH,EADF,CAAAC,cAAA,cAA8B,cACH;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAW,UAAA,KAAAmC,wDAAA,2BAA4D;IAK9D9C,EAAA,CAAAG,YAAA,EAAK;IAIHH,EADF,CAAAC,cAAA,cAA8B,cACH;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAW,UAAA,KAAAoC,wDAAA,2BAA4D;IAUxE/C,EALU,CAAAG,YAAA,EAAK,EACC,EACF,EACJ,EACW,EACV;;;;IA/DoCH,EAAA,CAAAI,SAAA,IAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAQvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUpCV,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAC,YAAA,CAA0B;IAebR,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;IAUvBV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAC,UAAA,CAAAG,SAAA,CAAuB;;;;;IAYtEV,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAgD,SAAA,kBAA2B;IAC3BhD,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACpBF,EADoB,CAAAG,YAAA,EAAI,EAClB;;;;;IAGJH,EADF,CAAAC,cAAA,cAA6D,QACxD;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC7CF,EAD6C,CAAAG,YAAA,EAAI,EAC3C;;;ADxER,OAAM,MAAO8C,oBAAoB;EAK/BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAJ/B,KAAA5C,UAAU,GAAsB,IAAI;IACpC,KAAAO,OAAO,GAAG,IAAI;IACd,KAAAsC,gBAAgB,GAAa,EAAE;EAEgB;EAE/CC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,IAAI,CAACJ,WAAW,CAACK,aAAa,EAAE,CAACC,SAAS,CAAC;MACzCC,IAAI,EAAGC,IAAI,IAAI;QACbL,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEI,IAAI,CAAC;QACnC,IAAI,CAACpD,UAAU,GAAGoD,IAAI;QACtB,IAAI,CAACC,qBAAqB,EAAE;QAC5B,IAAI,CAAC9C,OAAO,GAAG,KAAK;QACpBwC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAChD,UAAU,CAAC;MAC/D,CAAC;MACDsD,KAAK,EAAGA,KAAK,IAAI;QACfP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC/C,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQ8C,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE;IAEtB,IAAI,CAAC6C,gBAAgB,GAAG,CAAC,SAAS,CAAC;IACnC,IAAI,CAAC7C,UAAU,CAACG,SAAS,CAACoD,OAAO,CAACC,QAAQ,IAAG;MAC3C,IAAI,CAACxD,UAAW,CAACa,cAAc,CAAC2C,QAAQ,CAAC,CAACD,OAAO,CAACE,WAAW,IAAG;QAC9D,IAAI,CAACZ,gBAAgB,CAACa,IAAI,CAAC,GAAGF,QAAQ,IAAIC,WAAW,EAAE,CAAC;MAC1D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAzC,eAAeA,CAAC2C,OAAe,EAAEH,QAAgB,EAAEC,WAAmB;IACpE,IAAI,CAAC,IAAI,CAACzD,UAAU,EAAE,OAAO,IAAI;IACjC,MAAMoD,IAAI,GAAG,IAAI,CAACpD,UAAU,CAAC4D,UAAU,CAACD,OAAO,CAAC,GAAGH,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAC3E,OAAOL,IAAI,GAAGA,IAAI,CAACS,OAAO,GAAG,IAAI;EACnC;EAEAlC,aAAaA,CAAC6B,QAAgB,EAAEC,WAAmB;IACjD,IAAI,CAAC,IAAI,CAACzD,UAAU,EAAE,OAAO,EAAE;IAE/B,MAAM8D,SAAS,GAAG,IAAI,CAAC9D,UAAU,CAAC+D,qBAAqB,CAACP,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAChF,IAAIK,SAAS,IAAIA,SAAS,CAACE,EAAE,KAAK,IAAI,IAAIF,SAAS,CAACG,GAAG,KAAK,IAAI,EAAE;MAChE,OAAO,GAAGH,SAAS,CAACE,EAAE,CAACE,OAAO,CAAC,CAAC,CAAC,MAAMJ,SAAS,CAACG,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;IACnE;IAEA,OAAO,EAAE;EACX;EAEAnC,aAAaA,CAACyB,QAAgB,EAAEC,WAAmB;IACjD,IAAI,CAAC,IAAI,CAACzD,UAAU,EAAE,OAAO,EAAE;IAE/B,MAAMmE,SAAS,GAAG,IAAI,CAACnE,UAAU,CAACoE,qBAAqB,CAACZ,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAChF,IAAIU,SAAS,IAAIA,SAAS,CAACE,EAAE,KAAK,IAAI,IAAIF,SAAS,CAACG,GAAG,KAAK,IAAI,EAAE;MAChE,OAAO,GAAGH,SAAS,CAACE,EAAE,CAACH,OAAO,CAAC,CAAC,CAAC,MAAMC,SAAS,CAACG,GAAG,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAE;IACnE;IAEA,OAAO,EAAE;EACX;EAEA3C,OAAOA,CAACiC,QAAgB,EAAEC,WAAmB;IAC3C,IAAI,CAAC,IAAI,CAACzD,UAAU,EAAE,OAAO,EAAE;IAC/B;IACA,MAAMuE,IAAI,GAAG,IAAI,CAACvE,UAAU,CAACwE,qBAAqB,CAAChB,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAC3E,IAAIc,IAAI,EAAE,OAAOA,IAAI;IAErB;IACA,KAAK,MAAMZ,OAAO,IAAI,IAAI,CAAC3D,UAAU,CAACC,YAAY,EAAE;MAClD,MAAMmD,IAAI,GAAG,IAAI,CAACpD,UAAU,CAAC4D,UAAU,CAACD,OAAO,CAAC,GAAGH,QAAQ,CAAC,GAAGC,WAAW,CAAC;MAC3E,IAAIL,IAAI,EAAE;QACR,OAAOA,IAAI,CAACqB,GAAG;MACjB;IACF;IACA,OAAO,EAAE;EACX;;;uCA7EW/B,oBAAoB,EAAAjD,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBlC,oBAAoB;MAAAmC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtF,EAAA,CAAAuF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB/B7F,EADF,CAAAC,cAAA,aAA6B,SACvB;UAAAD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UA6FvCH,EA1FA,CAAAW,UAAA,IAAAoF,mCAAA,iBAAyC,IAAAC,wCAAA,uBAOoB,IAAAC,mCAAA,iBA8Ed,IAAAC,mCAAA,iBAKc;UAG/DlG,EAAA,CAAAG,YAAA,EAAM;;;UA7FEH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAe,UAAA,UAAA+E,GAAA,CAAAhF,OAAA,CAAc;UAOWd,EAAA,CAAAI,SAAA,EAA4B;UAA5BJ,EAAA,CAAAe,UAAA,UAAA+E,GAAA,CAAAhF,OAAA,IAAAgF,GAAA,CAAAvF,UAAA,CAA4B;UA8ErDP,EAAA,CAAAI,SAAA,EAAa;UAAbJ,EAAA,CAAAe,UAAA,SAAA+E,GAAA,CAAAhF,OAAA,CAAa;UAKbd,EAAA,CAAAI,SAAA,EAA6B;UAA7BJ,EAAA,CAAAe,UAAA,UAAA+E,GAAA,CAAAhF,OAAA,KAAAgF,GAAA,CAAAvF,UAAA,CAA6B;;;qBD/EjCZ,YAAY,EAAAwG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EACZ1G,cAAc,EACdC,aAAa,EAAA0G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,YAAA,EACb7G,wBAAwB,EAAA8G,EAAA,CAAAC,kBAAA,EACxB9G,gBAAgB;MAAA+G,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}