import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { trigger, style, transition, animate } from '@angular/animations';
import { DataService, MatrixData } from '../services/data.service';

@Component({
  selector: 'app-pulsedesign',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule
  ],
  templateUrl: './pulsedesign.component.html',
  styleUrls: ['./pulsedesign.component.css'],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-in', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('150ms ease-out', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ])
  ]
})
export class PulsedesignComponent implements OnInit {
  matrixData: MatrixData | null = null;
  loading = true;
  displayedColumns: string[] = [];
  activeDropdown: string | null = null;

  constructor(private dataService: DataService) {}

  ngOnInit() {
    console.log('Component initialized, loading data...');
    this.dataService.getMatrixData().subscribe({
      next: (data) => {
        console.log('Data received:', data);
        this.matrixData = data;
        this.setupDisplayedColumns();
        this.loading = false;
        console.log('Loading complete, matrixData:', this.matrixData);
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.loading = false;
      }
    });
  }

  private setupDisplayedColumns() {
    if (!this.matrixData) return;

    this.displayedColumns = ['batchNo'];
    this.matrixData.testNames.forEach(testName => {
      this.matrixData!.subTestsByTest[testName].forEach(subTestName => {
        this.displayedColumns.push(`${testName}_${subTestName}`);
      });
    });
  }

  getMinimumValue(batchNo: string, testName: string, subTestName: string): number | null {
    if (!this.matrixData) return null;
    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];
    return data ? data.Minimum : null;
  }

  getPpPpKValue(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';

    const ppPpKData = this.matrixData.ppPpKByTestAndSubTest[testName]?.[subTestName];
    if (ppPpKData && ppPpKData.Pp !== null && ppPpKData.PpK !== null) {
      return `${ppPpKData.Pp.toFixed(3)} | ${ppPpKData.PpK.toFixed(3)}`;
    }

    return '';
  }

  getCpCpKValue(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';

    const cpCpKData = this.matrixData.cpCpKByTestAndSubTest[testName]?.[subTestName];
    if (cpCpKData && cpCpKData.Cp !== null && cpCpKData.CpK !== null) {
      return `${cpCpKData.Cp.toFixed(3)} | ${cpCpKData.CpK.toFixed(3)}`;
    }

    return '';
  }

  getUnit(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';
    // First try the pre-computed units
    const unit = this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName];
    if (unit) return unit;

    // Fallback: find any batch that has data for this testName and subTestName combination
    for (const batchNo of this.matrixData.batchNumbers) {
      const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];
      if (data) {
        return data.UOM;
      }
    }
    return '';
  }

  // Action methods
  onActionClick(action: string, testName: string, subTestName: string): void {
    const unit = this.getUnit(testName, subTestName);
    const ppPpK = this.getPpPpKValue(testName, subTestName);
    const cpCpK = this.getCpCpKValue(testName, subTestName);

    const message = `Action: ${action}
TestName: ${testName}
SubTestName: ${subTestName}
Unit: ${unit}
Pp | PpK: ${ppPpK || 'N/A'}
Cp | CpK: ${cpCpK || 'N/A'}`;

    alert(message);
  }

  onPCAClick(testName: string, subTestName: string): void {
    this.onActionClick('PCA', testName, subTestName);
  }

  onNelsonClick(testName: string, subTestName: string): void {
    this.onActionClick('Nelson', testName, subTestName);
  }

  onSQCClick(testName: string, subTestName: string): void {
    this.onActionClick('SQC', testName, subTestName);
  }

  onDownloadClick(testName: string, subTestName: string): void {
    this.onActionClick('Download', testName, subTestName);
  }

  // Dropdown management methods
  showDropdown(testName: string, subTestName: string): void {
    this.activeDropdown = testName + '_' + subTestName;
  }

  hideDropdown(): void {
    // Add a small delay to allow clicking on dropdown items
    setTimeout(() => {
      this.activeDropdown = null;
    }, 150);
  }

  hideDropdownImmediate(): void {
    this.activeDropdown = null;
  }
}
