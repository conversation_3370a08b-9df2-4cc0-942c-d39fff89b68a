import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { HttpClientModule } from '@angular/common/http';
import { DataService, MatrixData, TestData } from '../services/data.service';

@Component({
  selector: 'app-pulsedesign',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatCardModule,
    MatProgressSpinnerModule,
    HttpClientModule
  ],
  templateUrl: './pulsedesign.component.html',
  styleUrls: ['./pulsedesign.component.css']
})
export class PulsedesignComponent implements OnInit {
  matrixData: MatrixData | null = null;
  loading = true;
  displayedColumns: string[] = [];

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.dataService.getMatrixData().subscribe({
      next: (data) => {
        this.matrixData = data;
        this.setupDisplayedColumns();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading data:', error);
        this.loading = false;
      }
    });
  }

  private setupDisplayedColumns() {
    if (!this.matrixData) return;

    this.displayedColumns = ['batchNo'];
    this.matrixData.testNames.forEach(testName => {
      this.matrixData!.subTestsByTest[testName].forEach(subTestName => {
        this.displayedColumns.push(`${testName}_${subTestName}`);
      });
    });
  }

  getMinimumValue(batchNo: string, testName: string, subTestName: string): number | null {
    if (!this.matrixData) return null;
    const data = this.matrixData.dataMatrix[batchNo]?.[testName]?.[subTestName];
    return data ? data.Minimum : null;
  }

  getPpPpKValue(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';
    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];
    return data ? `${data.Pp.toFixed(3)} | ${data.PpK.toFixed(3)}` : '';
  }

  getCpCpKValue(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';
    const data = Object.values(this.matrixData.dataMatrix)[0]?.[testName]?.[subTestName];
    return data ? `${data.Cp.toFixed(3)} | ${data.CpK.toFixed(3)}` : '';
  }

  getUnit(testName: string, subTestName: string): string {
    if (!this.matrixData) return '';
    return this.matrixData.unitsByTestAndSubTest[testName]?.[subTestName] || '';
  }
}
