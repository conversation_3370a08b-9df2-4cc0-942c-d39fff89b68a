@use 'sass:map';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, app);

/// Generates custom tokens for the app.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the app
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: (
    background-color: map.get($systems, md-sys-color, background),
    text-color: map.get($systems, md-sys-color, on-background),
  );

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
