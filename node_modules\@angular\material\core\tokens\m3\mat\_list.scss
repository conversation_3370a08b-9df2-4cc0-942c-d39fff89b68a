@use 'sass:map';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, list);

/// Generates custom tokens for the mat-list.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-list
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: (
    active-indicator-color: map.get($systems, md-sys-color, secondary-container),
    active-indicator-shape: map.get($systems, md-sys-shape, corner-full),
  );

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
