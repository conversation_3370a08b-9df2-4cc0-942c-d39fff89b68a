/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { escapeIdentifier } from '../output/abstract_emitter';
import * as o from '../output/output_ast';
import { Identifiers } from './r3_identifiers';
export function typeWithParameters(type, numParams) {
    if (numParams === 0) {
        return o.expressionType(type);
    }
    const params = [];
    for (let i = 0; i < numParams; i++) {
        params.push(o.DYNAMIC_TYPE);
    }
    return o.expressionType(type, undefined, params);
}
const ANIMATE_SYMBOL_PREFIX = '@';
export function prepareSyntheticPropertyName(name) {
    return `${ANIMATE_SYMBOL_PREFIX}${name}`;
}
export function prepareSyntheticListenerName(name, phase) {
    return `${ANIMATE_SYMBOL_PREFIX}${name}.${phase}`;
}
export function getSafePropertyAccessString(accessor, name) {
    const escapedName = escapeIdentifier(name, false, false);
    return escapedName !== name ? `${accessor}[${escapedName}]` : `${accessor}.${name}`;
}
export function prepareSyntheticListenerFunctionName(name, phase) {
    return `animation_${name}_${phase}`;
}
export function jitOnlyGuardedExpression(expr) {
    return guardedExpression('ngJitMode', expr);
}
export function devOnlyGuardedExpression(expr) {
    return guardedExpression('ngDevMode', expr);
}
export function guardedExpression(guard, expr) {
    const guardExpr = new o.ExternalExpr({ name: guard, moduleName: null });
    const guardNotDefined = new o.BinaryOperatorExpr(o.BinaryOperator.Identical, new o.TypeofExpr(guardExpr), o.literal('undefined'));
    const guardUndefinedOrTrue = new o.BinaryOperatorExpr(o.BinaryOperator.Or, guardNotDefined, guardExpr, 
    /* type */ undefined, 
    /* sourceSpan */ undefined, true);
    return new o.BinaryOperatorExpr(o.BinaryOperator.And, guardUndefinedOrTrue, expr);
}
export function wrapReference(value) {
    const wrapped = new o.WrappedNodeExpr(value);
    return { value: wrapped, type: wrapped };
}
export function refsToArray(refs, shouldForwardDeclare) {
    const values = o.literalArr(refs.map((ref) => ref.value));
    return shouldForwardDeclare ? o.arrowFn([], values) : values;
}
export function createMayBeForwardRefExpression(expression, forwardRef) {
    return { expression, forwardRef };
}
/**
 * Convert a `MaybeForwardRefExpression` to an `Expression`, possibly wrapping its expression in a
 * `forwardRef()` call.
 *
 * If `MaybeForwardRefExpression.forwardRef` is `ForwardRefHandling.Unwrapped` then the expression
 * was originally wrapped in a `forwardRef()` call to prevent the value from being eagerly evaluated
 * in the code.
 *
 * See `packages/compiler-cli/src/ngtsc/annotations/src/injectable.ts` and
 * `packages/compiler/src/jit_compiler_facade.ts` for more information.
 */
export function convertFromMaybeForwardRefExpression({ expression, forwardRef, }) {
    switch (forwardRef) {
        case 0 /* ForwardRefHandling.None */:
        case 1 /* ForwardRefHandling.Wrapped */:
            return expression;
        case 2 /* ForwardRefHandling.Unwrapped */:
            return generateForwardRef(expression);
    }
}
/**
 * Generate an expression that has the given `expr` wrapped in the following form:
 *
 * ```
 * forwardRef(() => expr)
 * ```
 */
export function generateForwardRef(expr) {
    return o.importExpr(Identifiers.forwardRef).callFn([o.arrowFn([], expr)]);
}
//# sourceMappingURL=data:application/json;base64,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