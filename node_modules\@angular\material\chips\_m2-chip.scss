@use 'sass:map';
@use 'sass:meta';
@use 'sass:color';
@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/m2/palette' as m2-palette;
@use '../core/theming/theming';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    chip-container-shape-radius: 16px,
    chip-disabled-container-opacity: 0.4,
    chip-disabled-outline-color: transparent,
    chip-flat-selected-outline-width: 0,
    chip-focus-outline-color: transparent,
    chip-hover-state-layer-opacity: 0.04,
    chip-outline-color: transparent,
    chip-outline-width: 0,
    chip-selected-hover-state-layer-opacity: 0.04,
    chip-selected-trailing-action-state-layer-color: transparent,
    chip-trailing-action-focus-opacity: 1,
    chip-trailing-action-focus-state-layer-opacity: 0,
    chip-trailing-action-hover-state-layer-opacity: 0,
    chip-trailing-action-opacity: 0.54,
    chip-trailing-action-state-layer-color: transparent,
    chip-with-avatar-avatar-shape-radius: 14px,
    chip-with-avatar-avatar-size: 28px,
    chip-with-avatar-disabled-avatar-opacity: 1,
    chip-with-icon-disabled-icon-opacity: 1,
    chip-with-icon-icon-size: 18px,
    chip-with-trailing-icon-disabled-trailing-icon-opacity: 1,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: null) {
  $foreground: null;
  $background: null;
  $state-layer-color: inspection.get-theme-color($theme, foreground, base);
  $state-layer-opacity: 0.12; // 0.12 is a common value in Material Design for opacity.
  $surface: inspection.get-theme-color($theme, background, card);

  @if $palette-name == null {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $grey-50: map.get(m2-palette.$grey-palette, 50);
    $grey-900: map.get(m2-palette.$grey-palette, 900);
    $foreground: if($is-dark, $grey-50, $grey-900);

    $surface: inspection.get-theme-color($theme, background, card);
    $background: if(
        meta.type-of($state-layer-color) == color and meta.type-of($surface) == color,
        color.mix($state-layer-color, $surface, 12%),
        $state-layer-color
    );
  }
  @else {
    $background: inspection.get-theme-color($theme, $palette-name);
    $foreground: inspection.get-theme-color($theme, $palette-name, default-contrast);
  }
  @return (
    chip-disabled-label-text-color: $foreground,
    chip-elevated-container-color: $background,
    chip-elevated-disabled-container-color: $background,
    chip-elevated-selected-container-color: $background,
    chip-flat-disabled-selected-container-color: $background,
    chip-focus-state-layer-color: $state-layer-color,
    chip-focus-state-layer-opacity: $state-layer-opacity,
    chip-hover-state-layer-color: $state-layer-color,
    chip-label-text-color: $foreground,
    chip-selected-disabled-trailing-icon-color: $foreground,
    chip-selected-focus-state-layer-color: $state-layer-color,
    chip-selected-focus-state-layer-opacity: $state-layer-opacity,
    chip-selected-hover-state-layer-color: $state-layer-color,
    chip-selected-label-text-color: $foreground,
    chip-selected-trailing-icon-color: $foreground,
    chip-with-icon-disabled-icon-color: $foreground,
    chip-with-icon-icon-color: $foreground,
    chip-with-icon-selected-icon-color: $foreground,
    chip-with-trailing-icon-disabled-trailing-icon-color: $foreground,
    chip-with-trailing-icon-trailing-icon-color: $foreground,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    chip-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    chip-label-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    chip-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    chip-label-text-tracking: inspection.get-theme-typography($theme, body-2, letter-spacing),
    chip-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -2);
  @return (
    // The height of the chip.
    chip-container-height: map.get(
        (
          0: 32px,
          -1: 28px,
          -2: 24px,
        ),
        $scale
    )
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
