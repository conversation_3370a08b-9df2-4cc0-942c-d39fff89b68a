/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { utf8Encode } from '../util';
// https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit
const VERSION = 3;
const JS_B64_PREFIX = '# sourceMappingURL=data:application/json;base64,';
export class SourceMapGenerator {
    constructor(file = null) {
        this.file = file;
        this.sourcesContent = new Map();
        this.lines = [];
        this.lastCol0 = 0;
        this.hasMappings = false;
    }
    // The content is `null` when the content is expected to be loaded using the URL
    addSource(url, content = null) {
        if (!this.sourcesContent.has(url)) {
            this.sourcesContent.set(url, content);
        }
        return this;
    }
    addLine() {
        this.lines.push([]);
        this.lastCol0 = 0;
        return this;
    }
    addMapping(col0, sourceUrl, sourceLine0, sourceCol0) {
        if (!this.currentLine) {
            throw new Error(`A line must be added before mappings can be added`);
        }
        if (sourceUrl != null && !this.sourcesContent.has(sourceUrl)) {
            throw new Error(`Unknown source file "${sourceUrl}"`);
        }
        if (col0 == null) {
            throw new Error(`The column in the generated code must be provided`);
        }
        if (col0 < this.lastCol0) {
            throw new Error(`Mapping should be added in output order`);
        }
        if (sourceUrl && (sourceLine0 == null || sourceCol0 == null)) {
            throw new Error(`The source location must be provided when a source url is provided`);
        }
        this.hasMappings = true;
        this.lastCol0 = col0;
        this.currentLine.push({ col0, sourceUrl, sourceLine0, sourceCol0 });
        return this;
    }
    /**
     * @internal strip this from published d.ts files due to
     * https://github.com/microsoft/TypeScript/issues/36216
     */
    get currentLine() {
        return this.lines.slice(-1)[0];
    }
    toJSON() {
        if (!this.hasMappings) {
            return null;
        }
        const sourcesIndex = new Map();
        const sources = [];
        const sourcesContent = [];
        Array.from(this.sourcesContent.keys()).forEach((url, i) => {
            sourcesIndex.set(url, i);
            sources.push(url);
            sourcesContent.push(this.sourcesContent.get(url) || null);
        });
        let mappings = '';
        let lastCol0 = 0;
        let lastSourceIndex = 0;
        let lastSourceLine0 = 0;
        let lastSourceCol0 = 0;
        this.lines.forEach((segments) => {
            lastCol0 = 0;
            mappings += segments
                .map((segment) => {
                // zero-based starting column of the line in the generated code
                let segAsStr = toBase64VLQ(segment.col0 - lastCol0);
                lastCol0 = segment.col0;
                if (segment.sourceUrl != null) {
                    // zero-based index into the “sources” list
                    segAsStr += toBase64VLQ(sourcesIndex.get(segment.sourceUrl) - lastSourceIndex);
                    lastSourceIndex = sourcesIndex.get(segment.sourceUrl);
                    // the zero-based starting line in the original source
                    segAsStr += toBase64VLQ(segment.sourceLine0 - lastSourceLine0);
                    lastSourceLine0 = segment.sourceLine0;
                    // the zero-based starting column in the original source
                    segAsStr += toBase64VLQ(segment.sourceCol0 - lastSourceCol0);
                    lastSourceCol0 = segment.sourceCol0;
                }
                return segAsStr;
            })
                .join(',');
            mappings += ';';
        });
        mappings = mappings.slice(0, -1);
        return {
            'file': this.file || '',
            'version': VERSION,
            'sourceRoot': '',
            'sources': sources,
            'sourcesContent': sourcesContent,
            'mappings': mappings,
        };
    }
    toJsComment() {
        return this.hasMappings
            ? '//' + JS_B64_PREFIX + toBase64String(JSON.stringify(this, null, 0))
            : '';
    }
}
export function toBase64String(value) {
    let b64 = '';
    const encoded = utf8Encode(value);
    for (let i = 0; i < encoded.length;) {
        const i1 = encoded[i++];
        const i2 = i < encoded.length ? encoded[i++] : null;
        const i3 = i < encoded.length ? encoded[i++] : null;
        b64 += toBase64Digit(i1 >> 2);
        b64 += toBase64Digit(((i1 & 3) << 4) | (i2 === null ? 0 : i2 >> 4));
        b64 += i2 === null ? '=' : toBase64Digit(((i2 & 15) << 2) | (i3 === null ? 0 : i3 >> 6));
        b64 += i2 === null || i3 === null ? '=' : toBase64Digit(i3 & 63);
    }
    return b64;
}
function toBase64VLQ(value) {
    value = value < 0 ? (-value << 1) + 1 : value << 1;
    let out = '';
    do {
        let digit = value & 31;
        value = value >> 5;
        if (value > 0) {
            digit = digit | 32;
        }
        out += toBase64Digit(digit);
    } while (value > 0);
    return out;
}
const B64_DIGITS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
function toBase64Digit(value) {
    if (value < 0 || value >= 64) {
        throw new Error(`Can only encode value in the range [0, 63]`);
    }
    return B64_DIGITS[value];
}
//# sourceMappingURL=data:application/json;base64,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