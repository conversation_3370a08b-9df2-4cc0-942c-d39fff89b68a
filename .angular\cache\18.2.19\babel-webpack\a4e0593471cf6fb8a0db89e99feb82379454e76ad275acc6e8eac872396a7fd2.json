{"ast": null, "code": "import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('18.0.0');\nexport { VERSION };", "map": {"version": 3, "names": ["Version", "VERSION"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/@angular/cdk/fesm2022/cdk.mjs"], "sourcesContent": ["import { Version } from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nconst VERSION = new Version('18.0.0');\n\nexport { VERSION };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;;AAEvC;AACA,MAAMC,OAAO,GAAG,IAAID,OAAO,CAAC,QAAQ,CAAC;AAErC,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}