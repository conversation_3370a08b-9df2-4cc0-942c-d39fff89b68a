{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class DataService {\n  constructor(http) {\n    this.http = http;\n  }\n  getTestData() {\n    console.log('Fetching data from /assets/samplejson.json');\n    return this.http.get('/assets/samplejson.json');\n  }\n  getMatrixData() {\n    return this.getTestData().pipe(map(data => {\n      console.log('Raw data from JSON:', data);\n      const transformedData = this.transformToMatrix(data);\n      console.log('Transformed matrix data:', transformedData);\n      return transformedData;\n    }));\n  }\n  transformToMatrix(data) {\n    const batchNumbers = [...new Set(data.map(item => item.BatchNo))].sort();\n    const testNames = [...new Set(data.map(item => item.TestName))].sort();\n    const subTestsByTest = {};\n    const dataMatrix = {};\n    const unitsByTestAndSubTest = {};\n    const ppPpKByTestAndSubTest = {};\n    const cpCpKByTestAndSubTest = {};\n    // Initialize structures\n    testNames.forEach(testName => {\n      subTestsByTest[testName] = [...new Set(data.filter(item => item.TestName === testName).map(item => item.SubTestName))].sort();\n      unitsByTestAndSubTest[testName] = {};\n      ppPpKByTestAndSubTest[testName] = {};\n      cpCpKByTestAndSubTest[testName] = {};\n    });\n    batchNumbers.forEach(batchNo => {\n      dataMatrix[batchNo] = {};\n      testNames.forEach(testName => {\n        dataMatrix[batchNo][testName] = {};\n      });\n    });\n    // Populate data matrix, units, and statistical values\n    data.forEach(item => {\n      if (!dataMatrix[item.BatchNo][item.TestName][item.SubTestName]) {\n        dataMatrix[item.BatchNo][item.TestName][item.SubTestName] = item;\n        unitsByTestAndSubTest[item.TestName][item.SubTestName] = item.UOM;\n        // Store Pp/PpK values (use first valid occurrence for each TestName/SubTestName combination)\n        if (!ppPpKByTestAndSubTest[item.TestName][item.SubTestName]) {\n          ppPpKByTestAndSubTest[item.TestName][item.SubTestName] = {\n            Pp: item.Pp || null,\n            PpK: item.PpK || null\n          };\n        }\n        // Store Cp/CpK values (use first valid occurrence for each TestName/SubTestName combination)\n        if (!cpCpKByTestAndSubTest[item.TestName][item.SubTestName]) {\n          cpCpKByTestAndSubTest[item.TestName][item.SubTestName] = {\n            Cp: item.Cp || null,\n            CpK: item.CpK || null\n          };\n        }\n      }\n    });\n    return {\n      batchNumbers,\n      testNames,\n      subTestsByTest,\n      dataMatrix,\n      unitsByTestAndSubTest,\n      ppPpKByTestAndSubTest,\n      cpCpKByTestAndSubTest\n    };\n  }\n  static {\n    this.ɵfac = function DataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DataService,\n      factory: DataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "DataService", "constructor", "http", "getTestData", "console", "log", "get", "getMatrixData", "pipe", "data", "transformedData", "transformToMatrix", "batchNumbers", "Set", "item", "BatchNo", "sort", "testNames", "TestName", "subTestsByTest", "dataMatrix", "unitsByTestAndSubTest", "ppPpKByTestAndSubTest", "cpCpKByTestAndSubTest", "for<PERSON>ach", "testName", "filter", "SubTestName", "batchNo", "UOM", "Pp", "PpK", "Cp", "CpK", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\services\\data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\nexport interface TestData {\n  ID: number;\n  TestID: string;\n  BatchNo: string;\n  CreatedOn: string;\n  TestCode: string;\n  TestName: string;\n  Minimum: number;\n  LSL: number;\n  USL: number;\n  SD: string;\n  ED: string;\n  SubTestName: string;\n  PpL: number;\n  PpU: number;\n  CpL: number;\n  CpU: number;\n  Pp: number;\n  PpK: number;\n  Cp: number;\n  CpK: number;\n  UOM: string;\n  CPV_FLAG: string;\n}\n\nexport interface MatrixData {\n  batchNumbers: string[];\n  testNames: string[];\n  subTestsByTest: { [testName: string]: string[] };\n  dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } };\n  unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } };\n  ppPpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Pp: number | null, PpK: number | null } } };\n  cpCpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Cp: number | null, CpK: number | null } } };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DataService {\n\n  constructor(private http: HttpClient) { }\n\n  getTestData(): Observable<TestData[]> {\n    console.log('Fetching data from /assets/samplejson.json');\n    return this.http.get<TestData[]>('/assets/samplejson.json');\n  }\n\n  getMatrixData(): Observable<MatrixData> {\n    return this.getTestData().pipe(\n      map(data => {\n        console.log('Raw data from JSON:', data);\n        const transformedData = this.transformToMatrix(data);\n        console.log('Transformed matrix data:', transformedData);\n        return transformedData;\n      })\n    );\n  }\n\n  private transformToMatrix(data: TestData[]): MatrixData {\n    const batchNumbers = [...new Set(data.map(item => item.BatchNo))].sort();\n    const testNames = [...new Set(data.map(item => item.TestName))].sort();\n\n    const subTestsByTest: { [testName: string]: string[] } = {};\n    const dataMatrix: { [batchNo: string]: { [testName: string]: { [subTestName: string]: TestData } } } = {};\n    const unitsByTestAndSubTest: { [testName: string]: { [subTestName: string]: string } } = {};\n    const ppPpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Pp: number | null, PpK: number | null } } } = {};\n    const cpCpKByTestAndSubTest: { [testName: string]: { [subTestName: string]: { Cp: number | null, CpK: number | null } } } = {};\n\n    // Initialize structures\n    testNames.forEach(testName => {\n      subTestsByTest[testName] = [...new Set(data.filter(item => item.TestName === testName).map(item => item.SubTestName))].sort();\n      unitsByTestAndSubTest[testName] = {};\n      ppPpKByTestAndSubTest[testName] = {};\n      cpCpKByTestAndSubTest[testName] = {};\n    });\n\n    batchNumbers.forEach(batchNo => {\n      dataMatrix[batchNo] = {};\n      testNames.forEach(testName => {\n        dataMatrix[batchNo][testName] = {};\n      });\n    });\n\n    // Populate data matrix, units, and statistical values\n    data.forEach(item => {\n      if (!dataMatrix[item.BatchNo][item.TestName][item.SubTestName]) {\n        dataMatrix[item.BatchNo][item.TestName][item.SubTestName] = item;\n        unitsByTestAndSubTest[item.TestName][item.SubTestName] = item.UOM;\n\n        // Store Pp/PpK values (use first valid occurrence for each TestName/SubTestName combination)\n        if (!ppPpKByTestAndSubTest[item.TestName][item.SubTestName]) {\n          ppPpKByTestAndSubTest[item.TestName][item.SubTestName] = {\n            Pp: item.Pp || null,\n            PpK: item.PpK || null\n          };\n        }\n\n        // Store Cp/CpK values (use first valid occurrence for each TestName/SubTestName combination)\n        if (!cpCpKByTestAndSubTest[item.TestName][item.SubTestName]) {\n          cpCpKByTestAndSubTest[item.TestName][item.SubTestName] = {\n            Cp: item.Cp || null,\n            CpK: item.CpK || null\n          };\n        }\n      }\n    });\n\n    return {\n      batchNumbers,\n      testNames,\n      subTestsByTest,\n      dataMatrix,\n      unitsByTestAndSubTest,\n      ppPpKByTestAndSubTest,\n      cpCpKByTestAndSubTest\n    };\n  }\n}\n"], "mappings": "AAGA,SAASA,GAAG,QAAQ,gBAAgB;;;AAwCpC,OAAM,MAAOC,WAAW;EAEtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,WAAWA,CAAA;IACTC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzD,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAa,yBAAyB,CAAC;EAC7D;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACJ,WAAW,EAAE,CAACK,IAAI,CAC5BT,GAAG,CAACU,IAAI,IAAG;MACTL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,IAAI,CAAC;MACxC,MAAMC,eAAe,GAAG,IAAI,CAACC,iBAAiB,CAACF,IAAI,CAAC;MACpDL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,eAAe,CAAC;MACxD,OAAOA,eAAe;IACxB,CAAC,CAAC,CACH;EACH;EAEQC,iBAAiBA,CAACF,IAAgB;IACxC,MAAMG,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,IAAI,CAACV,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;IACxE,MAAMC,SAAS,GAAG,CAAC,GAAG,IAAIJ,GAAG,CAACJ,IAAI,CAACV,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,EAAE;IAEtE,MAAMG,cAAc,GAAqC,EAAE;IAC3D,MAAMC,UAAU,GAAuF,EAAE;IACzG,MAAMC,qBAAqB,GAA8D,EAAE;IAC3F,MAAMC,qBAAqB,GAAiG,EAAE;IAC9H,MAAMC,qBAAqB,GAAiG,EAAE;IAE9H;IACAN,SAAS,CAACO,OAAO,CAACC,QAAQ,IAAG;MAC3BN,cAAc,CAACM,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAIZ,GAAG,CAACJ,IAAI,CAACiB,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAKO,QAAQ,CAAC,CAAC1B,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACa,WAAW,CAAC,CAAC,CAAC,CAACX,IAAI,EAAE;MAC7HK,qBAAqB,CAACI,QAAQ,CAAC,GAAG,EAAE;MACpCH,qBAAqB,CAACG,QAAQ,CAAC,GAAG,EAAE;MACpCF,qBAAqB,CAACE,QAAQ,CAAC,GAAG,EAAE;IACtC,CAAC,CAAC;IAEFb,YAAY,CAACY,OAAO,CAACI,OAAO,IAAG;MAC7BR,UAAU,CAACQ,OAAO,CAAC,GAAG,EAAE;MACxBX,SAAS,CAACO,OAAO,CAACC,QAAQ,IAAG;QAC3BL,UAAU,CAACQ,OAAO,CAAC,CAACH,QAAQ,CAAC,GAAG,EAAE;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACAhB,IAAI,CAACe,OAAO,CAACV,IAAI,IAAG;MAClB,IAAI,CAACM,UAAU,CAACN,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,EAAE;QAC9DP,UAAU,CAACN,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,GAAGb,IAAI;QAChEO,qBAAqB,CAACP,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,GAAGb,IAAI,CAACe,GAAG;QAEjE;QACA,IAAI,CAACP,qBAAqB,CAACR,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,EAAE;UAC3DL,qBAAqB,CAACR,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,GAAG;YACvDG,EAAE,EAAEhB,IAAI,CAACgB,EAAE,IAAI,IAAI;YACnBC,GAAG,EAAEjB,IAAI,CAACiB,GAAG,IAAI;WAClB;QACH;QAEA;QACA,IAAI,CAACR,qBAAqB,CAACT,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,EAAE;UAC3DJ,qBAAqB,CAACT,IAAI,CAACI,QAAQ,CAAC,CAACJ,IAAI,CAACa,WAAW,CAAC,GAAG;YACvDK,EAAE,EAAElB,IAAI,CAACkB,EAAE,IAAI,IAAI;YACnBC,GAAG,EAAEnB,IAAI,CAACmB,GAAG,IAAI;WAClB;QACH;MACF;IACF,CAAC,CAAC;IAEF,OAAO;MACLrB,YAAY;MACZK,SAAS;MACTE,cAAc;MACdC,UAAU;MACVC,qBAAqB;MACrBC,qBAAqB;MACrBC;KACD;EACH;;;uCA9EWvB,WAAW,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXrC,WAAW;MAAAsC,OAAA,EAAXtC,WAAW,CAAAuC,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}