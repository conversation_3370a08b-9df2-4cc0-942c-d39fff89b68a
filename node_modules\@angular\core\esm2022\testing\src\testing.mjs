/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * Entry point for all public APIs of the core/testing package.
 */
export * from './async';
export { ComponentFixture } from './component_fixture';
export { resetFakeAsyncZone, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, tick, } from './fake_async';
export { TestBed, getTestBed, inject, InjectSetupWrapper, withModule, } from './test_bed';
export { TestComponentRenderer, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, } from './test_bed_common';
export * from './test_hooks';
export * from './metadata_override';
export { MetadataOverrider as ɵMetadataOverrider } from './metadata_overrider';
export { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState, } from '@angular/core';
export { DeferBlockFixture } from './defer';
//# sourceMappingURL=data:application/json;base64,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