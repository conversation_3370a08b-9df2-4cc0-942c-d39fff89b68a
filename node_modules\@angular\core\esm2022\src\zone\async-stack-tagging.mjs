/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export class AsyncStackTaggingZoneSpec {
    constructor(namePrefix, consoleAsyncStackTaggingImpl = console) {
        this.name = 'asyncStackTagging for ' + namePrefix;
        this.createTask = consoleAsyncStackTaggingImpl?.createTask ?? (() => null);
    }
    onScheduleTask(delegate, _current, target, task) {
        task.consoleTask = this.createTask(`Zone - ${task.source || task.type}`);
        return delegate.scheduleTask(target, task);
    }
    onInvokeTask(delegate, _currentZone, targetZone, task, applyThis, applyArgs) {
        let ret;
        if (task.consoleTask) {
            ret = task.consoleTask.run(() => delegate.invokeTask(targetZone, task, applyThis, applyArgs));
        }
        else {
            ret = delegate.invokeTask(targetZone, task, applyThis, applyArgs);
        }
        return ret;
    }
}
//# sourceMappingURL=data:application/json;base64,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