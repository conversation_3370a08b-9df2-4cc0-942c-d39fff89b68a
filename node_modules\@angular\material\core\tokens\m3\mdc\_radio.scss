@use 'sass:map';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, radio);

/// Generates the tokens for MDC radio
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC radio
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-utils.get-mdc-tokens('radio-button', $systems, $exclude-hardcoded);
  $variant-tokens: (
    primary: (), // Default, no overrides needed
    secondary: (
      selected-focus-icon-color: map.get($systems, md-sys-color, secondary),
      selected-focus-state-layer-color: map.get($systems, md-sys-color, secondary),
      selected-hover-icon-color: map.get($systems, md-sys-color, secondary),
      selected-hover-state-layer-color: map.get($systems, md-sys-color, secondary),
      selected-icon-color: map.get($systems, md-sys-color, secondary),
      selected-pressed-icon-color: map.get($systems, md-sys-color, secondary),
      unselected-pressed-state-layer-color: map.get($systems, md-sys-color, secondary),
    ),
    tertiary: (
      selected-focus-icon-color: map.get($systems, md-sys-color, tertiary),
      selected-focus-state-layer-color: map.get($systems, md-sys-color, tertiary),
      selected-hover-icon-color: map.get($systems, md-sys-color, tertiary),
      selected-hover-state-layer-color: map.get($systems, md-sys-color, tertiary),
      selected-icon-color: map.get($systems, md-sys-color, tertiary),
      selected-pressed-icon-color: map.get($systems, md-sys-color, tertiary),
      unselected-pressed-state-layer-color: map.get($systems, md-sys-color, tertiary),
    ),
    error: (
      selected-focus-icon-color: map.get($systems, md-sys-color, error),
      selected-focus-state-layer-color: map.get($systems, md-sys-color, error),
      selected-hover-icon-color: map.get($systems, md-sys-color, error),
      selected-hover-state-layer-color: map.get($systems, md-sys-color, error),
      selected-icon-color: map.get($systems, md-sys-color, error),
      selected-pressed-icon-color: map.get($systems, md-sys-color, error),
      unselected-pressed-state-layer-color: map.get($systems, md-sys-color, error),
    ),
  );

  @return token-utils.namespace-tokens($prefix, ($mdc-tokens, $variant-tokens), $token-slots);
}
