/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// Public API for compiler
export { Compiler, COMPILER_OPTIONS, CompilerFactory, ModuleWithComponentFactories, } from './linker/compiler';
export { ComponentFactory, ComponentRef } from './linker/component_factory';
export { ComponentFactoryResolver } from './linker/component_factory_resolver';
export { DestroyRef } from './linker/destroy_ref';
export { ElementRef } from './linker/element_ref';
export { NgModuleFactory, NgModuleRef } from './linker/ng_module_factory';
export { getModuleFactory, getNgModuleById } from './linker/ng_module_factory_loader';
export { QueryList } from './linker/query_list';
export { TemplateRef } from './linker/template_ref';
export { ViewContainerRef } from './linker/view_container_ref';
export { EmbeddedViewRef, ViewRef } from './linker/view_ref';
//# sourceMappingURL=data:application/json;base64,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