@use '../style/sass-utils';
@use '../theming/inspection';
@use '../tokens/m2-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    optgroup-label-text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    optgroup-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    optgroup-label-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    optgroup-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    optgroup-label-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
    optgroup-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
