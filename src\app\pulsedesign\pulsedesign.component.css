.pulse-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

h1 {
  color: white;
  font-size: 2.5rem;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  margin: 0 0 30px 0;
}

.matrix-card {
  margin: 20px auto;
  max-width: 95%;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.matrix-table th,
.matrix-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

/* Header Styles */
.test-name-header th {
  background-color: #3f51b5;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.sub-test-header th {
  background-color: #5c6bc0;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.batch-header {
  background-color: #3f51b5 !important;
  color: white !important;
  font-weight: bold !important;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  min-width: 80px;
}

/* Data Row Styles */
.data-row:nth-child(even) {
  background-color: #f5f5f5;
}

.data-row:nth-child(odd) {
  background-color: white;
}

.batch-cell {
  background-color: #e8eaf6;
  font-weight: 600;
  color: #3f51b5;
}

.value-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Footer Styles */
.footer-row {
  background-color: #f0f0f0;
  font-weight: 600;
}

.footer-label {
  background-color: #9e9e9e;
  color: white;
  font-weight: bold;
  text-align: center;
}

.unit-row {
  background-color: #e3f2fd;
}

.pp-row {
  background-color: #fff3e0;
}

.cp-row {
  background-color: #f3e5f5;
}

.footer-cell {
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

/* Loading Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: white;
}

.loading-container p {
  margin-top: 20px;
  font-size: 16px;
}

/* Debug and Error Styles */
.debug-info {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  margin: 10px 0;
  border-radius: 5px;
  color: #333;
}

.error-container {
  background-color: rgba(255, 0, 0, 0.1);
  color: white;
  padding: 20px;
  text-align: center;
  border-radius: 5px;
  margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .matrix-table {
    font-size: 10px;
  }

  .matrix-table th,
  .matrix-table td {
    padding: 4px;
  }

  h1 {
    font-size: 1.8rem;
  }
}
