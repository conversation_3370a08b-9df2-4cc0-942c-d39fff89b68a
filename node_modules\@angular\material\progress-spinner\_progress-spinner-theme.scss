@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mdc/circular-progress' as tokens-mdc-circular-progress;
@use '@material/circular-progress/circular-progress-theme' as mdc-circular-progress-theme;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-progress-spinner.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include mdc-circular-progress-theme.theme(
          tokens-mdc-circular-progress.get-unthemable-tokens()
      );
    }
  }
}

/// Outputs color theme styles for the mat-progress-spinner.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the spinner: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    $mdc-circular-progress-color-tokens: tokens-mdc-circular-progress.get-color-tokens($theme);

    @include sass-utils.current-selector-or-root() {
      @include mdc-circular-progress-theme.theme($mdc-circular-progress-color-tokens);

      .mat-accent {
        $color: inspection.get-theme-color($theme, accent);
        @include mdc-circular-progress-theme.theme((active-indicator-color: $color));
      }

      .mat-warn {
        $color: inspection.get-theme-color($theme, warn);
        @include mdc-circular-progress-theme.theme((active-indicator-color: $color));
      }
    }
  }
}

/// Outputs typography theme styles for the mat-progress-spinner.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
}

/// Outputs density theme styles for the mat-progress-spinner.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  $circular-progress-tokens: tokens-mdc-circular-progress.get-token-slots();

  @include token-utils.batch-create-token-values(
    $tokens,
    (prefix: tokens-mdc-circular-progress.$prefix, tokens: $circular-progress-tokens),
  );
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-progress-spinner.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the spinner: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-progress-spinner') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-circular-progress-tokens:
      token-utils.get-tokens-for($tokens, tokens-mdc-circular-progress.$prefix, $options...);
  @include mdc-circular-progress-theme.theme($mdc-circular-progress-tokens);
}
