@use 'sass:meta';
@use 'sass:map';
@use './mat/app' as tokens-mat-app;
@use './mat/autocomplete' as tokens-mat-autocomplete;
@use './mat/badge' as tokens-mat-badge;
@use './mat/text-button' as tokens-mat-text-button;
@use './mat/protected-button' as tokens-mat-protected-button;
@use './mat/filled-button' as tokens-mat-filled-button;
@use './mat/outlined-button' as tokens-mat-outlined-button;
@use './mat/dialog' as tokens-mat-dialog;
@use './mat/bottom-sheet' as tokens-mat-bottom-sheet;
@use './mat/card' as tokens-mat-card;
@use './mat/chip' as tokens-mat-chip;
@use './mat/datepicker' as tokens-mat-datepicker;
@use './mat/divider' as tokens-mat-divider;
@use './mat/expansion' as tokens-mat-expansion;
@use './mat/fab' as tokens-mat-fab;
@use './mat/fab-small' as tokens-mat-fab-small;
@use './mat/form-field' as tokens-mat-form-field;
@use './mat/grid-list' as tokens-mat-grid-list;
@use './mat/icon' as tokens-mat-icon;
@use './mat/icon-button' as tokens-mat-icon-button;
@use './mat/list' as tokens-mat-list;
@use './mat/menu' as tokens-mat-menu;
@use './mat/option' as tokens-mat-option;
@use './mat/optgroup' as tokens-mat-optgroup;
@use './mat/paginator' as tokens-mat-paginator;
@use './mat/checkbox' as tokens-mat-checkbox;
@use './mat/full-pseudo-checkbox' as tokens-mat-full-pseudo-checkbox;
@use './mat/minimal-pseudo-checkbox' as tokens-mat-minimal-pseudo-checkbox;
@use './mat/radio' as tokens-mat-radio;
@use './mat/ripple' as tokens-mat-ripple;
@use './mat/select' as tokens-mat-select;
@use './mat/sidenav' as tokens-mat-sidenav;
@use './mat/slider' as tokens-mat-slider;
@use './mat/switch' as tokens-mat-switch;
@use './mat/snack-bar' as tokens-mat-snack-bar;
@use './mat/sort' as tokens-mat-sort;
@use './mat/standard-button-toggle' as tokens-mat-button-toggle;
@use './mat/stepper' as tokens-mat-stepper;
@use './mat/tab-header' as tokens-mat-tab-header;
@use './mat/table' as tokens-mat-table;
@use './mat/toolbar' as tokens-mat-toolbar;
@use './mat/tree' as tokens-mat-tree;
@use './mdc/checkbox' as tokens-mdc-checkbox;
@use './mdc/text-button' as tokens-mdc-text-button;
@use './mdc/protected-button' as tokens-mdc-protected-button;
@use './mdc/filled-button' as tokens-mdc-filled-button;
@use './mdc/outlined-button' as tokens-mdc-outlined-button;
@use './mdc/chip' as tokens-mdc-chip;
@use './mdc/circular-progress' as tokens-mdc-circular-progress;
@use './mdc/dialog' as tokens-mdc-dialog;
@use './mdc/elevated-card' as tokens-mdc-elevated-card;
@use './mdc/extended-fab' as tokens-mdc-extended-fab;
@use './mdc/fab' as tokens-mdc-fab;
@use './mdc/fab-small' as tokens-mdc-fab-small;
@use './mdc/filled-text-field' as tokens-mdc-filled-text-field;
@use './mdc/icon-button' as tokens-mdc-icon-button;
@use './mdc/linear-progress' as tokens-mdc-linear-progress;
@use './mdc/list' as tokens-mdc-list;
@use './mdc/outlined-card' as tokens-mdc-outlined-card;
@use './mdc/outlined-text-field' as tokens-mdc-outlined-text-field;
@use './mdc/plain-tooltip' as tokens-mdc-plain-tooltip;
@use './mdc/radio' as tokens-mdc-radio;
@use './mdc/slider' as tokens-mdc-slider;
@use './mdc/snack-bar' as tokens-mdc-snack-bar;
@use './mdc/switch' as tokens-mdc-switch;
@use './mdc/tab' as tokens-mdc-tab;
@use './mdc/tab-indicator' as tokens-mdc-tab-indicator;

$_module-names: (
  // Custom tokens
  tokens-mat-app,
  tokens-mat-autocomplete,
  tokens-mat-badge,
  tokens-mat-bottom-sheet,
  tokens-mat-button-toggle,
  tokens-mat-card,
  tokens-mat-chip,
  tokens-mat-datepicker,
  tokens-mat-dialog,
  tokens-mat-divider,
  tokens-mat-expansion,
  tokens-mat-fab,
  tokens-mat-fab-small,
  tokens-mat-filled-button,
  tokens-mat-form-field,
  tokens-mat-grid-list,
  tokens-mat-icon-button,
  tokens-mat-icon,
  tokens-mat-menu,
  tokens-mat-optgroup,
  tokens-mat-option,
  tokens-mat-outlined-button,
  tokens-mat-paginator,
  tokens-mat-checkbox,
  tokens-mat-full-pseudo-checkbox,
  tokens-mat-list,
  tokens-mat-minimal-pseudo-checkbox,
  tokens-mat-protected-button,
  tokens-mat-radio,
  tokens-mat-ripple,
  tokens-mat-select,
  tokens-mat-sidenav,
  tokens-mat-slider,
  tokens-mat-switch,
  tokens-mat-snack-bar,
  tokens-mat-sort,
  tokens-mat-stepper,
  tokens-mat-tab-header,
  tokens-mat-table,
  tokens-mat-text-button,
  tokens-mat-toolbar,
  tokens-mat-tree,
  // MDC tokens
  tokens-mdc-checkbox,
  tokens-mdc-chip,
  tokens-mdc-text-button,
  tokens-mdc-protected-button,
  tokens-mdc-filled-button,
  tokens-mdc-outlined-button,
  tokens-mdc-chip,
  tokens-mdc-circular-progress,
  tokens-mdc-dialog,
  tokens-mdc-elevated-card,
  tokens-mdc-extended-fab,
  tokens-mdc-fab,
  tokens-mdc-fab-small,
  tokens-mdc-filled-text-field,
  tokens-mdc-icon-button,
  tokens-mdc-linear-progress,
  tokens-mdc-list,
  tokens-mdc-outlined-card,
  tokens-mdc-outlined-text-field,
  tokens-mdc-plain-tooltip,
  tokens-mdc-radio,
  tokens-mdc-slider,
  tokens-mdc-snack-bar,
  tokens-mdc-switch,
  tokens-mdc-tab,
  tokens-mdc-tab-indicator
);

/// Gets the full set of M3 tokens for the given theme object.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} Full set of M3 tokens
@function get-m3-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: ();

  @each $module-name in $_module-names {
    $fn: meta.get-function(
      $name: 'get-tokens',
      $module: $module-name,
    );
    $tokens: map.merge($tokens, meta.call($fn, $systems, $exclude-hardcoded, $token-slots));
  }

  @return $tokens;
}
