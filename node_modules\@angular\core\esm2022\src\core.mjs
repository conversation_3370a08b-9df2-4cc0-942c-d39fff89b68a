/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * Entry point from which you should import all public core APIs.
 */
export * from './authoring';
// Authoring functions are exported separately as this file is exempted from
// JSCompiler's conformance requirement for inferred const exports. See:
// https://docs.google.com/document/d/1RXb1wYwsbJotO1KBgSDsAtKpduGmIHod9ADxuXcAvV4/edit?tab=t.0
export { input } from './authoring/input/input';
export { contentChild, contentChildren, viewChild, viewChildren } from './authoring/queries';
export { model } from './authoring/model/model';
export * from './metadata';
export * from './version';
export * from './di';
export { ApplicationRef, NgProbeToken, APP_BOOTSTRAP_LISTENER, } from './application/application_ref';
export { PlatformRef } from './platform/platform_ref';
export { createPlatform, createPlatformFactory, assertPlatform, destroyPlatform, getPlatform, } from './platform/platform';
export { provideZoneChangeDetection, } from './change_detection/scheduling/ng_zone_scheduling';
export { provideExperimentalZonelessChangeDetection } from './change_detection/scheduling/zoneless_scheduling_impl';
export { ExperimentalPendingTasks } from './pending_tasks';
export { provideExperimentalCheckNoChangesForDebug } from './change_detection/scheduling/exhaustive_check_no_changes';
export { enableProdMode, isDevMode } from './util/is_dev_mode';
export { APP_ID, PACKAGE_ROOT_URL, PLATFORM_INITIALIZER, PLATFORM_ID, ANIMATION_MODULE_TYPE, CSP_NONCE, } from './application/application_tokens';
export { APP_INITIALIZER, ApplicationInitStatus } from './application/application_init';
export * from './zone';
export * from './render';
export * from './linker';
export * from './linker/ng_module_factory_loader_impl';
export { DebugElement, DebugEventListener, DebugNode, asNativeElements, getDebugNode, } from './debug/debug_node';
export { Testability, TestabilityRegistry, setTestabilityGetter, } from './testability/testability';
export * from './change_detection';
export * from './platform/platform_core_providers';
export { TRANSLATIONS, TRANSLATIONS_FORMAT, LOCALE_ID, DEFAULT_CURRENCY_CODE, MissingTranslationStrategy, } from './i18n/tokens';
export { ApplicationModule } from './application/application_module';
export { Type } from './interface/type';
export { EventEmitter } from './event_emitter';
export { ErrorHandler } from './error_handler';
export * from './core_private_export';
export * from './core_render3_private_export';
export * from './core_reactivity_export';
export { SecurityContext } from './sanitization/security';
export { Sanitizer } from './sanitization/sanitizer';
export { createNgModule, createNgModuleRef, createEnvironmentInjector, } from './render3/ng_module_ref';
export { createComponent, reflectComponentType } from './render3/component';
export { isStandalone } from './render3/definition';
export { AfterRenderPhase } from './render3/after_render/api';
export { afterRender, afterNextRender, } from './render3/after_render/hooks';
export { mergeApplicationConfig } from './application/application_config';
export { makeStateKey, TransferState } from './transfer_state';
export { booleanAttribute, numberAttribute } from './util/coercion';
import { global } from './util/global';
if (typeof ngDevMode !== 'undefined' && ngDevMode) {
    // This helper is to give a reasonable error message to people upgrading to v9 that have not yet
    // installed `@angular/localize` in their app.
    // tslint:disable-next-line: no-toplevel-property-access
    global.$localize ??= function () {
        throw new Error('It looks like your application or one of its dependencies is using i18n.\n' +
            'Angular 9 introduced a global `$localize()` function that needs to be loaded.\n' +
            'Please run `ng add @angular/localize` from the Angular CLI.\n' +
            "(For non-CLI projects, add `import '@angular/localize/init';` to your `polyfills.ts` file.\n" +
            'For server-side rendering applications add the import to your `main.server.ts` file.)');
    };
}
//# sourceMappingURL=data:application/json;base64,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