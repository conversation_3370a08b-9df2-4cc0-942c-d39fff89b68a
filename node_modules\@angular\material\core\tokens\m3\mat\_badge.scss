@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, badge);

/// Generates custom tokens for the mat-badge.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-badge
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: ((
    background-color: map.get($systems, md-sys-color, error),
    text-color: map.get($systems, md-sys-color, on-error),
    disabled-state-background-color: sass-utils.safe-color-change(
      map.get($systems, md-sys-color, error),
      $alpha: 0.38,
    ),
    disabled-state-text-color: map.get($systems, md-sys-color, on-error),
    text-font: map.get($systems, md-sys-typescale, label-small-font),
    text-size: map.get($systems, md-sys-typescale, label-small-size),
    text-weight: map.get($systems, md-sys-typescale, label-small-weight),
    small-size-text-size: token-utils.hardcode(0, $exclude-hardcoded),
    container-shape: map.get($systems, md-sys-shape, corner-full),
    container-size: token-utils.hardcode(16px, $exclude-hardcoded),
    legacy-container-size: token-utils.hardcode(unset, $exclude-hardcoded),
    legacy-small-size-container-size: token-utils.hardcode(unset, $exclude-hardcoded),
    small-size-container-size: token-utils.hardcode(6px, $exclude-hardcoded),
    container-padding: token-utils.hardcode(0 4px, $exclude-hardcoded),
    small-size-container-padding: token-utils.hardcode(0, $exclude-hardcoded),
    container-offset: token-utils.hardcode(-12px 0, $exclude-hardcoded),
    small-size-container-offset: token-utils.hardcode(-6px 0, $exclude-hardcoded),
    container-overlap-offset: token-utils.hardcode(-12px, $exclude-hardcoded),
    small-size-container-overlap-offset: token-utils.hardcode(-6px, $exclude-hardcoded),

    // This size isn't in the M3 spec so we emit the same values as for `medium`.
    large-size-container-size: token-utils.hardcode(16px, $exclude-hardcoded),
    large-size-container-offset: token-utils.hardcode(-12px 0, $exclude-hardcoded),
    large-size-container-overlap-offset: token-utils.hardcode(-12px, $exclude-hardcoded),
    large-size-text-size: map.get($systems, md-sys-typescale, label-small-size),
    large-size-container-padding: token-utils.hardcode(0 4px, $exclude-hardcoded),
    legacy-large-size-container-size: token-utils.hardcode(unset, $exclude-hardcoded),
  ), (
    primary: (
      background-color: map.get($systems, md-sys-color, primary),
      text-color: map.get($systems, md-sys-color, on-primary),
      disabled-state-background-color: sass-utils.safe-color-change(
        map.get($systems, md-sys-color, primary),
        $alpha: 0.38,
      ),
      disabled-state-text-color: map.get($systems, md-sys-color, on-primary),
    ),
    secondary: (
      background-color: map.get($systems, md-sys-color, secondary),
      text-color: map.get($systems, md-sys-color, on-secondary),
      disabled-state-background-color: sass-utils.safe-color-change(
        map.get($systems, md-sys-color, secondary),
        $alpha: 0.38,
      ),
      disabled-state-text-color: map.get($systems, md-sys-color, on-secondary),
    ),
    tertiary: (
      background-color: map.get($systems, md-sys-color, tertiary),
      text-color: map.get($systems, md-sys-color, on-tertiary),
      disabled-state-background-color: sass-utils.safe-color-change(
        map.get($systems, md-sys-color, tertiary),
        $alpha: 0.38,
      ),
      disabled-state-text-color: map.get($systems, md-sys-color, on-tertiary),
    ),
    error: () // Default, no overrides needed
  ));

  @return token-utils.namespace-tokens($prefix, $tokens, $token-slots);
}
