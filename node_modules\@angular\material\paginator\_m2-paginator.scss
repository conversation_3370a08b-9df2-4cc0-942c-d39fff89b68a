@use 'sass:math';
@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $foreground-base: inspection.get-theme-color($theme, foreground, base);

  @return (
    paginator-container-text-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.87),
    paginator-container-background-color: inspection.get-theme-color($theme, background, card),
    paginator-enabled-icon-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.54),
    paginator-disabled-icon-color: sass-utils.safe-color-change($foreground-base, $alpha: 0.12),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    paginator-container-text-font: inspection.get-theme-typography($theme, caption, font-family),
    paginator-container-text-line-height:
        inspection.get-theme-typography($theme, caption, line-height),
    paginator-container-text-size: inspection.get-theme-typography($theme, caption, font-size),
    paginator-container-text-tracking:
        inspection.get-theme-typography($theme, caption, letter-spacing),
    paginator-container-text-weight: inspection.get-theme-typography($theme, caption, font-weight),
    paginator-select-trigger-text-size: inspection.get-theme-typography($theme, caption, font-size),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -5);
  $size-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 40px,
    -4: 40px,
    -5: 40px,
  );
  $form-field-size-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
    -4: 40px,
    -5: 36px,
  );
  $form-field-density-scale: if($density-scale > -4, -4, $density-scale);
  $form-field-height: map.get($form-field-size-scale, $form-field-density-scale);

  // We computed the desired height of the form-field using the density configuration. The
  // spec only describes vertical spacing/alignment in non-dense mode. This means that we
  // cannot update the spacing to explicit numbers based on the density scale. Instead, we
  // determine the height reduction and equally subtract it from the default `top` and `bottom`
  // padding that is provided by the Material Design specification.
  $form-field-vertical-deduction: math.div(56px - $form-field-height, 2);
  $form-field-vertical-padding: 16px - $form-field-vertical-deduction;

  @return (
    paginator-container-size: map.get($size-scale, $density-scale),
    paginator-form-field-container-height: $form-field-height,
    paginator-form-field-container-vertical-padding: $form-field-vertical-padding,
    paginator-touch-target-display: if($density-scale < -2, none, block),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
