{"ast": null, "code": "import { Immediate } from '../util/Immediate';\nconst {\n  setImmediate,\n  clearImmediate\n} = Immediate;\nexport const immediateProvider = {\n  setImmediate(...args) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n  },\n  clearImmediate(handle) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Immediate", "setImmediate", "clearImmediate", "immediate<PERSON>rovider", "args", "delegate", "handle", "undefined"], "sources": ["C:/cal_wf/Augment/5-json-pulse/node_modules/rxjs/dist/esm/internal/scheduler/immediateProvider.js"], "sourcesContent": ["import { Immediate } from '../util/Immediate';\nconst { setImmediate, clearImmediate } = Immediate;\nexport const immediateProvider = {\n    setImmediate(...args) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n    },\n    clearImmediate(handle) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,MAAM;EAAEC,YAAY;EAAEC;AAAe,CAAC,GAAGF,SAAS;AAClD,OAAO,MAAMG,iBAAiB,GAAG;EAC7BF,YAAYA,CAAC,GAAGG,IAAI,EAAE;IAClB,MAAM;MAAEC;IAAS,CAAC,GAAGF,iBAAiB;IACtC,OAAO,CAAC,CAACE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACJ,YAAY,KAAKA,YAAY,EAAE,GAAGG,IAAI,CAAC;EACjH,CAAC;EACDF,cAAcA,CAACI,MAAM,EAAE;IACnB,MAAM;MAAED;IAAS,CAAC,GAAGF,iBAAiB;IACtC,OAAO,CAAC,CAACE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACH,cAAc,KAAKA,cAAc,EAAEI,MAAM,CAAC;EACpH,CAAC;EACDD,QAAQ,EAAEE;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}