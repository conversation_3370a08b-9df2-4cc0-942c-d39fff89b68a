/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import * as o from './output_ast';
export function mapEntry(key, value) {
    return { key, value, quoted: false };
}
export function mapLiteral(obj, quoted = false) {
    return o.literalMap(Object.keys(obj).map((key) => ({
        key,
        quoted,
        value: obj[key],
    })));
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibWFwX3V0aWwuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21waWxlci9zcmMvb3V0cHV0L21hcF91dGlsLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILE9BQU8sS0FBSyxDQUFDLE1BQU0sY0FBYyxDQUFDO0FBVWxDLE1BQU0sVUFBVSxRQUFRLENBQUMsR0FBVyxFQUFFLEtBQW1CO0lBQ3ZELE9BQU8sRUFBQyxHQUFHLEVBQUUsS0FBSyxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUMsQ0FBQztBQUNyQyxDQUFDO0FBRUQsTUFBTSxVQUFVLFVBQVUsQ0FDeEIsR0FBa0MsRUFDbEMsU0FBa0IsS0FBSztJQUV2QixPQUFPLENBQUMsQ0FBQyxVQUFVLENBQ2pCLE1BQU0sQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQyxDQUFDO1FBQzdCLEdBQUc7UUFDSCxNQUFNO1FBQ04sS0FBSyxFQUFFLEdBQUcsQ0FBQyxHQUFHLENBQUM7S0FDaEIsQ0FBQyxDQUFDLENBQ0osQ0FBQztBQUNKLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5kZXYvbGljZW5zZVxuICovXG5cbmltcG9ydCAqIGFzIG8gZnJvbSAnLi9vdXRwdXRfYXN0JztcblxuZXhwb3J0IHR5cGUgTWFwRW50cnkgPSB7XG4gIGtleTogc3RyaW5nO1xuICBxdW90ZWQ6IGJvb2xlYW47XG4gIHZhbHVlOiBvLkV4cHJlc3Npb247XG59O1xuXG5leHBvcnQgdHlwZSBNYXBMaXRlcmFsID0gTWFwRW50cnlbXTtcblxuZXhwb3J0IGZ1bmN0aW9uIG1hcEVudHJ5KGtleTogc3RyaW5nLCB2YWx1ZTogby5FeHByZXNzaW9uKTogTWFwRW50cnkge1xuICByZXR1cm4ge2tleSwgdmFsdWUsIHF1b3RlZDogZmFsc2V9O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gbWFwTGl0ZXJhbChcbiAgb2JqOiB7W2tleTogc3RyaW5nXTogby5FeHByZXNzaW9ufSxcbiAgcXVvdGVkOiBib29sZWFuID0gZmFsc2UsXG4pOiBvLkV4cHJlc3Npb24ge1xuICByZXR1cm4gby5saXRlcmFsTWFwKFxuICAgIE9iamVjdC5rZXlzKG9iaikubWFwKChrZXkpID0+ICh7XG4gICAgICBrZXksXG4gICAgICBxdW90ZWQsXG4gICAgICB2YWx1ZTogb2JqW2tleV0sXG4gICAgfSkpLFxuICApO1xufVxuIl19