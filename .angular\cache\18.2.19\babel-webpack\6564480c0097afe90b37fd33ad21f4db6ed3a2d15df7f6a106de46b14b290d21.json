{"ast": null, "code": "import { PulsedesignComponent } from './pulsedesign/pulsedesign.component';\nexport const routes = [{\n  path: '',\n  redirectTo: '/pulsedesign',\n  pathMatch: 'full'\n}, {\n  path: 'pulsedesign',\n  component: PulsedesignComponent\n}, {\n  path: '**',\n  redirectTo: '/pulsedesign'\n}];", "map": {"version": 3, "names": ["PulsedesignComponent", "routes", "path", "redirectTo", "pathMatch", "component"], "sources": ["C:\\cal_wf\\Augment\\5-json-pulse\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { PulsedesignComponent } from './pulsedesign/pulsedesign.component';\n\nexport const routes: Routes = [\n  { path: '', redirectTo: '/pulsedesign', pathMatch: 'full' },\n  { path: 'pulsedesign', component: PulsedesignComponent },\n  { path: '**', redirectTo: '/pulsedesign' }\n];\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,qCAAqC;AAE1E,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,cAAc;EAAEC,SAAS,EAAE;AAAM,CAAE,EAC3D;EAAEF,IAAI,EAAE,aAAa;EAAEG,SAAS,EAAEL;AAAoB,CAAE,EACxD;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAc,CAAE,CAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}