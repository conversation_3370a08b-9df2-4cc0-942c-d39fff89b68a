/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { RuntimeError } from '../errors';
import { assertDefined, assertEqual, assertNumber, throwError } from '../util/assert';
import { getComponentDef, getNgModuleDef } from './definition';
import { isLContainer, isLView } from './interfaces/type_checks';
import { DECLARATION_COMPONENT_VIEW, HEADER_OFFSET, T_HOST, TVIEW, } from './interfaces/view';
// [Assert functions do not constraint type when they are guarded by a truthy
// expression.](https://github.com/microsoft/TypeScript/issues/37295)
export function assertTNodeForLView(tNode, lView) {
    assertTNodeForTView(tNode, lView[TVIEW]);
}
export function assertTNodeForTView(tNode, tView) {
    assertTNode(tNode);
    const tData = tView.data;
    for (let i = HEADER_OFFSET; i < tData.length; i++) {
        if (tData[i] === tNode) {
            return;
        }
    }
    throwError('This TNode does not belong to this TView.');
}
export function assertTNode(tNode) {
    assertDefined(tNode, 'TNode must be defined');
    if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {
        throwError('Not of type TNode, got: ' + tNode);
    }
}
export function assertTIcu(tIcu) {
    assertDefined(tIcu, 'Expected TIcu to be defined');
    if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {
        throwError('Object is not of TIcu type.');
    }
}
export function assertComponentType(actual, msg = "Type passed in is not ComponentType, it does not have 'ɵcmp' property.") {
    if (!getComponentDef(actual)) {
        throwError(msg);
    }
}
export function assertNgModuleType(actual, msg = "Type passed in is not NgModuleType, it does not have 'ɵmod' property.") {
    if (!getNgModuleDef(actual)) {
        throwError(msg);
    }
}
export function assertCurrentTNodeIsParent(isParent) {
    assertEqual(isParent, true, 'currentTNode should be a parent');
}
export function assertHasParent(tNode) {
    assertDefined(tNode, 'currentTNode should exist!');
    assertDefined(tNode.parent, 'currentTNode should have a parent');
}
export function assertLContainer(value) {
    assertDefined(value, 'LContainer must be defined');
    assertEqual(isLContainer(value), true, 'Expecting LContainer');
}
export function assertLViewOrUndefined(value) {
    value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');
}
export function assertLView(value) {
    assertDefined(value, 'LView must be defined');
    assertEqual(isLView(value), true, 'Expecting LView');
}
export function assertFirstCreatePass(tView, errMessage) {
    assertEqual(tView.firstCreatePass, true, errMessage || 'Should only be called in first create pass.');
}
export function assertFirstUpdatePass(tView, errMessage) {
    assertEqual(tView.firstUpdatePass, true, errMessage || 'Should only be called in first update pass.');
}
/**
 * This is a basic sanity check that an object is probably a directive def. DirectiveDef is
 * an interface, so we can't do a direct instanceof check.
 */
export function assertDirectiveDef(obj) {
    if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {
        throwError(`Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`);
    }
}
export function assertIndexInDeclRange(tView, index) {
    assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);
}
export function assertIndexInExpandoRange(lView, index) {
    const tView = lView[1];
    assertBetween(tView.expandoStartIndex, lView.length, index);
}
export function assertBetween(lower, upper, index) {
    if (!(lower <= index && index < upper)) {
        throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);
    }
}
export function assertProjectionSlots(lView, errMessage) {
    assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');
    assertDefined(lView[DECLARATION_COMPONENT_VIEW][T_HOST].projection, errMessage ||
        'Components with projection nodes (<ng-content>) must have projection slots defined.');
}
export function assertParentView(lView, errMessage) {
    assertDefined(lView, errMessage || "Component views should always have a parent view (component's host view)");
}
export function assertNoDuplicateDirectives(directives) {
    // The array needs at least two elements in order to have duplicates.
    if (directives.length < 2) {
        return;
    }
    const seenDirectives = new Set();
    for (const current of directives) {
        if (seenDirectives.has(current)) {
            throw new RuntimeError(309 /* RuntimeErrorCode.DUPLICATE_DIRECTIVE */, `Directive ${current.type.name} matches multiple times on the same element. ` +
                `Directives can only match an element once.`);
        }
        seenDirectives.add(current);
    }
}
/**
 * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a
 * NodeInjector data structure.
 *
 * @param lView `LView` which should be checked.
 * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.
 */
export function assertNodeInjector(lView, injectorIndex) {
    assertIndexInExpandoRange(lView, injectorIndex);
    assertIndexInExpandoRange(lView, injectorIndex + 8 /* NodeInjectorOffset.PARENT */);
    assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');
    assertNumber(lView[injectorIndex + 8 /* NodeInjectorOffset.PARENT */], 'injectorIndex should point to parent injector');
}
//# sourceMappingURL=data:application/json;base64,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